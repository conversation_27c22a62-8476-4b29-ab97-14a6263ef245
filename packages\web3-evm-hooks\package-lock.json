{"name": "@airlyft/web3-evm-hooks", "version": "1.0.0", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "@airlyft/web3-evm-hooks", "version": "1.0.0", "dependencies": {"@ethersproject/providers": "^5.7.2", "@metamask/detect-provider": "^2.0.0", "@polkadot/extension-dapp": "^0.44.6", "@walletconnect/ethereum-provider": "^2.8.2", "@walletconnect/modal": "^2.5.2", "ethers": "^5.7.2", "zustand": "^4.1.4"}, "devDependencies": {"react": "file:../public-ui/node_modules/react"}, "peerDependencies": {"@types/react": "^18.2.6", "ethers": "^5.7.2", "react": "^18.2.0"}}, "../account-ui/node_modules/react": {"version": "18.3.1", "extraneous": true, "license": "MIT", "dependencies": {"loose-envify": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "../public-ui/node_modules/react": {"version": "18.3.1", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "../types": {"name": "@airlyft/types", "version": "1.0.0", "extraneous": true, "devDependencies": {"@graphql-codegen/cli": "^2.12.0", "@graphql-codegen/typescript": "^2.7.3"}}, "../web3-evm": {"name": "@airlyft/web3-evm", "version": "1.0.0", "extraneous": true, "dependencies": {"@airlyft/types": "^1.0.0", "ethers": "^5.7.2"}, "peerDependencies": {"ethers": "^5.7.2"}}, "node_modules/@babel/runtime": {"version": "7.24.5", "resolved": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.24.5.tgz", "integrity": "sha512-Nms86NXrsaeU9vbBJKni6gXiEXZ4CVpYVzEjDH9Sb8vmZ3UljyA1GSOJl/6LGPO8EHLuSF9H+IxNXHPX8QHJ4g==", "dependencies": {"regenerator-runtime": "^0.14.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@ethersproject/abi": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/hash": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/@ethersproject/abstract-provider": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/networks": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/web": "^5.7.0"}}, "node_modules/@ethersproject/abstract-signer": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0"}}, "node_modules/@ethersproject/address": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/rlp": "^5.7.0"}}, "node_modules/@ethersproject/base64": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0"}}, "node_modules/@ethersproject/basex": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/properties": "^5.7.0"}}, "node_modules/@ethersproject/bignumber": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "bn.js": "^5.2.1"}}, "node_modules/@ethersproject/bytes": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@ethersproject/constants": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0"}}, "node_modules/@ethersproject/contracts": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abi": "^5.7.0", "@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/transactions": "^5.7.0"}}, "node_modules/@ethersproject/hash": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/base64": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/@ethersproject/hdnode": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/basex": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/pbkdf2": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/sha2": "^5.7.0", "@ethersproject/signing-key": "^5.7.0", "@ethersproject/strings": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/wordlists": "^5.7.0"}}, "node_modules/@ethersproject/json-wallets": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/hdnode": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/pbkdf2": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/random": "^5.7.0", "@ethersproject/strings": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "aes-js": "3.0.0", "scrypt-js": "3.0.1"}}, "node_modules/@ethersproject/json-wallets/node_modules/aes-js": {"version": "3.0.0", "license": "MIT"}, "node_modules/@ethersproject/keccak256": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "js-sha3": "0.8.0"}}, "node_modules/@ethersproject/logger": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT"}, "node_modules/@ethersproject/networks": {"version": "5.7.1", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@ethersproject/pbkdf2": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/sha2": "^5.7.0"}}, "node_modules/@ethersproject/properties": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/logger": "^5.7.0"}}, "node_modules/@ethersproject/providers": {"version": "5.7.2", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/base64": "^5.7.0", "@ethersproject/basex": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/hash": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/networks": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/random": "^5.7.0", "@ethersproject/rlp": "^5.7.0", "@ethersproject/sha2": "^5.7.0", "@ethersproject/strings": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/web": "^5.7.0", "bech32": "1.1.4", "ws": "7.4.6"}}, "node_modules/@ethersproject/random": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@ethersproject/rlp": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@ethersproject/sha2": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "hash.js": "1.1.7"}}, "node_modules/@ethersproject/signing-key": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "bn.js": "^5.2.1", "elliptic": "6.5.4", "hash.js": "1.1.7"}}, "node_modules/@ethersproject/solidity": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/sha2": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/@ethersproject/strings": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@ethersproject/transactions": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/rlp": "^5.7.0", "@ethersproject/signing-key": "^5.7.0"}}, "node_modules/@ethersproject/units": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "node_modules/@ethersproject/wallet": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/hash": "^5.7.0", "@ethersproject/hdnode": "^5.7.0", "@ethersproject/json-wallets": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/random": "^5.7.0", "@ethersproject/signing-key": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/wordlists": "^5.7.0"}}, "node_modules/@ethersproject/web": {"version": "5.7.1", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/base64": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/@ethersproject/wordlists": {"version": "5.7.0", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/hash": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "node_modules/@lit-labs/ssr-dom-shim": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@lit-labs/ssr-dom-shim/-/ssr-dom-shim-1.1.1.tgz", "integrity": "sha512-kXOeFbfCm4fFf2A3WwVEeQj55tMZa8c8/f9AKHMobQMkzNUfUj+antR3fRPaZJawsa1aZiP/Da3ndpZrwEe4rQ=="}, "node_modules/@lit/reactive-element": {"version": "1.6.2", "resolved": "https://registry.npmjs.org/@lit/reactive-element/-/reactive-element-1.6.2.tgz", "integrity": "sha512-rDfl+QnCYjuIGf5xI2sVJWdYIi56CTCwWa+nidKYX6oIuBYwUbT/vX4qbUDlHiZKJ/3FRNQ/tWJui44p6/stSA==", "dependencies": {"@lit-labs/ssr-dom-shim": "^1.0.0"}}, "node_modules/@metamask/detect-provider": {"version": "2.0.0", "license": "ISC", "engines": {"node": ">=14.0.0"}}, "node_modules/@motionone/animation": {"version": "10.15.1", "resolved": "https://registry.npmjs.org/@motionone/animation/-/animation-10.15.1.tgz", "integrity": "sha512-mZcJxLjHor+bhcPuIFErMDNyrdb2vJur8lSfMCsuCB4UyV8ILZLvK+t+pg56erv8ud9xQGK/1OGPt10agPrCyQ==", "dependencies": {"@motionone/easing": "^10.15.1", "@motionone/types": "^10.15.1", "@motionone/utils": "^10.15.1", "tslib": "^2.3.1"}}, "node_modules/@motionone/dom": {"version": "10.16.2", "resolved": "https://registry.npmjs.org/@motionone/dom/-/dom-10.16.2.tgz", "integrity": "sha512-bnuHdNbge1FutZXv+k7xub9oPWcF0hsu8y1HTH/qg6av58YI0VufZ3ngfC7p2xhMJMnoh0LXFma2EGTgPeCkeg==", "dependencies": {"@motionone/animation": "^10.15.1", "@motionone/generators": "^10.15.1", "@motionone/types": "^10.15.1", "@motionone/utils": "^10.15.1", "hey-listen": "^1.0.8", "tslib": "^2.3.1"}}, "node_modules/@motionone/easing": {"version": "10.15.1", "resolved": "https://registry.npmjs.org/@motionone/easing/-/easing-10.15.1.tgz", "integrity": "sha512-6hIHBSV+ZVehf9dcKZLT7p5PEKHGhDwky2k8RKkmOvUoYP3S+dXsKupyZpqx5apjd9f+php4vXk4LuS+ADsrWw==", "dependencies": {"@motionone/utils": "^10.15.1", "tslib": "^2.3.1"}}, "node_modules/@motionone/generators": {"version": "10.15.1", "resolved": "https://registry.npmjs.org/@motionone/generators/-/generators-10.15.1.tgz", "integrity": "sha512-67HLsvHJbw6cIbLA/o+gsm7h+6D4Sn7AUrB/GPxvujse1cGZ38F5H7DzoH7PhX+sjvtDnt2IhFYF2Zp1QTMKWQ==", "dependencies": {"@motionone/types": "^10.15.1", "@motionone/utils": "^10.15.1", "tslib": "^2.3.1"}}, "node_modules/@motionone/svelte": {"version": "10.16.2", "resolved": "https://registry.npmjs.org/@motionone/svelte/-/svelte-10.16.2.tgz", "integrity": "sha512-38xsroKrfK+aHYhuQlE6eFcGy0EwrB43Q7RGjF73j/kRUTcLNu/LAaKiLLsN5lyqVzCgTBVt4TMT/ShWbTbc5Q==", "dependencies": {"@motionone/dom": "^10.16.2", "tslib": "^2.3.1"}}, "node_modules/@motionone/types": {"version": "10.15.1", "resolved": "https://registry.npmjs.org/@motionone/types/-/types-10.15.1.tgz", "integrity": "sha512-iIUd/EgUsRZGrvW0jqdst8st7zKTzS9EsKkP+6c6n4MPZoQHwiHuVtTQLD6Kp0bsBLhNzKIBlHXponn/SDT4hA=="}, "node_modules/@motionone/utils": {"version": "10.15.1", "resolved": "https://registry.npmjs.org/@motionone/utils/-/utils-10.15.1.tgz", "integrity": "sha512-p0YncgU+iklvYr/Dq4NobTRdAPv9PveRDUXabPEeOjBLSO/1FNB2phNTZxOxpi1/GZwYpAoECEa0Wam+nsmhSw==", "dependencies": {"@motionone/types": "^10.15.1", "hey-listen": "^1.0.8", "tslib": "^2.3.1"}}, "node_modules/@motionone/vue": {"version": "10.16.2", "resolved": "https://registry.npmjs.org/@motionone/vue/-/vue-10.16.2.tgz", "integrity": "sha512-7/dEK/nWQXOkJ70bqb2KyNfSWbNvWqKKq1C8juj+0Mg/AorgD8O5wE3naddK0G+aXuNMqRuc4jlsYHHWHtIzVw==", "dependencies": {"@motionone/dom": "^10.16.2", "tslib": "^2.3.1"}}, "node_modules/@noble/curves": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/@noble/curves/-/curves-1.4.0.tgz", "integrity": "sha512-p+4cb332SFCrReJkCYe8Xzm0OWi4Jji5jVdIZRL/PmacmDkFNw6MrrV+gGpiPxLHbV+zKFRywUWbaseT+tZRXg==", "peer": true, "dependencies": {"@noble/hashes": "1.4.0"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@noble/hashes": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/@noble/hashes/-/hashes-1.4.0.tgz", "integrity": "sha512-V1JJ1WTRUqHHrOSh597hURcMqVKVGL/ea3kv0gSnEdsEZ0/+VyPghM1lMNGc00z7CIQorSvbKpuJkxvuHbvdbg==", "peer": true, "engines": {"node": ">= 16"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@noble/secp256k1": {"version": "1.7.1", "resolved": "https://registry.npmjs.org/@noble/secp256k1/-/secp256k1-1.7.1.tgz", "integrity": "sha512-hOUk6AyBFmqVrv7k5WAw/LpszxVbj9gGN4JRkIX52fdFAj1UA61KXmZDvqVEm+pOyec3+fIeZB02LYa/pWOArw==", "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}]}, "node_modules/@polkadot-api/json-rpc-provider": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/@polkadot-api/json-rpc-provider/-/json-rpc-provider-0.0.1.tgz", "integrity": "sha512-/SMC/l7foRjpykLTUTacIH05H3mr9ip8b5xxfwXlVezXrNVLp3Cv0GX6uItkKd+ZjzVPf3PFrDF2B2/HLSNESA==", "optional": true, "peer": true}, "node_modules/@polkadot-api/json-rpc-provider-proxy": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/@polkadot-api/json-rpc-provider-proxy/-/json-rpc-provider-proxy-0.0.1.tgz", "integrity": "sha512-gmVDUP8LpCH0BXewbzqXF2sdHddq1H1q+XrAW2of+KZj4woQkIGBRGTJHeBEVHe30EB+UejR1N2dT4PO/RvDdg==", "optional": true, "peer": true}, "node_modules/@polkadot-api/metadata-builders": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/@polkadot-api/metadata-builders/-/metadata-builders-0.0.1.tgz", "integrity": "sha512-GCI78BHDzXAF/L2pZD6Aod/yl82adqQ7ftNmKg51ixRL02JpWUA+SpUKTJE5MY1p8kiJJIo09P2um24SiJHxNA==", "optional": true, "peer": true, "dependencies": {"@polkadot-api/substrate-bindings": "0.0.1", "@polkadot-api/utils": "0.0.1"}}, "node_modules/@polkadot-api/observable-client": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/@polkadot-api/observable-client/-/observable-client-0.1.0.tgz", "integrity": "sha512-GBCGDRztKorTLna/unjl/9SWZcRmvV58o9jwU2Y038VuPXZcr01jcw/1O3x+yeAuwyGzbucI/mLTDa1QoEml3A==", "optional": true, "peer": true, "dependencies": {"@polkadot-api/metadata-builders": "0.0.1", "@polkadot-api/substrate-bindings": "0.0.1", "@polkadot-api/substrate-client": "0.0.1", "@polkadot-api/utils": "0.0.1"}, "peerDependencies": {"rxjs": ">=7.8.0"}}, "node_modules/@polkadot-api/substrate-bindings": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/@polkadot-api/substrate-bindings/-/substrate-bindings-0.0.1.tgz", "integrity": "sha512-bAe7a5bOPnuFVmpv7y4BBMRpNTnMmE0jtTqRUw/+D8ZlEHNVEJQGr4wu3QQCl7k1GnSV1wfv3mzIbYjErEBocg==", "optional": true, "peer": true, "dependencies": {"@noble/hashes": "^1.3.1", "@polkadot-api/utils": "0.0.1", "@scure/base": "^1.1.1", "scale-ts": "^1.6.0"}}, "node_modules/@polkadot-api/substrate-client": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/@polkadot-api/substrate-client/-/substrate-client-0.0.1.tgz", "integrity": "sha512-9Bg9SGc3AwE+wXONQoW8GC00N3v6lCZLW74HQzqB6ROdcm5VAHM4CB/xRzWSUF9CXL78ugiwtHx3wBcpx4H4Wg==", "optional": true, "peer": true}, "node_modules/@polkadot-api/utils": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/@polkadot-api/utils/-/utils-0.0.1.tgz", "integrity": "sha512-3j+pRmlF9SgiYDabSdZsBSsN5XHbpXOAce1lWj56IEEaFZVjsiCaxDOA7C9nCcgfVXuvnbxqqEGQvnY+QfBAUw==", "optional": true, "peer": true}, "node_modules/@polkadot/api": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/api/-/api-11.0.3.tgz", "integrity": "sha512-VYuS42s5MiUOLo/PP1deYsddelGP4/mb1KFonXyWk69XowsfihGbVjsjh+DA4jPXvoNJqdGnDdu3SeppTY9MZQ==", "peer": true, "dependencies": {"@polkadot/api-augment": "11.0.3", "@polkadot/api-base": "11.0.3", "@polkadot/api-derive": "11.0.3", "@polkadot/keyring": "^12.6.2", "@polkadot/rpc-augment": "11.0.3", "@polkadot/rpc-core": "11.0.3", "@polkadot/rpc-provider": "11.0.3", "@polkadot/types": "11.0.3", "@polkadot/types-augment": "11.0.3", "@polkadot/types-codec": "11.0.3", "@polkadot/types-create": "11.0.3", "@polkadot/types-known": "11.0.3", "@polkadot/util": "^12.6.2", "@polkadot/util-crypto": "^12.6.2", "eventemitter3": "^5.0.1", "rxjs": "^7.8.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/api-augment": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/api-augment/-/api-augment-11.0.3.tgz", "integrity": "sha512-O1QEUXiHpPJqVe398EQ+tywZLj1vDNZALzh2TKsyFSqEjT4N/EschCck9aFXpB0Bp0K2lm7+fKJf6eWPShNfFQ==", "peer": true, "dependencies": {"@polkadot/api-base": "11.0.3", "@polkadot/rpc-augment": "11.0.3", "@polkadot/types": "11.0.3", "@polkadot/types-augment": "11.0.3", "@polkadot/types-codec": "11.0.3", "@polkadot/util": "^12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/api-base": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/api-base/-/api-base-11.0.3.tgz", "integrity": "sha512-e/KSsDcFIG17SPw+buBXAq5NnBOMMOWljTljazDF7Nq3Sz6P+SFqOSb3kdICtp1fYAxg5my/uTs2OOARxJRXKA==", "peer": true, "dependencies": {"@polkadot/rpc-core": "11.0.3", "@polkadot/types": "11.0.3", "@polkadot/util": "^12.6.2", "rxjs": "^7.8.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/api-derive": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/api-derive/-/api-derive-11.0.3.tgz", "integrity": "sha512-WJriPWX0WnDhHAETAYkgCxIhkKmnRPqSsBKUHZxq+GgMJByOA4wgo3gzq5aQLXgkcCLV3wM4a6hALodYz0urSw==", "peer": true, "dependencies": {"@polkadot/api": "11.0.3", "@polkadot/api-augment": "11.0.3", "@polkadot/api-base": "11.0.3", "@polkadot/rpc-core": "11.0.3", "@polkadot/types": "11.0.3", "@polkadot/types-codec": "11.0.3", "@polkadot/util": "^12.6.2", "@polkadot/util-crypto": "^12.6.2", "rxjs": "^7.8.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/extension-dapp": {"version": "0.44.9", "resolved": "https://registry.npmjs.org/@polkadot/extension-dapp/-/extension-dapp-0.44.9.tgz", "integrity": "sha512-xYY9bg4y2YW1ORWTflrPBypYueCpzajlYsU1CWuPP9fzKsdfd97wwa+dIYYvLbJy7tcivC+uIT3BpaFaJn2mXg==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/extension-inject": "^0.44.9", "@polkadot/util": "^10.4.2", "@polkadot/util-crypto": "^10.4.2"}, "peerDependencies": {"@polkadot/api": "*", "@polkadot/util": "*", "@polkadot/util-crypto": "*"}}, "node_modules/@polkadot/extension-dapp/node_modules/@noble/hashes": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@noble/hashes/-/hashes-1.2.0.tgz", "integrity": "sha512-FZfhjEDbT5GRswV3C6uvLPHMiVD6lQBmpoX5+eSiPaMTXte/IKqI5dykDxzZB/WBeK/CDuQRBWarPdi3FNY2zQ==", "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}]}, "node_modules/@polkadot/extension-dapp/node_modules/@polkadot/networks": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/networks/-/networks-10.4.2.tgz", "integrity": "sha512-FAh/znrEvWBiA/LbcT5GXHsCFUl//y9KqxLghSr/CreAmAergiJNT0MVUezC7Y36nkATgmsr4ylFwIxhVtuuCw==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/util": "10.4.2", "@substrate/ss58-registry": "^1.38.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@polkadot/extension-dapp/node_modules/@polkadot/util": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/util/-/util-10.4.2.tgz", "integrity": "sha512-0r5MGICYiaCdWnx+7Axlpvzisy/bi1wZGXgCSw5+ZTyPTOqvsYRqM2X879yxvMsGfibxzWqNzaiVjToz1jvUaA==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/x-bigint": "10.4.2", "@polkadot/x-global": "10.4.2", "@polkadot/x-textdecoder": "10.4.2", "@polkadot/x-textencoder": "10.4.2", "@types/bn.js": "^5.1.1", "bn.js": "^5.2.1"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@polkadot/extension-dapp/node_modules/@polkadot/util-crypto": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/util-crypto/-/util-crypto-10.4.2.tgz", "integrity": "sha512-RxZvF7C4+EF3fzQv8hZOLrYCBq5+wA+2LWv98nECkroChY3C2ZZvyWDqn8+aonNULt4dCVTWDZM0QIY6y4LUAQ==", "dependencies": {"@babel/runtime": "^7.20.13", "@noble/hashes": "1.2.0", "@noble/secp256k1": "1.7.1", "@polkadot/networks": "10.4.2", "@polkadot/util": "10.4.2", "@polkadot/wasm-crypto": "^6.4.1", "@polkadot/x-bigint": "10.4.2", "@polkadot/x-randomvalues": "10.4.2", "@scure/base": "1.1.1", "ed2curve": "^0.3.0", "tweetnacl": "^1.0.3"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"@polkadot/util": "10.4.2"}}, "node_modules/@polkadot/extension-dapp/node_modules/@polkadot/wasm-bridge": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-bridge/-/wasm-bridge-6.4.1.tgz", "integrity": "sha512-QZDvz6dsUlbYsaMV5biZgZWkYH9BC5AfhT0f0/knv8+LrbAoQdP3Asbvddw8vyU9sbpuCHXrd4bDLBwUCRfrBQ==", "dependencies": {"@babel/runtime": "^7.20.6"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/extension-dapp/node_modules/@polkadot/wasm-crypto": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto/-/wasm-crypto-6.4.1.tgz", "integrity": "sha512-FH+dcDPdhSLJvwL0pMLtn/LIPd62QDPODZRCmDyw+pFjLOMaRBc7raomWUOqyRWJTnqVf/iscc2rLVLNMyt7ag==", "dependencies": {"@babel/runtime": "^7.20.6", "@polkadot/wasm-bridge": "6.4.1", "@polkadot/wasm-crypto-asmjs": "6.4.1", "@polkadot/wasm-crypto-init": "6.4.1", "@polkadot/wasm-crypto-wasm": "6.4.1", "@polkadot/wasm-util": "6.4.1"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/extension-dapp/node_modules/@polkadot/wasm-crypto-asmjs": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto-asmjs/-/wasm-crypto-asmjs-6.4.1.tgz", "integrity": "sha512-UxZTwuBZlnODGIQdCsE2Sn/jU0O2xrNQ/TkhRFELfkZXEXTNu4lw6NpaKq7Iey4L+wKd8h4lT3VPVkMcPBLOvA==", "dependencies": {"@babel/runtime": "^7.20.6"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/extension-dapp/node_modules/@polkadot/wasm-crypto-init": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto-init/-/wasm-crypto-init-6.4.1.tgz", "integrity": "sha512-1ALagSi/nfkyFaH6JDYfy/QbicVbSn99K8PV9rctDUfxc7P06R7CoqbjGQ4OMPX6w1WYVPU7B4jPHGLYBlVuMw==", "dependencies": {"@babel/runtime": "^7.20.6", "@polkadot/wasm-bridge": "6.4.1", "@polkadot/wasm-crypto-asmjs": "6.4.1", "@polkadot/wasm-crypto-wasm": "6.4.1"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/extension-dapp/node_modules/@polkadot/wasm-crypto-wasm": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto-wasm/-/wasm-crypto-wasm-6.4.1.tgz", "integrity": "sha512-3VV9ZGzh0ZY3SmkkSw+0TRXxIpiO0nB8lFwlRgcwaCihwrvLfRnH9GI8WE12mKsHVjWTEVR3ogzILJxccAUjDA==", "dependencies": {"@babel/runtime": "^7.20.6", "@polkadot/wasm-util": "6.4.1"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/extension-dapp/node_modules/@polkadot/wasm-util": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-util/-/wasm-util-6.4.1.tgz", "integrity": "sha512-Uwo+WpEsDmFExWC5kTNvsVhvqXMZEKf4gUHXFn4c6Xz4lmieRT5g+1bO1KJ21pl4msuIgdV3Bksfs/oiqMFqlw==", "dependencies": {"@babel/runtime": "^7.20.6"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/extension-dapp/node_modules/@polkadot/x-bigint": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-bigint/-/x-bigint-10.4.2.tgz", "integrity": "sha512-awRiox+/XSReLzimAU94fPldowiwnnMUkQJe8AebYhNocAj6SJU00GNoj6j6tAho6yleOwrTJXZaWFBaQVJQNg==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/x-global": "10.4.2"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@polkadot/extension-dapp/node_modules/@polkadot/x-randomvalues": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-randomvalues/-/x-randomvalues-10.4.2.tgz", "integrity": "sha512-mf1Wbpe7pRZHO0V3V89isPLqZOy5XGX2bCqsfUWHgb1NvV1MMx5TjVjdaYyNlGTiOkAmJKlOHshcfPU2sYWpNg==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/x-global": "10.4.2"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@polkadot/extension-dapp/node_modules/@polkadot/x-textdecoder": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-textdecoder/-/x-textdecoder-10.4.2.tgz", "integrity": "sha512-d3ADduOKUTU+cliz839+KCFmi23pxTlabH7qh7Vs1GZQvXOELWdqFOqakdiAjtMn68n1KVF4O14Y+OUm7gp/zA==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/x-global": "10.4.2"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@polkadot/extension-dapp/node_modules/@polkadot/x-textencoder": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-textencoder/-/x-textencoder-10.4.2.tgz", "integrity": "sha512-mxcQuA1exnyv74Kasl5vxBq01QwckG088lYjc3KwmND6+pPrW2OWagbxFX5VFoDLDAE+UJtnUHsjdWyOTDhpQA==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/x-global": "10.4.2"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@polkadot/extension-dapp/node_modules/@scure/base": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@scure/base/-/base-1.1.1.tgz", "integrity": "sha512-ZxOhsSyxYwLJj3pLZCefNitxsj093tb2vq90mp2txoYeBqbcjDjqFhyM8eUjq/uFm6zJ+mUuqxlS2FkuSY1MTA==", "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}]}, "node_modules/@polkadot/extension-inject": {"version": "0.44.9", "resolved": "https://registry.npmjs.org/@polkadot/extension-inject/-/extension-inject-0.44.9.tgz", "integrity": "sha512-c23vp0C/8R5C3gdqoH2JRlKcvVjJFl9uM3t6rM/uwDs7GEQr9jrsmIOHGhNoI1/M/xBrCm/KuYNYi0dafdm/Vw==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/rpc-provider": "^9.14.2", "@polkadot/types": "^9.14.2", "@polkadot/util": "^10.4.2", "@polkadot/util-crypto": "^10.4.2", "@polkadot/x-global": "^10.4.2"}, "peerDependencies": {"@polkadot/api": "*"}}, "node_modules/@polkadot/extension-inject/node_modules/@noble/hashes": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@noble/hashes/-/hashes-1.2.0.tgz", "integrity": "sha512-FZfhjEDbT5GRswV3C6uvLPHMiVD6lQBmpoX5+eSiPaMTXte/IKqI5dykDxzZB/WBeK/CDuQRBWarPdi3FNY2zQ==", "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}]}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/keyring": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/keyring/-/keyring-10.4.2.tgz", "integrity": "sha512-7iHhJuXaHrRTG6cJDbZE9G+c1ts1dujp0qbO4RfAPmT7YUvphHvAtCKueN9UKPz5+TYDL+rP/jDEaSKU8jl/qQ==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/util": "10.4.2", "@polkadot/util-crypto": "10.4.2"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"@polkadot/util": "10.4.2", "@polkadot/util-crypto": "10.4.2"}}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/networks": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/networks/-/networks-10.4.2.tgz", "integrity": "sha512-FAh/znrEvWBiA/LbcT5GXHsCFUl//y9KqxLghSr/CreAmAergiJNT0MVUezC7Y36nkATgmsr4ylFwIxhVtuuCw==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/util": "10.4.2", "@substrate/ss58-registry": "^1.38.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/rpc-provider": {"version": "9.14.2", "resolved": "https://registry.npmjs.org/@polkadot/rpc-provider/-/rpc-provider-9.14.2.tgz", "integrity": "sha512-YTSywjD5PF01V47Ru5tln2LlpUwJiSOdz6rlJXPpMaY53hUp7+xMU01FVAQ1bllSBNisSD1Msv/mYHq84Oai2g==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/keyring": "^10.4.2", "@polkadot/types": "9.14.2", "@polkadot/types-support": "9.14.2", "@polkadot/util": "^10.4.2", "@polkadot/util-crypto": "^10.4.2", "@polkadot/x-fetch": "^10.4.2", "@polkadot/x-global": "^10.4.2", "@polkadot/x-ws": "^10.4.2", "eventemitter3": "^5.0.0", "mock-socket": "^9.2.1", "nock": "^13.3.0"}, "engines": {"node": ">=14.0.0"}, "optionalDependencies": {"@substrate/connect": "0.7.19"}}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/types": {"version": "9.14.2", "resolved": "https://registry.npmjs.org/@polkadot/types/-/types-9.14.2.tgz", "integrity": "sha512-hGLddTiJbvowhhUZJ3k+olmmBc1KAjWIQxujIUIYASih8FQ3/YJDKxaofGOzh0VygOKW3jxQBN2VZPofyDP9KQ==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/keyring": "^10.4.2", "@polkadot/types-augment": "9.14.2", "@polkadot/types-codec": "9.14.2", "@polkadot/types-create": "9.14.2", "@polkadot/util": "^10.4.2", "@polkadot/util-crypto": "^10.4.2", "rxjs": "^7.8.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/types-augment": {"version": "9.14.2", "resolved": "https://registry.npmjs.org/@polkadot/types-augment/-/types-augment-9.14.2.tgz", "integrity": "sha512-WO9d7RJufUeY3iFgt2Wz762kOu1tjEiGBR5TT4AHtpEchVHUeosVTrN9eycC+BhleqYu52CocKz6u3qCT/jKLg==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/types": "9.14.2", "@polkadot/types-codec": "9.14.2", "@polkadot/util": "^10.4.2"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/types-codec": {"version": "9.14.2", "resolved": "https://registry.npmjs.org/@polkadot/types-codec/-/types-codec-9.14.2.tgz", "integrity": "sha512-AJ4XF7W1no4PENLBRU955V6gDxJw0h++EN3YoDgThozZ0sj3OxyFupKgNBZcZb2V23H8JxQozzIad8k+nJbO1w==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/util": "^10.4.2", "@polkadot/x-bigint": "^10.4.2"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/types-create": {"version": "9.14.2", "resolved": "https://registry.npmjs.org/@polkadot/types-create/-/types-create-9.14.2.tgz", "integrity": "sha512-nSnKpBierlmGBQT8r6/SHf6uamBIzk4WmdMsAsR4uJKJF1PtbIqx2W5PY91xWSiMSNMzjkbCppHkwaDAMwLGaw==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/types-codec": "9.14.2", "@polkadot/util": "^10.4.2"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/types-support": {"version": "9.14.2", "resolved": "https://registry.npmjs.org/@polkadot/types-support/-/types-support-9.14.2.tgz", "integrity": "sha512-VWCOPgXDK3XtXT7wMLyIWeNDZxUbNcw/8Pn6n6vMogs7o/n4h6WGbGMeTIQhPWyn831/RmkVs5+2DUC+2LlOhw==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/util": "^10.4.2"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/util": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/util/-/util-10.4.2.tgz", "integrity": "sha512-0r5MGICYiaCdWnx+7Axlpvzisy/bi1wZGXgCSw5+ZTyPTOqvsYRqM2X879yxvMsGfibxzWqNzaiVjToz1jvUaA==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/x-bigint": "10.4.2", "@polkadot/x-global": "10.4.2", "@polkadot/x-textdecoder": "10.4.2", "@polkadot/x-textencoder": "10.4.2", "@types/bn.js": "^5.1.1", "bn.js": "^5.2.1"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/util-crypto": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/util-crypto/-/util-crypto-10.4.2.tgz", "integrity": "sha512-RxZvF7C4+EF3fzQv8hZOLrYCBq5+wA+2LWv98nECkroChY3C2ZZvyWDqn8+aonNULt4dCVTWDZM0QIY6y4LUAQ==", "dependencies": {"@babel/runtime": "^7.20.13", "@noble/hashes": "1.2.0", "@noble/secp256k1": "1.7.1", "@polkadot/networks": "10.4.2", "@polkadot/util": "10.4.2", "@polkadot/wasm-crypto": "^6.4.1", "@polkadot/x-bigint": "10.4.2", "@polkadot/x-randomvalues": "10.4.2", "@scure/base": "1.1.1", "ed2curve": "^0.3.0", "tweetnacl": "^1.0.3"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"@polkadot/util": "10.4.2"}}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/wasm-bridge": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-bridge/-/wasm-bridge-6.4.1.tgz", "integrity": "sha512-QZDvz6dsUlbYsaMV5biZgZWkYH9BC5AfhT0f0/knv8+LrbAoQdP3Asbvddw8vyU9sbpuCHXrd4bDLBwUCRfrBQ==", "dependencies": {"@babel/runtime": "^7.20.6"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/wasm-crypto": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto/-/wasm-crypto-6.4.1.tgz", "integrity": "sha512-FH+dcDPdhSLJvwL0pMLtn/LIPd62QDPODZRCmDyw+pFjLOMaRBc7raomWUOqyRWJTnqVf/iscc2rLVLNMyt7ag==", "dependencies": {"@babel/runtime": "^7.20.6", "@polkadot/wasm-bridge": "6.4.1", "@polkadot/wasm-crypto-asmjs": "6.4.1", "@polkadot/wasm-crypto-init": "6.4.1", "@polkadot/wasm-crypto-wasm": "6.4.1", "@polkadot/wasm-util": "6.4.1"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/wasm-crypto-asmjs": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto-asmjs/-/wasm-crypto-asmjs-6.4.1.tgz", "integrity": "sha512-UxZTwuBZlnODGIQdCsE2Sn/jU0O2xrNQ/TkhRFELfkZXEXTNu4lw6NpaKq7Iey4L+wKd8h4lT3VPVkMcPBLOvA==", "dependencies": {"@babel/runtime": "^7.20.6"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/wasm-crypto-init": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto-init/-/wasm-crypto-init-6.4.1.tgz", "integrity": "sha512-1ALagSi/nfkyFaH6JDYfy/QbicVbSn99K8PV9rctDUfxc7P06R7CoqbjGQ4OMPX6w1WYVPU7B4jPHGLYBlVuMw==", "dependencies": {"@babel/runtime": "^7.20.6", "@polkadot/wasm-bridge": "6.4.1", "@polkadot/wasm-crypto-asmjs": "6.4.1", "@polkadot/wasm-crypto-wasm": "6.4.1"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/wasm-crypto-wasm": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto-wasm/-/wasm-crypto-wasm-6.4.1.tgz", "integrity": "sha512-3VV9ZGzh0ZY3SmkkSw+0TRXxIpiO0nB8lFwlRgcwaCihwrvLfRnH9GI8WE12mKsHVjWTEVR3ogzILJxccAUjDA==", "dependencies": {"@babel/runtime": "^7.20.6", "@polkadot/wasm-util": "6.4.1"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/wasm-util": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-util/-/wasm-util-6.4.1.tgz", "integrity": "sha512-Uwo+WpEsDmFExWC5kTNvsVhvqXMZEKf4gUHXFn4c6Xz4lmieRT5g+1bO1KJ21pl4msuIgdV3Bksfs/oiqMFqlw==", "dependencies": {"@babel/runtime": "^7.20.6"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/x-bigint": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-bigint/-/x-bigint-10.4.2.tgz", "integrity": "sha512-awRiox+/XSReLzimAU94fPldowiwnnMUkQJe8AebYhNocAj6SJU00GNoj6j6tAho6yleOwrTJXZaWFBaQVJQNg==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/x-global": "10.4.2"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/x-fetch": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-fetch/-/x-fetch-10.4.2.tgz", "integrity": "sha512-Ubb64yaM4qwhogNP+4mZ3ibRghEg5UuCYRMNaCFoPgNAY8tQXuDKrHzeks3+frlmeH9YRd89o8wXLtWouwZIcw==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/x-global": "10.4.2", "@types/node-fetch": "^2.6.2", "node-fetch": "^3.3.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/x-randomvalues": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-randomvalues/-/x-randomvalues-10.4.2.tgz", "integrity": "sha512-mf1Wbpe7pRZHO0V3V89isPLqZOy5XGX2bCqsfUWHgb1NvV1MMx5TjVjdaYyNlGTiOkAmJKlOHshcfPU2sYWpNg==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/x-global": "10.4.2"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/x-textdecoder": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-textdecoder/-/x-textdecoder-10.4.2.tgz", "integrity": "sha512-d3ADduOKUTU+cliz839+KCFmi23pxTlabH7qh7Vs1GZQvXOELWdqFOqakdiAjtMn68n1KVF4O14Y+OUm7gp/zA==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/x-global": "10.4.2"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/x-textencoder": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-textencoder/-/x-textencoder-10.4.2.tgz", "integrity": "sha512-mxcQuA1exnyv74Kasl5vxBq01QwckG088lYjc3KwmND6+pPrW2OWagbxFX5VFoDLDAE+UJtnUHsjdWyOTDhpQA==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/x-global": "10.4.2"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@polkadot/extension-inject/node_modules/@polkadot/x-ws": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-ws/-/x-ws-10.4.2.tgz", "integrity": "sha512-3gHSTXAWQu1EMcMVTF5QDKHhEHzKxhAArweEyDXE7VsgKUP/ixxw4hVZBrkX122iI5l5mjSiooRSnp/Zl3xqDQ==", "dependencies": {"@babel/runtime": "^7.20.13", "@polkadot/x-global": "10.4.2", "@types/websocket": "^1.0.5", "websocket": "^1.0.34"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@polkadot/extension-inject/node_modules/@scure/base": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@scure/base/-/base-1.1.1.tgz", "integrity": "sha512-ZxOhsSyxYwLJj3pLZCefNitxsj093tb2vq90mp2txoYeBqbcjDjqFhyM8eUjq/uFm6zJ+mUuqxlS2FkuSY1MTA==", "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}]}, "node_modules/@polkadot/extension-inject/node_modules/@substrate/connect": {"version": "0.7.19", "resolved": "https://registry.npmjs.org/@substrate/connect/-/connect-0.7.19.tgz", "integrity": "sha512-+DDRadc466gCmDU71sHrYOt1HcI2Cbhm7zdCFjZfFVHXhC/E8tOdrVSglAH2HDEHR0x2SiHRxtxOGC7ak2Zjog==", "optional": true, "dependencies": {"@substrate/connect-extension-protocol": "^1.0.1", "@substrate/smoldot-light": "0.7.9", "eventemitter3": "^4.0.7"}}, "node_modules/@polkadot/extension-inject/node_modules/@substrate/connect-extension-protocol": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@substrate/connect-extension-protocol/-/connect-extension-protocol-1.0.1.tgz", "integrity": "sha512-161JhCC1csjH3GE5mPLEd7HbWtwNSPJBg3p1Ksz9SFlTzj/bgEwudiRN2y5i0MoLGCIJRYKyKGMxVnd29PzNjg==", "optional": true}, "node_modules/@polkadot/extension-inject/node_modules/@substrate/connect/node_modules/eventemitter3": {"version": "4.0.7", "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz", "integrity": "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==", "optional": true}, "node_modules/@polkadot/extension-inject/node_modules/node-fetch": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-3.3.2.tgz", "integrity": "sha512-dRB78srN/l6gqWulah9SrxeYnxeddIG30+GOqK/9OlLVyLg3HPnr6SqOWTWOXKRwC2eGYCkZ59NNuSgvSrpgOA==", "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}}, "node_modules/@polkadot/keyring": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/keyring/-/keyring-12.6.2.tgz", "integrity": "sha512-O3Q7GVmRYm8q7HuB3S0+Yf/q/EB2egKRRU3fv9b3B7V+A52tKzA+vIwEmNVaD1g5FKW9oB97rmpggs0zaKFqHw==", "peer": true, "dependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2"}}, "node_modules/@polkadot/networks": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/networks/-/networks-12.6.2.tgz", "integrity": "sha512-1oWtZm1IvPWqvMrldVH6NI2gBoCndl5GEwx7lAuQWGr7eNL+6Bdc5K3Z9T0MzFvDGoi2/CBqjX9dRKo39pDC/w==", "peer": true, "dependencies": {"@polkadot/util": "12.6.2", "@substrate/ss58-registry": "^1.44.0", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/rpc-augment": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/rpc-augment/-/rpc-augment-11.0.3.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON>EPwdRXMZmleKE0u/qVc20Ut+9O5mLoxKM6jCplg+aWvc51pbS2XOpXEpvWRIWWzCSpqzVc6WcMMYU6MTA==", "peer": true, "dependencies": {"@polkadot/rpc-core": "11.0.3", "@polkadot/types": "11.0.3", "@polkadot/types-codec": "11.0.3", "@polkadot/util": "^12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/rpc-core": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/rpc-core/-/rpc-core-11.0.3.tgz", "integrity": "sha512-DhMUoAVxRw1Cv49lkVQ889eLbrfXZbNCFBTEMyauNUQFZUyIDE8w/FLPiOsyhiXwUww5wTMWte9UnWxvBlppvQ==", "peer": true, "dependencies": {"@polkadot/rpc-augment": "11.0.3", "@polkadot/rpc-provider": "11.0.3", "@polkadot/types": "11.0.3", "@polkadot/util": "^12.6.2", "rxjs": "^7.8.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/rpc-provider": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/rpc-provider/-/rpc-provider-11.0.3.tgz", "integrity": "sha512-xgfHOF4y9jAD9aDDX/i9WUCRw8N6duRs0P/ysw2AafGKUGSBA/pqP/WXN61dQqq+DzZYfzL/FsMyTcUirsEkJA==", "peer": true, "dependencies": {"@polkadot/keyring": "^12.6.2", "@polkadot/types": "11.0.3", "@polkadot/types-support": "11.0.3", "@polkadot/util": "^12.6.2", "@polkadot/util-crypto": "^12.6.2", "@polkadot/x-fetch": "^12.6.2", "@polkadot/x-global": "^12.6.2", "@polkadot/x-ws": "^12.6.2", "eventemitter3": "^5.0.1", "mock-socket": "^9.3.1", "nock": "^13.5.0", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "optionalDependencies": {"@substrate/connect": "0.8.10"}}, "node_modules/@polkadot/rpc-provider/node_modules/@polkadot/x-global": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-global/-/x-global-12.6.2.tgz", "integrity": "sha512-a8d6m+PW98jmsYDtAWp88qS4dl8DyqUBsd0S+WgyfSMtpEXu6v9nXDgPZgwF5xdDvXhm+P0ZfVkVTnIGrScb5g==", "peer": true, "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/types": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/types/-/types-11.0.3.tgz", "integrity": "sha512-7AGUaObaGKZntOzHX4IRKj+K1AjZpYBgrivnY73tqGCp7biByn8Ht6a74xFngGfay1yRrx7eS3NOeIpp4nKhrw==", "peer": true, "dependencies": {"@polkadot/keyring": "^12.6.2", "@polkadot/types-augment": "11.0.3", "@polkadot/types-codec": "11.0.3", "@polkadot/types-create": "11.0.3", "@polkadot/util": "^12.6.2", "@polkadot/util-crypto": "^12.6.2", "rxjs": "^7.8.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/types-augment": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/types-augment/-/types-augment-11.0.3.tgz", "integrity": "sha512-/LXP/LDDr59ZigfeYXtYWrIr4qttYwlkEV7EMUnse5zYWAGH8mkras0Um7gX7kd/GndJAHvXAbbSdrduhg/uNA==", "peer": true, "dependencies": {"@polkadot/types": "11.0.3", "@polkadot/types-codec": "11.0.3", "@polkadot/util": "^12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/types-codec": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/types-codec/-/types-codec-11.0.3.tgz", "integrity": "sha512-WSYmacSfUnnspKpLbagng8zo84eXLOztCYSppmqLoCrwtQE0zg5P/jF18qIeozvmPh+I/HMordXKt34JPgI/6w==", "peer": true, "dependencies": {"@polkadot/util": "^12.6.2", "@polkadot/x-bigint": "^12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/types-create": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/types-create/-/types-create-11.0.3.tgz", "integrity": "sha512-/uK9BCaivd5kcqZZCeIOlN0pSFyIELk9VGVOM3HsitE6IQrOlYPrYe0RqrQhoU/kjNC5BHaWviYV3Hu3TlOJuw==", "peer": true, "dependencies": {"@polkadot/types-codec": "11.0.3", "@polkadot/util": "^12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/types-known": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/types-known/-/types-known-11.0.3.tgz", "integrity": "sha512-nuv/a32QYtZYCD7IMj+WKgcblgMJse2t3RuWO+ZtT/uscOmR7TjctkS2ayzSJDQ5wWCPGet1eIwYpJYfGFOPkw==", "peer": true, "dependencies": {"@polkadot/networks": "^12.6.2", "@polkadot/types": "11.0.3", "@polkadot/types-codec": "11.0.3", "@polkadot/types-create": "11.0.3", "@polkadot/util": "^12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/types-support": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/types-support/-/types-support-11.0.3.tgz", "integrity": "sha512-U9EG48zbbx5Q8B3GgHav8+/8hNYDOTijvSnEhUWsshh8U0Z7sNCmjfD4y0zfEzxoJyP7PTad62lJXfhQtehEsQ==", "peer": true, "dependencies": {"@polkadot/util": "^12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/util": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/util/-/util-12.6.2.tgz", "integrity": "sha512-l8TubR7CLEY47240uki0TQzFvtnxFIO7uI/0GoWzpYD/O62EIAMRsuY01N4DuwgKq2ZWD59WhzsLYmA5K6ksdw==", "peer": true, "dependencies": {"@polkadot/x-bigint": "12.6.2", "@polkadot/x-global": "12.6.2", "@polkadot/x-textdecoder": "12.6.2", "@polkadot/x-textencoder": "12.6.2", "@types/bn.js": "^5.1.5", "bn.js": "^5.2.1", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/util-crypto": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/util-crypto/-/util-crypto-12.6.2.tgz", "integrity": "sha512-FEWI/dJ7wDMNN1WOzZAjQoIcCP/3vz3wvAp5QQm+lOrzOLj0iDmaIGIcBkz8HVm3ErfSe/uKP0KS4jgV/ib+Mg==", "peer": true, "dependencies": {"@noble/curves": "^1.3.0", "@noble/hashes": "^1.3.3", "@polkadot/networks": "12.6.2", "@polkadot/util": "12.6.2", "@polkadot/wasm-crypto": "^7.3.2", "@polkadot/wasm-util": "^7.3.2", "@polkadot/x-bigint": "12.6.2", "@polkadot/x-randomvalues": "12.6.2", "@scure/base": "^1.1.5", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2"}}, "node_modules/@polkadot/util/node_modules/@polkadot/x-global": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-global/-/x-global-12.6.2.tgz", "integrity": "sha512-a8d6m+PW98jmsYDtAWp88qS4dl8DyqUBsd0S+WgyfSMtpEXu6v9nXDgPZgwF5xdDvXhm+P0ZfVkVTnIGrScb5g==", "peer": true, "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/wasm-bridge": {"version": "7.3.2", "resolved": "https://registry.npmjs.org/@polkadot/wasm-bridge/-/wasm-bridge-7.3.2.tgz", "integrity": "sha512-AJEXChcf/nKXd5Q/YLEV5dXQMle3UNT7jcXYmIffZAo/KI394a+/24PaISyQjoNC0fkzS1Q8T5pnGGHmXiVz2g==", "peer": true, "dependencies": {"@polkadot/wasm-util": "7.3.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/wasm-crypto": {"version": "7.3.2", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto/-/wasm-crypto-7.3.2.tgz", "integrity": "sha512-+neIDLSJ6jjVXsjyZ5oLSv16oIpwp+PxFqTUaZdZDoA2EyFRQB8pP7+qLsMNk+WJuhuJ4qXil/7XiOnZYZ+wxw==", "peer": true, "dependencies": {"@polkadot/wasm-bridge": "7.3.2", "@polkadot/wasm-crypto-asmjs": "7.3.2", "@polkadot/wasm-crypto-init": "7.3.2", "@polkadot/wasm-crypto-wasm": "7.3.2", "@polkadot/wasm-util": "7.3.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/wasm-crypto-asmjs": {"version": "7.3.2", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto-asmjs/-/wasm-crypto-asmjs-7.3.2.tgz", "integrity": "sha512-QP5eiUqUFur/2UoF2KKKYJcesc71fXhQFLT3D4ZjG28Mfk2ZPI0QNRUfpcxVQmIUpV5USHg4geCBNuCYsMm20Q==", "peer": true, "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/wasm-crypto-init": {"version": "7.3.2", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto-init/-/wasm-crypto-init-7.3.2.tgz", "integrity": "sha512-FPq73zGmvZtnuJaFV44brze3Lkrki3b4PebxCy9Fplw8nTmisKo9Xxtfew08r0njyYh+uiJRAxPCXadkC9sc8g==", "peer": true, "dependencies": {"@polkadot/wasm-bridge": "7.3.2", "@polkadot/wasm-crypto-asmjs": "7.3.2", "@polkadot/wasm-crypto-wasm": "7.3.2", "@polkadot/wasm-util": "7.3.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*", "@polkadot/x-randomvalues": "*"}}, "node_modules/@polkadot/wasm-crypto-wasm": {"version": "7.3.2", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto-wasm/-/wasm-crypto-wasm-7.3.2.tgz", "integrity": "sha512-15wd0EMv9IXs5Abp1ZKpKKAVyZPhATIAHfKsyoWCEFDLSOA0/K0QGOxzrAlsrdUkiKZOq7uzSIgIDgW8okx2Mw==", "peer": true, "dependencies": {"@polkadot/wasm-util": "7.3.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/wasm-util": {"version": "7.3.2", "resolved": "https://registry.npmjs.org/@polkadot/wasm-util/-/wasm-util-7.3.2.tgz", "integrity": "sha512-bmD+Dxo1lTZyZNxbyPE380wd82QsX+43mgCm40boyKrRppXEyQmWT98v/Poc7chLuskYb6X8IQ6lvvK2bGR4Tg==", "peer": true, "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "*"}}, "node_modules/@polkadot/x-bigint": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-bigint/-/x-bigint-12.6.2.tgz", "integrity": "sha512-HSIk60uFPX4GOFZSnIF7VYJz7WZA7tpFJsne7SzxOooRwMTWEtw3fUpFy5cYYOeLh17/kHH1Y7SVcuxzVLc74Q==", "peer": true, "dependencies": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/x-bigint/node_modules/@polkadot/x-global": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-global/-/x-global-12.6.2.tgz", "integrity": "sha512-a8d6m+PW98jmsYDtAWp88qS4dl8DyqUBsd0S+WgyfSMtpEXu6v9nXDgPZgwF5xdDvXhm+P0ZfVkVTnIGrScb5g==", "peer": true, "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/x-fetch": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-fetch/-/x-fetch-12.6.2.tgz", "integrity": "sha512-8wM/Z9JJPWN1pzSpU7XxTI1ldj/AfC8hKioBlUahZ8gUiJaOF7K9XEFCrCDLis/A1BoOu7Ne6WMx/vsJJIbDWw==", "peer": true, "dependencies": {"@polkadot/x-global": "12.6.2", "node-fetch": "^3.3.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/x-fetch/node_modules/@polkadot/x-global": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-global/-/x-global-12.6.2.tgz", "integrity": "sha512-a8d6m+PW98jmsYDtAWp88qS4dl8DyqUBsd0S+WgyfSMtpEXu6v9nXDgPZgwF5xdDvXhm+P0ZfVkVTnIGrScb5g==", "peer": true, "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/x-fetch/node_modules/node-fetch": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-3.3.2.tgz", "integrity": "sha512-dRB78srN/l6gqWulah9SrxeYnxeddIG30+GOqK/9OlLVyLg3HPnr6SqOWTWOXKRwC2eGYCkZ59NNuSgvSrpgOA==", "peer": true, "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}}, "node_modules/@polkadot/x-global": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-global/-/x-global-10.4.2.tgz", "integrity": "sha512-g6GXHD/ykZvHap3M6wh19dO70Zm43l4jEhlxf5LtTo5/0/UporFCXr2YJYZqfbn9JbQwl1AU+NroYio+vtJdiA==", "dependencies": {"@babel/runtime": "^7.20.13"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@polkadot/x-randomvalues": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-randomvalues/-/x-randomvalues-12.6.2.tgz", "integrity": "sha512-Vr8uG7rH2IcNJwtyf5ebdODMcr0XjoCpUbI91Zv6AlKVYOGKZlKLYJHIwpTaKKB+7KPWyQrk4Mlym/rS7v9feg==", "peer": true, "dependencies": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@polkadot/util": "12.6.2", "@polkadot/wasm-util": "*"}}, "node_modules/@polkadot/x-randomvalues/node_modules/@polkadot/x-global": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-global/-/x-global-12.6.2.tgz", "integrity": "sha512-a8d6m+PW98jmsYDtAWp88qS4dl8DyqUBsd0S+WgyfSMtpEXu6v9nXDgPZgwF5xdDvXhm+P0ZfVkVTnIGrScb5g==", "peer": true, "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/x-textdecoder": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-textdecoder/-/x-textdecoder-12.6.2.tgz", "integrity": "sha512-M1Bir7tYvNappfpFWXOJcnxUhBUFWkUFIdJSyH0zs5LmFtFdbKAeiDXxSp2Swp5ddOZdZgPac294/o2TnQKN1w==", "peer": true, "dependencies": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/x-textdecoder/node_modules/@polkadot/x-global": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-global/-/x-global-12.6.2.tgz", "integrity": "sha512-a8d6m+PW98jmsYDtAWp88qS4dl8DyqUBsd0S+WgyfSMtpEXu6v9nXDgPZgwF5xdDvXhm+P0ZfVkVTnIGrScb5g==", "peer": true, "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/x-textencoder": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-textencoder/-/x-textencoder-12.6.2.tgz", "integrity": "sha512-4N+3UVCpI489tUJ6cv3uf0PjOHvgGp9Dl+SZRLgFGt9mvxnvpW/7+XBADRMtlG4xi5gaRK7bgl5bmY6OMDsNdw==", "peer": true, "dependencies": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/x-textencoder/node_modules/@polkadot/x-global": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-global/-/x-global-12.6.2.tgz", "integrity": "sha512-a8d6m+PW98jmsYDtAWp88qS4dl8DyqUBsd0S+WgyfSMtpEXu6v9nXDgPZgwF5xdDvXhm+P0ZfVkVTnIGrScb5g==", "peer": true, "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/x-ws": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-ws/-/x-ws-12.6.2.tgz", "integrity": "sha512-cGZWo7K5eRRQCRl2LrcyCYsrc3lRbTlixZh3AzgU8uX4wASVGRlNWi/Hf4TtHNe1ExCDmxabJzdIsABIfrr7xw==", "peer": true, "dependencies": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2", "ws": "^8.15.1"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/x-ws/node_modules/@polkadot/x-global": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-global/-/x-global-12.6.2.tgz", "integrity": "sha512-a8d6m+PW98jmsYDtAWp88qS4dl8DyqUBsd0S+WgyfSMtpEXu6v9nXDgPZgwF5xdDvXhm+P0ZfVkVTnIGrScb5g==", "peer": true, "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18"}}, "node_modules/@polkadot/x-ws/node_modules/ws": {"version": "8.17.0", "resolved": "https://registry.npmjs.org/ws/-/ws-8.17.0.tgz", "integrity": "sha512-uJq6108EgZMAl20KagGkzCKfMEjxmKvZHG7Tlq0Z6nOky7YF7aq4mOx6xK8TJ/i1LeK4Qus7INktacctDgY8Ow==", "peer": true, "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/@scure/base": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/@scure/base/-/base-1.1.6.tgz", "integrity": "sha512-ok9AWwhcgYuGG3Zfhyqg+zwl+Wn5uE+dwC0NV/2qQkx4dABbb/bx96vWu8NSj+BNjjSjno+JRYRjle1jV08k3g==", "peer": true, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@stablelib/aead": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/aead/-/aead-1.0.1.tgz", "integrity": "sha512-q39ik6sxGHewqtO0nP4BuSe3db5G1fEJE8ukvngS2gLkBXyy6E7pLubhbYgnkDFv6V8cWaxcE4Xn0t6LWcJkyg=="}, "node_modules/@stablelib/binary": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/binary/-/binary-1.0.1.tgz", "integrity": "sha512-ClJWvmL6UBM/wjkvv/7m5VP3GMr9t0osr4yVgLZsLCOz4hGN9gIAFEqnJ0TsSMAN+n840nf2cHZnA5/KFqHC7Q==", "dependencies": {"@stablelib/int": "^1.0.1"}}, "node_modules/@stablelib/bytes": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/bytes/-/bytes-1.0.1.tgz", "integrity": "sha512-<PERSON>re4Y4kdwuqL8BR2E9hV/R5sOrUj6NanZaZis0V6lX5yzqC3hBuVSDXUIBqQv/sCpmuWRiHLwqiT1pqqjuBXoQ=="}, "node_modules/@stablelib/chacha": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/chacha/-/chacha-1.0.1.tgz", "integrity": "sha512-Pmlrswzr0pBzDofdFuVe1q7KdsHKhhU24e8gkEwnTGOmlC7PADzLVxGdn2PoNVBBabdg0l/IfLKg6sHAbTQugg==", "dependencies": {"@stablelib/binary": "^1.0.1", "@stablelib/wipe": "^1.0.1"}}, "node_modules/@stablelib/chacha20poly1305": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/chacha20poly1305/-/chacha20poly1305-1.0.1.tgz", "integrity": "sha512-MmViqnqHd1ymwjOQfghRKw2R/jMIGT3wySN7cthjXCBdO+qErNPUBnRzqNpnvIwg7JBCg3LdeCZZO4de/yEhVA==", "dependencies": {"@stablelib/aead": "^1.0.1", "@stablelib/binary": "^1.0.1", "@stablelib/chacha": "^1.0.1", "@stablelib/constant-time": "^1.0.1", "@stablelib/poly1305": "^1.0.1", "@stablelib/wipe": "^1.0.1"}}, "node_modules/@stablelib/constant-time": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/constant-time/-/constant-time-1.0.1.tgz", "integrity": "sha512-tNOs3uD0vSJcK6z1fvef4Y+buN7DXhzHDPqRLSXUel1UfqMB1PWNsnnAezrKfEwTLpN0cGH2p9NNjs6IqeD0eg=="}, "node_modules/@stablelib/ed25519": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/@stablelib/ed25519/-/ed25519-1.0.3.tgz", "integrity": "sha512-puIMWaX9QlRsbhxfDc5i+mNPMY+0TmQEskunY1rZEBPi1acBCVQAhnsk/1Hk50DGPtVsZtAWQg4NHGlVaO9Hqg==", "dependencies": {"@stablelib/random": "^1.0.2", "@stablelib/sha512": "^1.0.1", "@stablelib/wipe": "^1.0.1"}}, "node_modules/@stablelib/hash": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/hash/-/hash-1.0.1.tgz", "integrity": "sha512-eTPJc/stDkdtOcrNMZ6mcMK1e6yBbqRBaNW55XA1jU8w/7QdnCF0CmMmOD1m7VSkBR44PWrMHU2l6r8YEQHMgg=="}, "node_modules/@stablelib/hkdf": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/hkdf/-/hkdf-1.0.1.tgz", "integrity": "sha512-SBEHYE16ZXlHuaW5RcGk533YlBj4grMeg5TooN80W3NpcHRtLZLLXvKyX0qcRFxf+BGDobJLnwkvgEwHIDBR6g==", "dependencies": {"@stablelib/hash": "^1.0.1", "@stablelib/hmac": "^1.0.1", "@stablelib/wipe": "^1.0.1"}}, "node_modules/@stablelib/hmac": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/hmac/-/hmac-1.0.1.tgz", "integrity": "sha512-V2APD9NSnhVpV/QMYgCVMIYKiYG6LSqw1S65wxVoirhU/51ACio6D4yDVSwMzuTJXWZoVHbDdINioBwKy5kVmA==", "dependencies": {"@stablelib/constant-time": "^1.0.1", "@stablelib/hash": "^1.0.1", "@stablelib/wipe": "^1.0.1"}}, "node_modules/@stablelib/int": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/int/-/int-1.0.1.tgz", "integrity": "sha512-byr69X/sDtDiIjIV6m4roLVWnNNlRGzsvxw+agj8CIEazqWGOQp2dTYgQhtyVXV9wpO6WyXRQUzLV/JRNumT2w=="}, "node_modules/@stablelib/keyagreement": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/keyagreement/-/keyagreement-1.0.1.tgz", "integrity": "sha512-VKL6xBwgJnI6l1jKrBAfn265cspaWBPAPEc62VBQrWHLqVgNRE09gQ/AnOEyKUWrrqfD+xSQ3u42gJjLDdMDQg==", "dependencies": {"@stablelib/bytes": "^1.0.1"}}, "node_modules/@stablelib/poly1305": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/poly1305/-/poly1305-1.0.1.tgz", "integrity": "sha512-1HlG3oTSuQDOhSnLwJRKeTRSAdFNVB/1djy2ZbS35rBSJ/PFqx9cf9qatinWghC2UbfOYD8AcrtbUQl8WoxabA==", "dependencies": {"@stablelib/constant-time": "^1.0.1", "@stablelib/wipe": "^1.0.1"}}, "node_modules/@stablelib/random": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/@stablelib/random/-/random-1.0.2.tgz", "integrity": "sha512-rIsE83Xpb7clHPVRlBj8qNe5L8ISQOzjghYQm/dZ7VaM2KHYwMW5adjQjrzTZCchFnNCNhkwtnOBa9HTMJCI8w==", "dependencies": {"@stablelib/binary": "^1.0.1", "@stablelib/wipe": "^1.0.1"}}, "node_modules/@stablelib/sha256": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/sha256/-/sha256-1.0.1.tgz", "integrity": "sha512-GIIH3e6KH+91FqGV42Kcj71Uefd/QEe7Dy42sBTeqppXV95ggCcxLTk39bEr+lZfJmp+ghsR07J++ORkRELsBQ==", "dependencies": {"@stablelib/binary": "^1.0.1", "@stablelib/hash": "^1.0.1", "@stablelib/wipe": "^1.0.1"}}, "node_modules/@stablelib/sha512": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/sha512/-/sha512-1.0.1.tgz", "integrity": "sha512-13gl/iawHV9zvDKciLo1fQ8Bgn2Pvf7OV6amaRVKiq3pjQ3UmEpXxWiAfV8tYjUpeZroBxtyrwtdooQT/i3hzw==", "dependencies": {"@stablelib/binary": "^1.0.1", "@stablelib/hash": "^1.0.1", "@stablelib/wipe": "^1.0.1"}}, "node_modules/@stablelib/wipe": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/wipe/-/wipe-1.0.1.tgz", "integrity": "sha512-WfqfX/eXGiAd3RJe4VU2snh/ZPwtSjLG4ynQ/vYzvghTh7dHFcI1wl+nrkWG6lGhukOxOsUHfv8dUXr58D0ayg=="}, "node_modules/@stablelib/x25519": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/@stablelib/x25519/-/x25519-1.0.3.tgz", "integrity": "sha512-KnTbKmUhPhHavzobclVJQG5kuivH+qDLpe84iRqX3CLrKp881cF160JvXJ+hjn1aMyCwYOKeIZefIH/P5cJoRw==", "dependencies": {"@stablelib/keyagreement": "^1.0.1", "@stablelib/random": "^1.0.2", "@stablelib/wipe": "^1.0.1"}}, "node_modules/@substrate/connect": {"version": "0.8.10", "resolved": "https://registry.npmjs.org/@substrate/connect/-/connect-0.8.10.tgz", "integrity": "sha512-DIyQ13DDlXqVFnLV+S6/JDgiGowVRRrh18kahieJxhgvzcWicw5eLc6jpfQ0moVVLBYkO7rctB5Wreldwpva8w==", "optional": true, "peer": true, "dependencies": {"@substrate/connect-extension-protocol": "^2.0.0", "@substrate/connect-known-chains": "^1.1.4", "@substrate/light-client-extension-helpers": "^0.0.6", "smoldot": "2.0.22"}}, "node_modules/@substrate/connect-extension-protocol": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@substrate/connect-extension-protocol/-/connect-extension-protocol-2.0.0.tgz", "integrity": "sha512-nKu8pDrE3LNCEgJjZe1iGXzaD6OSIDD4Xzz/yo4KO9mQ6LBvf49BVrt4qxBFGL6++NneLiWUZGoh+VSd4PyVIg==", "optional": true, "peer": true}, "node_modules/@substrate/connect-known-chains": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/@substrate/connect-known-chains/-/connect-known-chains-1.1.4.tgz", "integrity": "sha512-iT+BdKqvKl/uBLd8BAJysFM1BaMZXRkaXBP2B7V7ob/EyNs5h0EMhTVbO6MJxV/IEOg5OKsyl6FUqQK7pKnqyw==", "optional": true, "peer": true}, "node_modules/@substrate/light-client-extension-helpers": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/@substrate/light-client-extension-helpers/-/light-client-extension-helpers-0.0.6.tgz", "integrity": "sha512-girltEuxQ1BvkJWmc8JJlk4ZxnlGXc/wkLcNguhY+UoDEMBK0LsdtfzQKIfrIehi4QdeSBlFEFBoI4RqPmsZzA==", "optional": true, "peer": true, "dependencies": {"@polkadot-api/json-rpc-provider": "0.0.1", "@polkadot-api/json-rpc-provider-proxy": "0.0.1", "@polkadot-api/observable-client": "0.1.0", "@polkadot-api/substrate-client": "0.0.1", "@substrate/connect-extension-protocol": "^2.0.0", "@substrate/connect-known-chains": "^1.1.4", "rxjs": "^7.8.1"}, "peerDependencies": {"smoldot": "2.x"}}, "node_modules/@substrate/smoldot-light": {"version": "0.7.9", "resolved": "https://registry.npmjs.org/@substrate/smoldot-light/-/smoldot-light-0.7.9.tgz", "integrity": "sha512-HP8iP7sFYlpSgjjbo0lqHyU+gu9lL2hbDNce6dWk5/10mFFF9jKIFGfui4zCecUY808o/Go9pan/31kMJoLbug==", "optional": true, "dependencies": {"pako": "^2.0.4", "ws": "^8.8.1"}}, "node_modules/@substrate/smoldot-light/node_modules/ws": {"version": "8.17.0", "resolved": "https://registry.npmjs.org/ws/-/ws-8.17.0.tgz", "integrity": "sha512-uJq6108EgZMAl20KagGkzCKfMEjxmKvZHG7Tlq0Z6nOky7YF7aq4mOx6xK8TJ/i1LeK4Qus7INktacctDgY8Ow==", "optional": true, "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/@substrate/ss58-registry": {"version": "1.47.0", "resolved": "https://registry.npmjs.org/@substrate/ss58-registry/-/ss58-registry-1.47.0.tgz", "integrity": "sha512-6kuIJedRcisUJS2pgksEH2jZf3hfSIVzqtFzs/AyjTW3ETbMg5q1Bb7VWa0WYaT6dTrEXp/6UoXM5B9pSIUmcw=="}, "node_modules/@types/bn.js": {"version": "5.1.5", "resolved": "https://registry.npmjs.org/@types/bn.js/-/bn.js-5.1.5.tgz", "integrity": "sha512-V46N0zwKRF5Q00AZ6hWtN0T8gGmDUaUzLWQvHFo5yThtVwK/VCenFY3wXVbOvNfajEpsTfQM4IN9k/d6gUVX3A==", "dependencies": {"@types/node": "*"}}, "node_modules/@types/node": {"version": "20.12.12", "resolved": "https://registry.npmjs.org/@types/node/-/node-20.12.12.tgz", "integrity": "sha512-eWLDGF/FOSPtAvEqeRAQ4C8LSA7M1I7i0ky1I8U7kD1J5ITyW3AsRhQrKVoWf5pFKZ2kILsEGJhsI9r93PYnOw==", "dependencies": {"undici-types": "~5.26.4"}}, "node_modules/@types/node-fetch": {"version": "2.6.11", "resolved": "https://registry.npmjs.org/@types/node-fetch/-/node-fetch-2.6.11.tgz", "integrity": "sha512-24xFj9R5+rfQJLRyM56qh+wnVSYhyXC2tkoBndtY0U+vubqNsYXGjufB2nn8Q6gt0LrARwL6UBtMCSVCwl4B1g==", "dependencies": {"@types/node": "*", "form-data": "^4.0.0"}}, "node_modules/@types/prop-types": {"version": "15.7.5", "license": "MIT", "peer": true}, "node_modules/@types/react": {"version": "18.2.6", "license": "MIT", "peer": true, "dependencies": {"@types/prop-types": "*", "@types/scheduler": "*", "csstype": "^3.0.2"}}, "node_modules/@types/scheduler": {"version": "0.16.2", "license": "MIT", "peer": true}, "node_modules/@types/trusted-types": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/@types/trusted-types/-/trusted-types-2.0.3.tgz", "integrity": "sha512-NfQ4gyz38SL8sDNrSixxU2Os1a5xcdFxipAFxYEuLUlvU2uDwS4NUpsImcf1//SlWItCVMMLiylsxbmNMToV/g=="}, "node_modules/@types/websocket": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/@types/websocket/-/websocket-1.0.10.tgz", "integrity": "sha512-svjGZvPB7EzuYS94cI7a+qhwgGU1y89wUgjT6E2wVUfmAGIvRfT7obBvRtnhXCSsoMdlG4gBFGE7MfkIXZLoww==", "dependencies": {"@types/node": "*"}}, "node_modules/@walletconnect/core": {"version": "2.8.2", "resolved": "https://registry.npmjs.org/@walletconnect/core/-/core-2.8.2.tgz", "integrity": "sha512-24ygQe1RIjcBQEh+I1KlhpLgKONrL0ll+2HIoLlSs/NLvsvNT7Ib2ku+ded8o82Pgji3DSSl5h0RNknkw2L5pQ==", "dependencies": {"@walletconnect/heartbeat": "1.2.1", "@walletconnect/jsonrpc-provider": "1.0.13", "@walletconnect/jsonrpc-types": "1.0.3", "@walletconnect/jsonrpc-utils": "1.0.8", "@walletconnect/jsonrpc-ws-connection": "^1.0.11", "@walletconnect/keyvaluestorage": "^1.0.2", "@walletconnect/logger": "^2.0.1", "@walletconnect/relay-api": "^1.0.9", "@walletconnect/relay-auth": "^1.0.4", "@walletconnect/safe-json": "^1.0.2", "@walletconnect/time": "^1.0.2", "@walletconnect/types": "2.8.2", "@walletconnect/utils": "2.8.2", "events": "^3.3.0", "lodash.isequal": "4.5.0", "uint8arrays": "^3.1.0"}}, "node_modules/@walletconnect/environment": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@walletconnect/environment/-/environment-1.0.1.tgz", "integrity": "sha512-T426LLZtHj8e8rYnKfzsw1aG6+M0BT1ZxayMdv/p8yM0MU+eJDISqNY3/bccxRr4LrF9csq02Rhqt08Ibl0VRg==", "dependencies": {"tslib": "1.14.1"}}, "node_modules/@walletconnect/environment/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@walletconnect/ethereum-provider": {"version": "2.8.2", "resolved": "https://registry.npmjs.org/@walletconnect/ethereum-provider/-/ethereum-provider-2.8.2.tgz", "integrity": "sha512-MWhjSSbbT60vYdcLuhmTFI6t/UYHIJzILeKuKQnv4j6Pt4X4SzG2uuL7eP9PL5nHBdZxMeA0mwGn/Yhs8mgcng==", "dependencies": {"@walletconnect/jsonrpc-http-connection": "^1.0.7", "@walletconnect/jsonrpc-provider": "^1.0.13", "@walletconnect/jsonrpc-types": "^1.0.3", "@walletconnect/jsonrpc-utils": "^1.0.8", "@walletconnect/sign-client": "2.8.2", "@walletconnect/types": "2.8.2", "@walletconnect/universal-provider": "2.8.2", "@walletconnect/utils": "2.8.2", "events": "^3.3.0"}, "peerDependencies": {"@walletconnect/modal": ">=2"}, "peerDependenciesMeta": {"@walletconnect/modal": {"optional": true}}}, "node_modules/@walletconnect/events": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@walletconnect/events/-/events-1.0.1.tgz", "integrity": "sha512-NPTqaoi0oPBVNuLv7qPaJazmGHs5JGyO8eEAk5VGKmJzDR7AHzD4k6ilox5kxk1iwiOnFopBOOMLs86Oa76HpQ==", "dependencies": {"keyvaluestorage-interface": "^1.0.0", "tslib": "1.14.1"}}, "node_modules/@walletconnect/events/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@walletconnect/heartbeat": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/@walletconnect/heartbeat/-/heartbeat-1.2.1.tgz", "integrity": "sha512-yVzws616xsDLJxuG/28FqtZ5rzrTA4gUjdEMTbWB5Y8V1XHRmqq4efAxCw5ie7WjbXFSUyBHaWlMR+2/CpQC5Q==", "dependencies": {"@walletconnect/events": "^1.0.1", "@walletconnect/time": "^1.0.2", "tslib": "1.14.1"}}, "node_modules/@walletconnect/heartbeat/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@walletconnect/jsonrpc-http-connection": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/@walletconnect/jsonrpc-http-connection/-/jsonrpc-http-connection-1.0.7.tgz", "integrity": "sha512-qlfh8fCfu8LOM9JRR9KE0s0wxP6ZG9/Jom8M0qsoIQeKF3Ni0FyV4V1qy/cc7nfI46SLQLSl4tgWSfLiE1swyQ==", "dependencies": {"@walletconnect/jsonrpc-utils": "^1.0.6", "@walletconnect/safe-json": "^1.0.1", "cross-fetch": "^3.1.4", "tslib": "1.14.1"}}, "node_modules/@walletconnect/jsonrpc-http-connection/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@walletconnect/jsonrpc-provider": {"version": "1.0.13", "resolved": "https://registry.npmjs.org/@walletconnect/jsonrpc-provider/-/jsonrpc-provider-1.0.13.tgz", "integrity": "sha512-K73EpThqHnSR26gOyNEL+acEex3P7VWZe6KE12ZwKzAt2H4e5gldZHbjsu2QR9cLeJ8AXuO7kEMOIcRv1QEc7g==", "dependencies": {"@walletconnect/jsonrpc-utils": "^1.0.8", "@walletconnect/safe-json": "^1.0.2", "tslib": "1.14.1"}}, "node_modules/@walletconnect/jsonrpc-provider/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@walletconnect/jsonrpc-types": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/@walletconnect/jsonrpc-types/-/jsonrpc-types-1.0.3.tgz", "integrity": "sha512-iIQ8hboBl3o5ufmJ8cuduGad0CQm3ZlsHtujv9Eu16xq89q+BG7Nh5VLxxUgmtpnrePgFkTwXirCTkwJH1v+Yw==", "dependencies": {"keyvaluestorage-interface": "^1.0.0", "tslib": "1.14.1"}}, "node_modules/@walletconnect/jsonrpc-types/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@walletconnect/jsonrpc-utils": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/@walletconnect/jsonrpc-utils/-/jsonrpc-utils-1.0.8.tgz", "integrity": "sha512-vdeb03bD8VzJUL6ZtzRYsFMq1eZQcM3EAzT0a3st59dyLfJ0wq+tKMpmGH7HlB7waD858UWgfIcudbPFsbzVdw==", "dependencies": {"@walletconnect/environment": "^1.0.1", "@walletconnect/jsonrpc-types": "^1.0.3", "tslib": "1.14.1"}}, "node_modules/@walletconnect/jsonrpc-utils/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@walletconnect/jsonrpc-ws-connection": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/@walletconnect/jsonrpc-ws-connection/-/jsonrpc-ws-connection-1.0.11.tgz", "integrity": "sha512-TiFJ6saasKXD+PwGkm5ZGSw0837nc6EeFmurSPgIT/NofnOV4Tv7CVJqGQN0rQYoJUSYu21cwHNYaFkzNpUN+w==", "dependencies": {"@walletconnect/jsonrpc-utils": "^1.0.6", "@walletconnect/safe-json": "^1.0.2", "events": "^3.3.0", "tslib": "1.14.1", "ws": "^7.5.1"}}, "node_modules/@walletconnect/jsonrpc-ws-connection/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@walletconnect/jsonrpc-ws-connection/node_modules/ws": {"version": "7.5.9", "resolved": "https://registry.npmjs.org/ws/-/ws-7.5.9.tgz", "integrity": "sha512-F+P9Jil7UiSKSkppIiD94dN07AwvFixvLIj1Og1Rl9GGMuNipJnV9JzjD6XuqmAeiswGvUmNLjr5cFuXwNS77Q==", "engines": {"node": ">=8.3.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/@walletconnect/keyvaluestorage": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/@walletconnect/keyvaluestorage/-/keyvaluestorage-1.0.2.tgz", "integrity": "sha512-U/nNG+VLWoPFdwwKx0oliT4ziKQCEoQ27L5Hhw8YOFGA2Po9A9pULUYNWhDgHkrb0gYDNt//X7wABcEWWBd3FQ==", "dependencies": {"safe-json-utils": "^1.1.1", "tslib": "1.14.1"}, "peerDependencies": {"@react-native-async-storage/async-storage": "1.x", "lokijs": "1.x"}, "peerDependenciesMeta": {"@react-native-async-storage/async-storage": {"optional": true}, "lokijs": {"optional": true}}}, "node_modules/@walletconnect/keyvaluestorage/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@walletconnect/logger": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/@walletconnect/logger/-/logger-2.0.1.tgz", "integrity": "sha512-SsTKdsgWm+oDTBeNE/zHxxr5eJfZmE9/5yp/Ku+zJtcTAjELb3DXueWkDXmE9h8uHIbJzIb5wj5lPdzyrjT6hQ==", "dependencies": {"pino": "7.11.0", "tslib": "1.14.1"}}, "node_modules/@walletconnect/logger/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@walletconnect/modal": {"version": "2.5.2", "resolved": "https://registry.npmjs.org/@walletconnect/modal/-/modal-2.5.2.tgz", "integrity": "sha512-vMLAQFjbMeXZ3+ojb+0OmMRpXCg92vCWJS2t3pF6XyxZrp/qxB9W87HwP7q6ecJtePM1Snil5QlpXipprqzr9g==", "dependencies": {"@walletconnect/modal-core": "2.5.2", "@walletconnect/modal-ui": "2.5.2"}}, "node_modules/@walletconnect/modal-core": {"version": "2.5.2", "resolved": "https://registry.npmjs.org/@walletconnect/modal-core/-/modal-core-2.5.2.tgz", "integrity": "sha512-meYjouZxAik0peyhxDUTRY77uu/r4tLe1QoJp/Ra3brHD0i93uwX5U8RlBNDLGQhLGIraZl6xNANcxHGRHFSuQ==", "dependencies": {"buffer": "6.0.3", "valtio": "1.10.5"}}, "node_modules/@walletconnect/modal-ui": {"version": "2.5.2", "resolved": "https://registry.npmjs.org/@walletconnect/modal-ui/-/modal-ui-2.5.2.tgz", "integrity": "sha512-ZbHsFP+LWvyJ3wwJf3nJKkwqMOHpJ5ECnAZgopMX+hp/bS+4JEeCzUy1StmzyriT6RImLFRQkI6Zas/NetaUnw==", "dependencies": {"@walletconnect/modal-core": "2.5.2", "lit": "2.7.5", "motion": "10.16.2", "qrcode": "1.5.3"}}, "node_modules/@walletconnect/relay-api": {"version": "1.0.9", "resolved": "https://registry.npmjs.org/@walletconnect/relay-api/-/relay-api-1.0.9.tgz", "integrity": "sha512-Q3+rylJOqRkO1D9Su0DPE3mmznbAalYapJ9qmzDgK28mYF9alcP3UwG/og5V7l7CFOqzCLi7B8BvcBUrpDj0Rg==", "dependencies": {"@walletconnect/jsonrpc-types": "^1.0.2", "tslib": "1.14.1"}}, "node_modules/@walletconnect/relay-api/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@walletconnect/relay-auth": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/@walletconnect/relay-auth/-/relay-auth-1.0.4.tgz", "integrity": "sha512-kKJcS6+WxYq5kshpPaxGHdwf5y98ZwbfuS4EE/NkQzqrDFm5Cj+dP8LofzWvjrrLkZq7Afy7WrQMXdLy8Sx7HQ==", "dependencies": {"@stablelib/ed25519": "^1.0.2", "@stablelib/random": "^1.0.1", "@walletconnect/safe-json": "^1.0.1", "@walletconnect/time": "^1.0.2", "tslib": "1.14.1", "uint8arrays": "^3.0.0"}}, "node_modules/@walletconnect/relay-auth/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@walletconnect/safe-json": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/@walletconnect/safe-json/-/safe-json-1.0.2.tgz", "integrity": "sha512-Ogb7I27kZ3LPC3ibn8ldyUr5544t3/STow9+lzz7Sfo808YD7SBWk7SAsdBFlYgP2zDRy2hS3sKRcuSRM0OTmA==", "dependencies": {"tslib": "1.14.1"}}, "node_modules/@walletconnect/safe-json/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@walletconnect/sign-client": {"version": "2.8.2", "resolved": "https://registry.npmjs.org/@walletconnect/sign-client/-/sign-client-2.8.2.tgz", "integrity": "sha512-TcViLWHE55SqYeFPDny1JTuktMOszffzYK5R22VAGOeHW3PhUqJoMcMXUEhSHuEeLcvGT1F25CiyNOWo2url/g==", "dependencies": {"@walletconnect/core": "2.8.2", "@walletconnect/events": "^1.0.1", "@walletconnect/heartbeat": "1.2.1", "@walletconnect/jsonrpc-utils": "1.0.8", "@walletconnect/logger": "^2.0.1", "@walletconnect/time": "^1.0.2", "@walletconnect/types": "2.8.2", "@walletconnect/utils": "2.8.2", "events": "^3.3.0"}}, "node_modules/@walletconnect/time": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/@walletconnect/time/-/time-1.0.2.tgz", "integrity": "sha512-uzdd9woDcJ1AaBZRhqy5rNC9laqWGErfc4dxA9a87mPdKOgWMD85mcFo9dIYIts/Jwocfwn07EC6EzclKubk/g==", "dependencies": {"tslib": "1.14.1"}}, "node_modules/@walletconnect/time/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@walletconnect/types": {"version": "2.8.2", "resolved": "https://registry.npmjs.org/@walletconnect/types/-/types-2.8.2.tgz", "integrity": "sha512-TzFGL2+SEU5jTt/i+kOZhcboqxhkDL+HaFcVl5+CVS6i67dYCjHu2AUkx6NARRmVzJZV5tTIjSDnpPXARoJaZA==", "dependencies": {"@walletconnect/events": "^1.0.1", "@walletconnect/heartbeat": "1.2.1", "@walletconnect/jsonrpc-types": "1.0.3", "@walletconnect/keyvaluestorage": "^1.0.2", "@walletconnect/logger": "^2.0.1", "events": "^3.3.0"}}, "node_modules/@walletconnect/universal-provider": {"version": "2.8.2", "resolved": "https://registry.npmjs.org/@walletconnect/universal-provider/-/universal-provider-2.8.2.tgz", "integrity": "sha512-BguG0gp5r3xq49+A201OAx81aqLI6Et0S7derGwBRN8BaNlSqlIY+/hRSnQohjt0Gy57ZikACI9nSNPWNl8nHw==", "dependencies": {"@walletconnect/jsonrpc-http-connection": "^1.0.7", "@walletconnect/jsonrpc-provider": "1.0.13", "@walletconnect/jsonrpc-types": "^1.0.2", "@walletconnect/jsonrpc-utils": "^1.0.7", "@walletconnect/logger": "^2.0.1", "@walletconnect/sign-client": "2.8.2", "@walletconnect/types": "2.8.2", "@walletconnect/utils": "2.8.2", "events": "^3.3.0"}}, "node_modules/@walletconnect/utils": {"version": "2.8.2", "resolved": "https://registry.npmjs.org/@walletconnect/utils/-/utils-2.8.2.tgz", "integrity": "sha512-VyOL1iuE7X7BorBlyB5t/FCZsFMihF5JO7gNjpharIZMRoIjiXv2SVKU+qbPT/LyrGswJ0Fkjia+hXUb3tGaWw==", "dependencies": {"@stablelib/chacha20poly1305": "1.0.1", "@stablelib/hkdf": "1.0.1", "@stablelib/random": "^1.0.2", "@stablelib/sha256": "1.0.1", "@stablelib/x25519": "^1.0.3", "@walletconnect/relay-api": "^1.0.9", "@walletconnect/safe-json": "^1.0.2", "@walletconnect/time": "^1.0.2", "@walletconnect/types": "2.8.2", "@walletconnect/window-getters": "^1.0.1", "@walletconnect/window-metadata": "^1.0.1", "detect-browser": "5.3.0", "query-string": "7.1.3", "uint8arrays": "^3.1.0"}}, "node_modules/@walletconnect/window-getters": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@walletconnect/window-getters/-/window-getters-1.0.1.tgz", "integrity": "sha512-vHp+HqzGxORPAN8gY03qnbTMnhqIwjeRJNOMOAzePRg4xVEEE2WvYsI9G2NMjOknA8hnuYbU3/hwLcKbjhc8+Q==", "dependencies": {"tslib": "1.14.1"}}, "node_modules/@walletconnect/window-getters/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@walletconnect/window-metadata": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@walletconnect/window-metadata/-/window-metadata-1.0.1.tgz", "integrity": "sha512-9koTqyGrM2cqFRW517BPY/iEtUDx2r1+Pwwu5m7sJ7ka79wi3EyqhqcICk/yDmv6jAS1rjKgTKXlEhanYjijcA==", "dependencies": {"@walletconnect/window-getters": "^1.0.1", "tslib": "1.14.1"}}, "node_modules/@walletconnect/window-metadata/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="}, "node_modules/atomic-sleep": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/atomic-sleep/-/atomic-sleep-1.0.0.tgz", "integrity": "sha512-kNOjDqAh7px0XWNI+4QbzoiR/nTkHAWNud2uvnJquD1/x5a7EQZMJT0AczqK0Qn67oY/TTQ1LbUKajZpp3I9tQ==", "engines": {"node": ">=8.0.0"}}, "node_modules/base64-js": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/bech32": {"version": "1.1.4", "license": "MIT"}, "node_modules/bn.js": {"version": "5.2.1", "license": "MIT"}, "node_modules/brorand": {"version": "1.1.0", "license": "MIT"}, "node_modules/buffer": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/buffer/-/buffer-6.0.3.tgz", "integrity": "sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/bufferutil": {"version": "4.0.7", "hasInstallScript": true, "license": "MIT", "dependencies": {"node-gyp-build": "^4.3.0"}, "engines": {"node": ">=6.14.2"}}, "node_modules/camelcase": {"version": "5.3.1", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz", "integrity": "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==", "engines": {"node": ">=6"}}, "node_modules/cliui": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/cliui/-/cliui-6.0.0.tgz", "integrity": "sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ==", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^6.2.0"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/cross-fetch": {"version": "3.1.6", "resolved": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.6.tgz", "integrity": "sha512-riRvo06crlE8HiqOwIpQhxwdOk4fOeR7FVM/wXoxchFEqMNUjvbs3bfo4OTgMEMHzppd4DxFBDbyySj8Cv781g==", "dependencies": {"node-fetch": "^2.6.11"}}, "node_modules/csstype": {"version": "3.1.1", "license": "MIT", "peer": true}, "node_modules/d": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/d/-/d-1.0.2.tgz", "integrity": "sha512-MOqHvMWF9/9MX6nza0KgvFH4HpMU0EF5uUDXqX/BtxtU8NfB0QzRtJ8Oe/6SuS4kbhyzVJwjd97EA4PKrzJ8bw==", "dependencies": {"es5-ext": "^0.10.64", "type": "^2.7.2"}, "engines": {"node": ">=0.12"}}, "node_modules/data-uri-to-buffer": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-4.0.1.tgz", "integrity": "sha512-0R9ikRb668HB7QDxT1vkpuUBtqc53YyAwMwGeUFKRojY/NWKvdZ+9UYtRfGmhqNbRkTSVpMbmyhXipFFv2cb/A==", "engines": {"node": ">= 12"}}, "node_modules/debug": {"version": "4.3.4", "resolved": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decamelize": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz", "integrity": "sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==", "engines": {"node": ">=0.10.0"}}, "node_modules/decode-uri-component": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/decode-uri-component/-/decode-uri-component-0.2.2.tgz", "integrity": "sha512-FqUYQ+8o158GyGTrMFJms9qh3CqTKvAqgqsTnkLI8sKu0028orqBhxNMFkFen0zGyg6epACD32pjVk58ngIErQ==", "engines": {"node": ">=0.10"}}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==", "engines": {"node": ">=0.4.0"}}, "node_modules/detect-browser": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/detect-browser/-/detect-browser-5.3.0.tgz", "integrity": "sha512-53rsFbGdwMwlF7qvCt0ypLM5V5/Mbl0szB7GPN8y9NCcbknYOeVVXdrXEq+90IwAfrrzt6Hd+u2E2ntakICU8w=="}, "node_modules/dijkstrajs": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/dijkstrajs/-/dijkstrajs-1.0.3.tgz", "integrity": "sha512-qiSlmBq9+BCdCA/L46dw8Uy93mloxsPSbwnm5yrKn2vMPiy8KyAskTF6zuV/j5BMsmOGZDPs7KjU+mjb670kfA=="}, "node_modules/duplexify": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/duplexify/-/duplexify-4.1.2.tgz", "integrity": "sha512-fz3OjcNCHmRP12MJoZMPglx8m4rrFP8rovnk4vT8Fs+aonZoCwGg10dSsQsfP/E62eZcPTMSMP6686fu9Qlqtw==", "dependencies": {"end-of-stream": "^1.4.1", "inherits": "^2.0.3", "readable-stream": "^3.1.1", "stream-shift": "^1.0.0"}}, "node_modules/ed2curve": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/ed2curve/-/ed2curve-0.3.0.tgz", "integrity": "sha512-8w2fmmq3hv9rCrcI7g9hms2pMunQr1JINfcjwR9tAyZqhtyaMN991lF/ZfHfr5tzZQ8c7y7aBgZbjfbd0fjFwQ==", "dependencies": {"tweetnacl": "1.x.x"}}, "node_modules/elliptic": {"version": "6.5.4", "license": "MIT", "dependencies": {"bn.js": "^4.11.9", "brorand": "^1.1.0", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.1", "inherits": "^2.0.4", "minimalistic-assert": "^1.0.1", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/elliptic/node_modules/bn.js": {"version": "4.12.0", "license": "MIT"}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="}, "node_modules/encode-utf8": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/encode-utf8/-/encode-utf8-1.0.3.tgz", "integrity": "sha512-ucAnuBEhUK4boH2HjVYG5Q2mQyPorvv0u/ocS+zhdw0S8AlHYY+GOFhP1Gio5z4icpP2ivFSvhtFjQi8+T9ppw=="}, "node_modules/end-of-stream": {"version": "1.4.4", "resolved": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz", "integrity": "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==", "dependencies": {"once": "^1.4.0"}}, "node_modules/es5-ext": {"version": "0.10.64", "resolved": "https://registry.npmjs.org/es5-ext/-/es5-ext-0.10.64.tgz", "integrity": "sha512-p2snDhiLaXe6dahss1LddxqEm+SkuDvV8dnIQG0MWjyHpcMNfXKPE+/Cc0y+PhxJX3A4xGNeFCj5oc0BUh6deg==", "hasInstallScript": true, "dependencies": {"es6-iterator": "^2.0.3", "es6-symbol": "^3.1.3", "esniff": "^2.0.1", "next-tick": "^1.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/es6-iterator": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/es6-iterator/-/es6-iterator-2.0.3.tgz", "integrity": "sha512-zw4SRzoUkd+cl+ZoE15A9o1oQd920Bb0iOJMQkQhl3jNc03YqVjAhG7scf9C5KWRU/R13Orf588uCC6525o02g==", "dependencies": {"d": "1", "es5-ext": "^0.10.35", "es6-symbol": "^3.1.1"}}, "node_modules/es6-symbol": {"version": "3.1.4", "resolved": "https://registry.npmjs.org/es6-symbol/-/es6-symbol-3.1.4.tgz", "integrity": "sha512-U9bFFjX8tFiATgtkJ1zg25+KviIXpgRvRHS8sau3GfhVzThRQrOeksPeT0BWW2MNZs1OEWJ1DPXOQMn0KKRkvg==", "dependencies": {"d": "^1.0.2", "ext": "^1.7.0"}, "engines": {"node": ">=0.12"}}, "node_modules/esniff": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/esniff/-/esniff-2.0.1.tgz", "integrity": "sha512-kTUIGKQ/mDPFoJ0oVfcmyJn4iBDRptjNVIzwIFR7tqWXdVI9xfA2RMwY/gbSpJG3lkdWNEjLap/NqVHZiJsdfg==", "dependencies": {"d": "^1.0.1", "es5-ext": "^0.10.62", "event-emitter": "^0.3.5", "type": "^2.7.2"}, "engines": {"node": ">=0.10"}}, "node_modules/ethers": {"version": "5.7.2", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@ethersproject/abi": "5.7.0", "@ethersproject/abstract-provider": "5.7.0", "@ethersproject/abstract-signer": "5.7.0", "@ethersproject/address": "5.7.0", "@ethersproject/base64": "5.7.0", "@ethersproject/basex": "5.7.0", "@ethersproject/bignumber": "5.7.0", "@ethersproject/bytes": "5.7.0", "@ethersproject/constants": "5.7.0", "@ethersproject/contracts": "5.7.0", "@ethersproject/hash": "5.7.0", "@ethersproject/hdnode": "5.7.0", "@ethersproject/json-wallets": "5.7.0", "@ethersproject/keccak256": "5.7.0", "@ethersproject/logger": "5.7.0", "@ethersproject/networks": "5.7.1", "@ethersproject/pbkdf2": "5.7.0", "@ethersproject/properties": "5.7.0", "@ethersproject/providers": "5.7.2", "@ethersproject/random": "5.7.0", "@ethersproject/rlp": "5.7.0", "@ethersproject/sha2": "5.7.0", "@ethersproject/signing-key": "5.7.0", "@ethersproject/solidity": "5.7.0", "@ethersproject/strings": "5.7.0", "@ethersproject/transactions": "5.7.0", "@ethersproject/units": "5.7.0", "@ethersproject/wallet": "5.7.0", "@ethersproject/web": "5.7.1", "@ethersproject/wordlists": "5.7.0"}}, "node_modules/event-emitter": {"version": "0.3.5", "resolved": "https://registry.npmjs.org/event-emitter/-/event-emitter-0.3.5.tgz", "integrity": "sha512-D9rRn9y7kLPnJ+hMq7S/nhvoKwwvVJahBi2BPmx3bvbsEdK3W9ii8cBSGjP+72/LnM4n6fo3+dkCX5FeTQruXA==", "dependencies": {"d": "1", "es5-ext": "~0.10.14"}}, "node_modules/eventemitter3": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-5.0.1.tgz", "integrity": "sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA=="}, "node_modules/events": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/events/-/events-3.3.0.tgz", "integrity": "sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==", "engines": {"node": ">=0.8.x"}}, "node_modules/ext": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/ext/-/ext-1.7.0.tgz", "integrity": "sha512-6hxeJYaL110a9b5TEJSj0gojyHQAmA2ch5Os+ySCiA1QGdS697XWY1pzsrSjqA9LDEEgdB/KypIlR59RcLuHYw==", "dependencies": {"type": "^2.7.2"}}, "node_modules/fast-redact": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/fast-redact/-/fast-redact-3.2.0.tgz", "integrity": "sha512-zaTadChr+NekyzallAMXATXLOR8MNx3zqpZ0MUF2aGf4EathnG0f32VLODNlY8IuGY3HoRO2L6/6fSzNsLaHIw==", "engines": {"node": ">=6"}}, "node_modules/fetch-blob": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz", "integrity": "sha512-7yAQpD2UMJzLi1Dqv7qFYnPbaPx7ZfFK6PiIxQ4PfkGPyNyl2Ugx+a/umUonmKqjhM4DnfbMvdX6otXq83soQQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "dependencies": {"node-domexception": "^1.0.0", "web-streams-polyfill": "^3.0.3"}, "engines": {"node": "^12.20 || >= 14.13"}}, "node_modules/filter-obj": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/filter-obj/-/filter-obj-1.1.0.tgz", "integrity": "sha512-8rXg1ZnX7xzy2NGDVkBVaAy+lSlPNwad13BtgSlLuxfIslyt5Vg64U7tFcCt4WS1R0hvtnQybT/IyCkGZ3DpXQ==", "engines": {"node": ">=0.10.0"}}, "node_modules/find-up": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz", "integrity": "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/form-data": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/form-data/-/form-data-4.0.0.tgz", "integrity": "sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/formdata-polyfill": {"version": "4.0.10", "resolved": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz", "integrity": "sha512-buewHzMvYL29jdeQTVILecSaZKnt/RJWjoZCF5OW60Z67/GmSLBkOFM7qh1PI3zFNtJbaZL5eQu1vLfazOwj4g==", "dependencies": {"fetch-blob": "^3.1.2"}, "engines": {"node": ">=12.20.0"}}, "node_modules/get-caller-file": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/hash.js": {"version": "1.1.7", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "node_modules/hey-listen": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/hey-listen/-/hey-listen-1.0.8.tgz", "integrity": "sha512-COpmrF2NOg4TBWUJ5UVyaCU2A88wEMkUPK4hNqyCkqHbxT92BbvfjoSozkAIIm6XhicGlJHhFdullInrdhwU8Q=="}, "node_modules/hmac-drbg": {"version": "1.0.1", "license": "MIT", "dependencies": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/ieee754": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/inherits": {"version": "2.0.4", "license": "ISC"}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "engines": {"node": ">=8"}}, "node_modules/is-typedarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz", "integrity": "sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA=="}, "node_modules/js-sha3": {"version": "0.8.0", "license": "MIT"}, "node_modules/json-stringify-safe": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "integrity": "sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA=="}, "node_modules/keyvaluestorage-interface": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/keyvaluestorage-interface/-/keyvaluestorage-interface-1.0.0.tgz", "integrity": "sha512-8t6Q3TclQ4uZynJY9IGr2+SsIGwK9JHcO6ootkHCGA0CrQCRy+VkouYNO2xicET6b9al7QKzpebNow+gkpCL8g=="}, "node_modules/lit": {"version": "2.7.5", "resolved": "https://registry.npmjs.org/lit/-/lit-2.7.5.tgz", "integrity": "sha512-i/cH7Ye6nBDUASMnfwcictBnsTN91+aBjXoTHF2xARghXScKxpD4F4WYI+VLXg9lqbMinDfvoI7VnZXjyHgdfQ==", "dependencies": {"@lit/reactive-element": "^1.6.0", "lit-element": "^3.3.0", "lit-html": "^2.7.0"}}, "node_modules/lit-element": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/lit-element/-/lit-element-3.3.2.tgz", "integrity": "sha512-xXAeVWKGr4/njq0rGC9dethMnYCq5hpKYrgQZYTzawt9YQhMiXfD+T1RgrdY3NamOxwq2aXlb0vOI6e29CKgVQ==", "dependencies": {"@lit-labs/ssr-dom-shim": "^1.1.0", "@lit/reactive-element": "^1.3.0", "lit-html": "^2.7.0"}}, "node_modules/lit-html": {"version": "2.7.4", "resolved": "https://registry.npmjs.org/lit-html/-/lit-html-2.7.4.tgz", "integrity": "sha512-/Jw+FBpeEN+z8X6PJva5n7+0MzCVAH2yypN99qHYYkq8bI+j7I39GH+68Z/MZD6rGKDK9RpzBw7CocfmHfq6+g==", "dependencies": {"@types/trusted-types": "^2.0.2"}}, "node_modules/locate-path": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/lodash.isequal": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/lodash.isequal/-/lodash.isequal-4.5.0.tgz", "integrity": "sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ=="}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/minimalistic-assert": {"version": "1.0.1", "license": "ISC"}, "node_modules/minimalistic-crypto-utils": {"version": "1.0.1", "license": "MIT"}, "node_modules/mock-socket": {"version": "9.3.1", "resolved": "https://registry.npmjs.org/mock-socket/-/mock-socket-9.3.1.tgz", "integrity": "sha512-qxBgB7Qa2sEQgHFjj0dSigq7fX4k6Saisd5Nelwp2q8mlbAFh5dHV9JTTlF8viYJLSSWgMCZFUom8PJcMNBoJw==", "engines": {"node": ">= 8"}}, "node_modules/motion": {"version": "10.16.2", "resolved": "https://registry.npmjs.org/motion/-/motion-10.16.2.tgz", "integrity": "sha512-p+PurYqfUdcJZvtnmAqu5fJgV2kR0uLFQuBKtLeFVTrYEVllI99tiOTSefVNYuip9ELTEkepIIDftNdze76NAQ==", "dependencies": {"@motionone/animation": "^10.15.1", "@motionone/dom": "^10.16.2", "@motionone/svelte": "^10.16.2", "@motionone/types": "^10.15.1", "@motionone/utils": "^10.15.1", "@motionone/vue": "^10.16.2"}}, "node_modules/ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}, "node_modules/multiformats": {"version": "9.9.0", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-9.9.0.tgz", "integrity": "sha512-HoMUjhH9T8DDBNT+6xzkrd9ga/XiBI4xLr58LJACwK6G3HTOPeMz4nB4KJs33L2BelrIJa7P0VuNaVF3hMYfjg=="}, "node_modules/next-tick": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/next-tick/-/next-tick-1.1.0.tgz", "integrity": "sha512-CXdUiJembsNjuToQvxayPZF9Vqht7hewsvy2sOWafLvi2awflj9mOC6bHIg50orX8IJvWKY9wYQ/zB2kogPslQ=="}, "node_modules/nock": {"version": "13.5.4", "resolved": "https://registry.npmjs.org/nock/-/nock-13.5.4.tgz", "integrity": "sha512-yAyTfdeNJGGBFxWdzSKCBYxs5FxLbCg5X5Q4ets974hcQzG1+qCxvIyOo4j2Ry6MUlhWVMX4OoYDefAIIwupjw==", "dependencies": {"debug": "^4.1.0", "json-stringify-safe": "^5.0.1", "propagate": "^2.0.0"}, "engines": {"node": ">= 10.13"}}, "node_modules/node-domexception": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/node-domexception/-/node-domexception-1.0.0.tgz", "integrity": "sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "github", "url": "https://paypal.me/jimmywarting"}], "engines": {"node": ">=10.5.0"}}, "node_modules/node-fetch": {"version": "2.6.11", "resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.6.11.tgz", "integrity": "sha512-4I6pdBY1EthSqDmJkiNk3JIT8cswwR9nfeW/cPdUagJYEQG7R95WRH74wpz7ma8Gh/9dI9FP+OU+0E4FvtA55w==", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/node-gyp-build": {"version": "4.5.0", "license": "MIT", "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}}, "node_modules/on-exit-leak-free": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/on-exit-leak-free/-/on-exit-leak-free-0.2.0.tgz", "integrity": "sha512-dqaz3u44QbRXQooZLTUKU41ZrzYrcvLISVgbrzbyCMxpmSLJvZ3ZamIJIZ29P6OhZIkNIQKosdeM6t1LYbA9hg=="}, "node_modules/once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "dependencies": {"wrappy": "1"}}, "node_modules/p-limit": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/p-try": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz", "integrity": "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==", "engines": {"node": ">=6"}}, "node_modules/pako": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/pako/-/pako-2.1.0.tgz", "integrity": "sha512-w+eufiZ1WuJYgPXbV/PO3NCMEc3xqylkKHzp8bxp1uW4qaSNQUkwmLLEc3kKsfz8lpV1F8Ht3U1Cm+9Srog2ug==", "optional": true}, "node_modules/path-exists": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==", "engines": {"node": ">=8"}}, "node_modules/pino": {"version": "7.11.0", "resolved": "https://registry.npmjs.org/pino/-/pino-7.11.0.tgz", "integrity": "sha512-dMACeu63HtRLmCG8VKdy4cShCPKaYDR4youZqoSWLxl5Gu99HUw8bw75thbPv9Nip+H+QYX8o3ZJbTdVZZ2TVg==", "dependencies": {"atomic-sleep": "^1.0.0", "fast-redact": "^3.0.0", "on-exit-leak-free": "^0.2.0", "pino-abstract-transport": "v0.5.0", "pino-std-serializers": "^4.0.0", "process-warning": "^1.0.0", "quick-format-unescaped": "^4.0.3", "real-require": "^0.1.0", "safe-stable-stringify": "^2.1.0", "sonic-boom": "^2.2.1", "thread-stream": "^0.15.1"}, "bin": {"pino": "bin.js"}}, "node_modules/pino-abstract-transport": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/pino-abstract-transport/-/pino-abstract-transport-0.5.0.tgz", "integrity": "sha512-+KAgmVeqXYbTtU2FScx1XS3kNyfZ5TrXY07V96QnUSFqo2gAqlvmaxH67Lj7SWazqsMabf+58ctdTcBgnOLUOQ==", "dependencies": {"duplexify": "^4.1.2", "split2": "^4.0.0"}}, "node_modules/pino-std-serializers": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/pino-std-serializers/-/pino-std-serializers-4.0.0.tgz", "integrity": "sha512-cK0pekc1Kjy5w9V2/n+8MkZwusa6EyyxfeQCB799CQRhRt/CqYKiWs5adeu8Shve2ZNffvfC/7J64A2PJo1W/Q=="}, "node_modules/pngjs": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/pngjs/-/pngjs-5.0.0.tgz", "integrity": "sha512-40QW5YalBNfQo5yRYmiw7Yz6TKKVr3h6970B2YE+3fQpsWcrbj1PzJgxeJ19DRQjhMbKPIuMY8rFaXc8moolVw==", "engines": {"node": ">=10.13.0"}}, "node_modules/process-warning": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/process-warning/-/process-warning-1.0.0.tgz", "integrity": "sha512-du4wfLyj4yCZq1VupnVSZmRsPJsNuxoDQFdCFHLaYiEbFBD7QE0a+I4D7hOxrVnh78QE/YipFAj9lXHiXocV+Q=="}, "node_modules/propagate": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/propagate/-/propagate-2.0.1.tgz", "integrity": "sha512-vGrhOavPSTz4QVNuBNdcNXePNdNMaO1xj9yBeH1ScQPjk/rhg9sSlCXPhMkFuaNNW/syTvYqsnbIJxMBfRbbag==", "engines": {"node": ">= 8"}}, "node_modules/proxy-compare": {"version": "2.5.1", "resolved": "https://registry.npmjs.org/proxy-compare/-/proxy-compare-2.5.1.tgz", "integrity": "sha512-oyfc0Tx87Cpwva5ZXezSp5V9vht1c7dZBhvuV/y3ctkgMVUmiAGDVeeB0dKhGSyT0v1ZTEQYpe/RXlBVBNuCLA=="}, "node_modules/qrcode": {"version": "1.5.3", "resolved": "https://registry.npmjs.org/qrcode/-/qrcode-1.5.3.tgz", "integrity": "sha512-puyri6ApkEHYiVl4CFzo1tDkAZ+ATcnbJrJ6RiBM1Fhctdn/ix9MTE3hRph33omisEbC/2fcfemsseiKgBPKZg==", "dependencies": {"dijkstrajs": "^1.0.1", "encode-utf8": "^1.0.3", "pngjs": "^5.0.0", "yargs": "^15.3.1"}, "bin": {"qrcode": "bin/qrcode"}, "engines": {"node": ">=10.13.0"}}, "node_modules/query-string": {"version": "7.1.3", "resolved": "https://registry.npmjs.org/query-string/-/query-string-7.1.3.tgz", "integrity": "sha512-hh2WYhq4fi8+b+/2Kg9CEge4fDPvHS534aOOvOZeQ3+Vf2mCFsaFBYj0i+iXcAq6I9Vzp5fjMFBlONvayDC1qg==", "dependencies": {"decode-uri-component": "^0.2.2", "filter-obj": "^1.1.0", "split-on-first": "^1.0.0", "strict-uri-encode": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/quick-format-unescaped": {"version": "4.0.4", "resolved": "https://registry.npmjs.org/quick-format-unescaped/-/quick-format-unescaped-4.0.4.tgz", "integrity": "sha512-tYC1Q1hgyRuHgloV/YXs2w15unPVh8qfu/qCTfhTYamaw7fyhumKa2yGpdSo87vY32rIclj+4fWYQXUMs9EHvg=="}, "node_modules/react": {"resolved": "../public-ui/node_modules/react", "link": true}, "node_modules/readable-stream": {"version": "3.6.2", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "integrity": "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/real-require": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/real-require/-/real-require-0.1.0.tgz", "integrity": "sha512-r/H9MzAWtrv8aSVjPCMFpDMl5q66GqtmmRkRjpHTsp4zBAa+snZyiQNlMONiUmEJcsnaw0wCauJ2GWODr/aFkg==", "engines": {"node": ">= 12.13.0"}}, "node_modules/regenerator-runtime": {"version": "0.14.1", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz", "integrity": "sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw=="}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==", "engines": {"node": ">=0.10.0"}}, "node_modules/require-main-filename": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/require-main-filename/-/require-main-filename-2.0.0.tgz", "integrity": "sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg=="}, "node_modules/rxjs": {"version": "7.8.1", "resolved": "https://registry.npmjs.org/rxjs/-/rxjs-7.8.1.tgz", "integrity": "sha512-AA3TVj+0A2iuIoQkWEK/tqFjBq2j+6PO6Y0zJcvzLAFhEFIO3HL0vls9hWLncZbAAbK0mar7oZ4V079I/qPMxg==", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/safe-json-utils": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/safe-json-utils/-/safe-json-utils-1.1.1.tgz", "integrity": "sha512-SAJWGKDs50tAbiDXLf89PDwt9XYkWyANFWVzn4dTXl5QyI8t2o/bW5/OJl3lvc2WVU4MEpTo9Yz5NVFNsp+OJQ=="}, "node_modules/safe-stable-stringify": {"version": "2.4.3", "resolved": "https://registry.npmjs.org/safe-stable-stringify/-/safe-stable-stringify-2.4.3.tgz", "integrity": "sha512-e2bDA2WJT0wxseVd4lsDP4+3ONX6HpMXQa1ZhFQ7SU+GjvORCmShbCMltrtIDfkYhVHrOcPtj+KhmDBdPdZD1g==", "engines": {"node": ">=10"}}, "node_modules/scale-ts": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/scale-ts/-/scale-ts-1.6.0.tgz", "integrity": "sha512-Ja5VCjNZR8TGKhUumy9clVVxcDpM+YFjAnkMuwQy68Hixio3VRRvWdE3g8T/yC+HXA0ZDQl2TGyUmtmbcVl40Q==", "optional": true, "peer": true}, "node_modules/scrypt-js": {"version": "3.0.1", "license": "MIT"}, "node_modules/set-blocking": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz", "integrity": "sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw=="}, "node_modules/smoldot": {"version": "2.0.22", "resolved": "https://registry.npmjs.org/smoldot/-/smoldot-2.0.22.tgz", "integrity": "sha512-B50vRgTY6v3baYH6uCgL15tfaag5tcS2o/P5q1OiXcKGv1axZDfz2dzzMuIkVpyMR2ug11F6EAtQlmYBQd292g==", "optional": true, "peer": true, "dependencies": {"ws": "^8.8.1"}}, "node_modules/smoldot/node_modules/ws": {"version": "8.17.0", "resolved": "https://registry.npmjs.org/ws/-/ws-8.17.0.tgz", "integrity": "sha512-uJq6108EgZMAl20KagGkzCKfMEjxmKvZHG7Tlq0Z6nOky7YF7aq4mOx6xK8TJ/i1LeK4Qus7INktacctDgY8Ow==", "optional": true, "peer": true, "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/sonic-boom": {"version": "2.8.0", "resolved": "https://registry.npmjs.org/sonic-boom/-/sonic-boom-2.8.0.tgz", "integrity": "sha512-kuonw1YOYYNOve5iHdSahXPOK49GqwA+LZhI6Wz/l0rP57iKyXXIHaRagOBHAPmGwJC6od2Z9zgvZ5loSgMlVg==", "dependencies": {"atomic-sleep": "^1.0.0"}}, "node_modules/split-on-first": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/split-on-first/-/split-on-first-1.1.0.tgz", "integrity": "sha512-43ZssAJaMusuKWL8sKUBQXHWOpq8d6CfN/u1p4gUzfJkM05C8rxTmYrkIPTXapZpORA6LkkzcUulJ8FqA7Uudw==", "engines": {"node": ">=6"}}, "node_modules/split2": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/split2/-/split2-4.2.0.tgz", "integrity": "sha512-UcjcJOWknrNkF6PLX83qcHM6KHgVKNkV62Y8a5uYDVv9ydGQVwAHMKqHdJje1VTWpljG0WYpCDhrCdAOYH4TWg==", "engines": {"node": ">= 10.x"}}, "node_modules/stream-shift": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/stream-shift/-/stream-shift-1.0.1.tgz", "integrity": "sha512-AiisoFqQ0vbGcZgQPY1cdP2I76glaVA/RauYR4G4thNFgkTqr90yXTo4LYX60Jl+sIlPNHHdGSwo01AvbKUSVQ=="}, "node_modules/strict-uri-encode": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/strict-uri-encode/-/strict-uri-encode-2.0.0.tgz", "integrity": "sha512-QwiXZgpRcKkhTj2Scnn++4PKtWsH0kpzZ62L2R6c/LUVYv7hVnZqcg2+sMuT6R7Jusu1vviK/MFsu6kNJfWlEQ==", "engines": {"node": ">=4"}}, "node_modules/string_decoder": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-width": {"version": "4.2.3", "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/thread-stream": {"version": "0.15.2", "resolved": "https://registry.npmjs.org/thread-stream/-/thread-stream-0.15.2.tgz", "integrity": "sha512-UkEhKIg2pD+fjkHQKyJO3yoIvAP3N6RlNFt2dUhcS1FGvCD1cQa1M/PGknCLFIyZdtJOWQjejp7bdNqmN7zwdA==", "dependencies": {"real-require": "^0.1.0"}}, "node_modules/tr46": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz", "integrity": "sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw=="}, "node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/tweetnacl": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-1.0.3.tgz", "integrity": "sha512-6rt+RN7aOi1nGMyC4Xa5DdYiukl2UWCbcJft7YhxReBGQD7OAM8Pbxw6YMo4r2diNEA8FEmu32YOn9rhaiE5yw=="}, "node_modules/type": {"version": "2.7.2", "resolved": "https://registry.npmjs.org/type/-/type-2.7.2.tgz", "integrity": "sha512-dzlvlNlt6AXU7EBSfpAscydQ7gXB+pPGsPnfJnZpiNJBDj7IaJzQlBZYGdEi4R9HmPdBv2XmWJ6YUtoTa7lmCw=="}, "node_modules/typedarray-to-buffer": {"version": "3.1.5", "resolved": "https://registry.npmjs.org/typedarray-to-buffer/-/typedarray-to-buffer-3.1.5.tgz", "integrity": "sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q==", "dependencies": {"is-typedarray": "^1.0.0"}}, "node_modules/uint8arrays": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-3.1.1.tgz", "integrity": "sha512-+QJa8QRnbdXVpHYjLoTpJIdCTiw9Ir62nocClWuXIq2JIh4Uta0cQsTSpFL678p2CN8B+XSApwcU+pQEqVpKWg==", "dependencies": {"multiformats": "^9.4.2"}}, "node_modules/undici-types": {"version": "5.26.5", "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz", "integrity": "sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA=="}, "node_modules/use-sync-external-store": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.2.0.tgz", "integrity": "sha512-eEgnFxGQ1Ife9bzYs6VLi8/4X6CObHMw9Qr9tPY43iKwsPw8xE8+EFsf/2cFZ5S3esXgpWgtSCtLNS41F+sKPA==", "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0"}}, "node_modules/utf-8-validate": {"version": "5.0.10", "hasInstallScript": true, "license": "MIT", "dependencies": {"node-gyp-build": "^4.3.0"}, "engines": {"node": ">=6.14.2"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw=="}, "node_modules/valtio": {"version": "1.10.5", "resolved": "https://registry.npmjs.org/valtio/-/valtio-1.10.5.tgz", "integrity": "sha512-jTp0k63VXf4r5hPoaC6a6LCG4POkVSh629WLi1+d5PlajLsbynTMd7qAgEiOSPxzoX5iNvbN7iZ/k/g29wrNiQ==", "dependencies": {"proxy-compare": "2.5.1", "use-sync-external-store": "1.2.0"}, "engines": {"node": ">=12.20.0"}, "peerDependencies": {"react": ">=16.8"}, "peerDependenciesMeta": {"react": {"optional": true}}}, "node_modules/web-streams-polyfill": {"version": "3.3.3", "resolved": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz", "integrity": "sha512-d2JWLCivmZYTSIoge9MsgFCZrt571BikcWGYkjC1khllbTeDlGqZ2D8vD8E/lJa8WGWbb7Plm8/XJYV7IJHZZw==", "engines": {"node": ">= 8"}}, "node_modules/webidl-conversions": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ=="}, "node_modules/websocket": {"version": "1.0.35", "resolved": "https://registry.npmjs.org/websocket/-/websocket-1.0.35.tgz", "integrity": "sha512-/REy6amwPZl44DDzvRCkaI1q1bIiQB0mEFQLUrhz3z2EK91cp3n72rAjUlrTP0zV22HJIUOVHQGPxhFRjxjt+Q==", "dependencies": {"bufferutil": "^4.0.1", "debug": "^2.2.0", "es5-ext": "^0.10.63", "typedarray-to-buffer": "^3.1.5", "utf-8-validate": "^5.0.2", "yaeti": "^0.0.6"}, "engines": {"node": ">=4.0.0"}}, "node_modules/websocket/node_modules/debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dependencies": {"ms": "2.0.0"}}, "node_modules/websocket/node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="}, "node_modules/whatwg-url": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz", "integrity": "sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/which-module": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/which-module/-/which-module-2.0.1.tgz", "integrity": "sha512-iBdZ57RDvnOR9AGBhML2vFZf7h8vmBjhoaZqODJBFWHVtKkDmKuHai3cx5PgVMrX5YDNp27AofYbAwctSS+vhQ=="}, "node_modules/wrap-ansi": {"version": "6.2.0", "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz", "integrity": "sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ=="}, "node_modules/ws": {"version": "7.4.6", "license": "MIT", "engines": {"node": ">=8.3.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/y18n": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/y18n/-/y18n-4.0.3.tgz", "integrity": "sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ=="}, "node_modules/yaeti": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/yaeti/-/yaeti-0.0.6.tgz", "integrity": "sha512-MvQa//+KcZCUkBTIC9blM+CU9J2GzuTytsOUwf2lidtvkx/6gnEp1QvJv34t9vdjhFmha/mUiNDbN0D0mJWdug==", "engines": {"node": ">=0.10.32"}}, "node_modules/yargs": {"version": "15.4.1", "resolved": "https://registry.npmjs.org/yargs/-/yargs-15.4.1.tgz", "integrity": "sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A==", "dependencies": {"cliui": "^6.0.0", "decamelize": "^1.2.0", "find-up": "^4.1.0", "get-caller-file": "^2.0.1", "require-directory": "^2.1.1", "require-main-filename": "^2.0.0", "set-blocking": "^2.0.0", "string-width": "^4.2.0", "which-module": "^2.0.0", "y18n": "^4.0.0", "yargs-parser": "^18.1.2"}, "engines": {"node": ">=8"}}, "node_modules/yargs-parser": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-18.1.3.tgz", "integrity": "sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ==", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "engines": {"node": ">=6"}}, "node_modules/zustand": {"version": "4.1.4", "license": "MIT", "dependencies": {"use-sync-external-store": "1.2.0"}, "engines": {"node": ">=12.7.0"}, "peerDependencies": {"immer": ">=9.0", "react": ">=16.8"}, "peerDependenciesMeta": {"immer": {"optional": true}, "react": {"optional": true}}}}, "dependencies": {"@babel/runtime": {"version": "7.24.5", "resolved": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.24.5.tgz", "integrity": "sha512-Nms86NXrsaeU9vbBJKni6gXiEXZ4CVpYVzEjDH9Sb8vmZ3UljyA1GSOJl/6LGPO8EHLuSF9H+IxNXHPX8QHJ4g==", "requires": {"regenerator-runtime": "^0.14.0"}}, "@ethersproject/abi": {"version": "5.7.0", "requires": {"@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/hash": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "@ethersproject/abstract-provider": {"version": "5.7.0", "requires": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/networks": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/web": "^5.7.0"}}, "@ethersproject/abstract-signer": {"version": "5.7.0", "requires": {"@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0"}}, "@ethersproject/address": {"version": "5.7.0", "requires": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/rlp": "^5.7.0"}}, "@ethersproject/base64": {"version": "5.7.0", "requires": {"@ethersproject/bytes": "^5.7.0"}}, "@ethersproject/basex": {"version": "5.7.0", "requires": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/properties": "^5.7.0"}}, "@ethersproject/bignumber": {"version": "5.7.0", "requires": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "bn.js": "^5.2.1"}}, "@ethersproject/bytes": {"version": "5.7.0", "requires": {"@ethersproject/logger": "^5.7.0"}}, "@ethersproject/constants": {"version": "5.7.0", "requires": {"@ethersproject/bignumber": "^5.7.0"}}, "@ethersproject/contracts": {"version": "5.7.0", "requires": {"@ethersproject/abi": "^5.7.0", "@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/transactions": "^5.7.0"}}, "@ethersproject/hash": {"version": "5.7.0", "requires": {"@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/base64": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "@ethersproject/hdnode": {"version": "5.7.0", "requires": {"@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/basex": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/pbkdf2": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/sha2": "^5.7.0", "@ethersproject/signing-key": "^5.7.0", "@ethersproject/strings": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/wordlists": "^5.7.0"}}, "@ethersproject/json-wallets": {"version": "5.7.0", "requires": {"@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/hdnode": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/pbkdf2": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/random": "^5.7.0", "@ethersproject/strings": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "aes-js": "3.0.0", "scrypt-js": "3.0.1"}, "dependencies": {"aes-js": {"version": "3.0.0"}}}, "@ethersproject/keccak256": {"version": "5.7.0", "requires": {"@ethersproject/bytes": "^5.7.0", "js-sha3": "0.8.0"}}, "@ethersproject/logger": {"version": "5.7.0"}, "@ethersproject/networks": {"version": "5.7.1", "requires": {"@ethersproject/logger": "^5.7.0"}}, "@ethersproject/pbkdf2": {"version": "5.7.0", "requires": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/sha2": "^5.7.0"}}, "@ethersproject/properties": {"version": "5.7.0", "requires": {"@ethersproject/logger": "^5.7.0"}}, "@ethersproject/providers": {"version": "5.7.2", "requires": {"@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/base64": "^5.7.0", "@ethersproject/basex": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/hash": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/networks": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/random": "^5.7.0", "@ethersproject/rlp": "^5.7.0", "@ethersproject/sha2": "^5.7.0", "@ethersproject/strings": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/web": "^5.7.0", "bech32": "1.1.4", "ws": "7.4.6"}}, "@ethersproject/random": {"version": "5.7.0", "requires": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "@ethersproject/rlp": {"version": "5.7.0", "requires": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "@ethersproject/sha2": {"version": "5.7.0", "requires": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "hash.js": "1.1.7"}}, "@ethersproject/signing-key": {"version": "5.7.0", "requires": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "bn.js": "^5.2.1", "elliptic": "6.5.4", "hash.js": "1.1.7"}}, "@ethersproject/solidity": {"version": "5.7.0", "requires": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/sha2": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "@ethersproject/strings": {"version": "5.7.0", "requires": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "@ethersproject/transactions": {"version": "5.7.0", "requires": {"@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/rlp": "^5.7.0", "@ethersproject/signing-key": "^5.7.0"}}, "@ethersproject/units": {"version": "5.7.0", "requires": {"@ethersproject/bignumber": "^5.7.0", "@ethersproject/constants": "^5.7.0", "@ethersproject/logger": "^5.7.0"}}, "@ethersproject/wallet": {"version": "5.7.0", "requires": {"@ethersproject/abstract-provider": "^5.7.0", "@ethersproject/abstract-signer": "^5.7.0", "@ethersproject/address": "^5.7.0", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/hash": "^5.7.0", "@ethersproject/hdnode": "^5.7.0", "@ethersproject/json-wallets": "^5.7.0", "@ethersproject/keccak256": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/random": "^5.7.0", "@ethersproject/signing-key": "^5.7.0", "@ethersproject/transactions": "^5.7.0", "@ethersproject/wordlists": "^5.7.0"}}, "@ethersproject/web": {"version": "5.7.1", "requires": {"@ethersproject/base64": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "@ethersproject/wordlists": {"version": "5.7.0", "requires": {"@ethersproject/bytes": "^5.7.0", "@ethersproject/hash": "^5.7.0", "@ethersproject/logger": "^5.7.0", "@ethersproject/properties": "^5.7.0", "@ethersproject/strings": "^5.7.0"}}, "@lit-labs/ssr-dom-shim": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@lit-labs/ssr-dom-shim/-/ssr-dom-shim-1.1.1.tgz", "integrity": "sha512-kXOeFbfCm4fFf2A3WwVEeQj55tMZa8c8/f9AKHMobQMkzNUfUj+antR3fRPaZJawsa1aZiP/Da3ndpZrwEe4rQ=="}, "@lit/reactive-element": {"version": "1.6.2", "resolved": "https://registry.npmjs.org/@lit/reactive-element/-/reactive-element-1.6.2.tgz", "integrity": "sha512-rDfl+QnCYjuIGf5xI2sVJWdYIi56CTCwWa+nidKYX6oIuBYwUbT/vX4qbUDlHiZKJ/3FRNQ/tWJui44p6/stSA==", "requires": {"@lit-labs/ssr-dom-shim": "^1.0.0"}}, "@metamask/detect-provider": {"version": "2.0.0"}, "@motionone/animation": {"version": "10.15.1", "resolved": "https://registry.npmjs.org/@motionone/animation/-/animation-10.15.1.tgz", "integrity": "sha512-mZcJxLjHor+bhcPuIFErMDNyrdb2vJur8lSfMCsuCB4UyV8ILZLvK+t+pg56erv8ud9xQGK/1OGPt10agPrCyQ==", "requires": {"@motionone/easing": "^10.15.1", "@motionone/types": "^10.15.1", "@motionone/utils": "^10.15.1", "tslib": "^2.3.1"}}, "@motionone/dom": {"version": "10.16.2", "resolved": "https://registry.npmjs.org/@motionone/dom/-/dom-10.16.2.tgz", "integrity": "sha512-bnuHdNbge1FutZXv+k7xub9oPWcF0hsu8y1HTH/qg6av58YI0VufZ3ngfC7p2xhMJMnoh0LXFma2EGTgPeCkeg==", "requires": {"@motionone/animation": "^10.15.1", "@motionone/generators": "^10.15.1", "@motionone/types": "^10.15.1", "@motionone/utils": "^10.15.1", "hey-listen": "^1.0.8", "tslib": "^2.3.1"}}, "@motionone/easing": {"version": "10.15.1", "resolved": "https://registry.npmjs.org/@motionone/easing/-/easing-10.15.1.tgz", "integrity": "sha512-6hIHBSV+ZVehf9dcKZLT7p5PEKHGhDwky2k8RKkmOvUoYP3S+dXsKupyZpqx5apjd9f+php4vXk4LuS+ADsrWw==", "requires": {"@motionone/utils": "^10.15.1", "tslib": "^2.3.1"}}, "@motionone/generators": {"version": "10.15.1", "resolved": "https://registry.npmjs.org/@motionone/generators/-/generators-10.15.1.tgz", "integrity": "sha512-67HLsvHJbw6cIbLA/o+gsm7h+6D4Sn7AUrB/GPxvujse1cGZ38F5H7DzoH7PhX+sjvtDnt2IhFYF2Zp1QTMKWQ==", "requires": {"@motionone/types": "^10.15.1", "@motionone/utils": "^10.15.1", "tslib": "^2.3.1"}}, "@motionone/svelte": {"version": "10.16.2", "resolved": "https://registry.npmjs.org/@motionone/svelte/-/svelte-10.16.2.tgz", "integrity": "sha512-38xsroKrfK+aHYhuQlE6eFcGy0EwrB43Q7RGjF73j/kRUTcLNu/LAaKiLLsN5lyqVzCgTBVt4TMT/ShWbTbc5Q==", "requires": {"@motionone/dom": "^10.16.2", "tslib": "^2.3.1"}}, "@motionone/types": {"version": "10.15.1", "resolved": "https://registry.npmjs.org/@motionone/types/-/types-10.15.1.tgz", "integrity": "sha512-iIUd/EgUsRZGrvW0jqdst8st7zKTzS9EsKkP+6c6n4MPZoQHwiHuVtTQLD6Kp0bsBLhNzKIBlHXponn/SDT4hA=="}, "@motionone/utils": {"version": "10.15.1", "resolved": "https://registry.npmjs.org/@motionone/utils/-/utils-10.15.1.tgz", "integrity": "sha512-p0YncgU+iklvYr/Dq4NobTRdAPv9PveRDUXabPEeOjBLSO/1FNB2phNTZxOxpi1/GZwYpAoECEa0Wam+nsmhSw==", "requires": {"@motionone/types": "^10.15.1", "hey-listen": "^1.0.8", "tslib": "^2.3.1"}}, "@motionone/vue": {"version": "10.16.2", "resolved": "https://registry.npmjs.org/@motionone/vue/-/vue-10.16.2.tgz", "integrity": "sha512-7/dEK/nWQXOkJ70bqb2KyNfSWbNvWqKKq1C8juj+0Mg/AorgD8O5wE3naddK0G+aXuNMqRuc4jlsYHHWHtIzVw==", "requires": {"@motionone/dom": "^10.16.2", "tslib": "^2.3.1"}}, "@noble/curves": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/@noble/curves/-/curves-1.4.0.tgz", "integrity": "sha512-p+4cb332SFCrReJkCYe8Xzm0OWi4Jji5jVdIZRL/PmacmDkFNw6MrrV+gGpiPxLHbV+zKFRywUWbaseT+tZRXg==", "peer": true, "requires": {"@noble/hashes": "1.4.0"}}, "@noble/hashes": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/@noble/hashes/-/hashes-1.4.0.tgz", "integrity": "sha512-V1JJ1WTRUqHHrOSh597hURcMqVKVGL/ea3kv0gSnEdsEZ0/+VyPghM1lMNGc00z7CIQorSvbKpuJkxvuHbvdbg==", "peer": true}, "@noble/secp256k1": {"version": "1.7.1", "resolved": "https://registry.npmjs.org/@noble/secp256k1/-/secp256k1-1.7.1.tgz", "integrity": "sha512-hOUk6AyBFmqVrv7k5WAw/LpszxVbj9gGN4JRkIX52fdFAj1UA61KXmZDvqVEm+pOyec3+fIeZB02LYa/pWOArw=="}, "@polkadot-api/json-rpc-provider": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/@polkadot-api/json-rpc-provider/-/json-rpc-provider-0.0.1.tgz", "integrity": "sha512-/SMC/l7foRjpykLTUTacIH05H3mr9ip8b5xxfwXlVezXrNVLp3Cv0GX6uItkKd+ZjzVPf3PFrDF2B2/HLSNESA==", "optional": true, "peer": true}, "@polkadot-api/json-rpc-provider-proxy": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/@polkadot-api/json-rpc-provider-proxy/-/json-rpc-provider-proxy-0.0.1.tgz", "integrity": "sha512-gmVDUP8LpCH0BXewbzqXF2sdHddq1H1q+XrAW2of+KZj4woQkIGBRGTJHeBEVHe30EB+UejR1N2dT4PO/RvDdg==", "optional": true, "peer": true}, "@polkadot-api/metadata-builders": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/@polkadot-api/metadata-builders/-/metadata-builders-0.0.1.tgz", "integrity": "sha512-GCI78BHDzXAF/L2pZD6Aod/yl82adqQ7ftNmKg51ixRL02JpWUA+SpUKTJE5MY1p8kiJJIo09P2um24SiJHxNA==", "optional": true, "peer": true, "requires": {"@polkadot-api/substrate-bindings": "0.0.1", "@polkadot-api/utils": "0.0.1"}}, "@polkadot-api/observable-client": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/@polkadot-api/observable-client/-/observable-client-0.1.0.tgz", "integrity": "sha512-GBCGDRztKorTLna/unjl/9SWZcRmvV58o9jwU2Y038VuPXZcr01jcw/1O3x+yeAuwyGzbucI/mLTDa1QoEml3A==", "optional": true, "peer": true, "requires": {"@polkadot-api/metadata-builders": "0.0.1", "@polkadot-api/substrate-bindings": "0.0.1", "@polkadot-api/substrate-client": "0.0.1", "@polkadot-api/utils": "0.0.1"}}, "@polkadot-api/substrate-bindings": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/@polkadot-api/substrate-bindings/-/substrate-bindings-0.0.1.tgz", "integrity": "sha512-bAe7a5bOPnuFVmpv7y4BBMRpNTnMmE0jtTqRUw/+D8ZlEHNVEJQGr4wu3QQCl7k1GnSV1wfv3mzIbYjErEBocg==", "optional": true, "peer": true, "requires": {"@noble/hashes": "^1.3.1", "@polkadot-api/utils": "0.0.1", "@scure/base": "^1.1.1", "scale-ts": "^1.6.0"}}, "@polkadot-api/substrate-client": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/@polkadot-api/substrate-client/-/substrate-client-0.0.1.tgz", "integrity": "sha512-9Bg9SGc3AwE+wXONQoW8GC00N3v6lCZLW74HQzqB6ROdcm5VAHM4CB/xRzWSUF9CXL78ugiwtHx3wBcpx4H4Wg==", "optional": true, "peer": true}, "@polkadot-api/utils": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/@polkadot-api/utils/-/utils-0.0.1.tgz", "integrity": "sha512-3j+pRmlF9SgiYDabSdZsBSsN5XHbpXOAce1lWj56IEEaFZVjsiCaxDOA7C9nCcgfVXuvnbxqqEGQvnY+QfBAUw==", "optional": true, "peer": true}, "@polkadot/api": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/api/-/api-11.0.3.tgz", "integrity": "sha512-VYuS42s5MiUOLo/PP1deYsddelGP4/mb1KFonXyWk69XowsfihGbVjsjh+DA4jPXvoNJqdGnDdu3SeppTY9MZQ==", "peer": true, "requires": {"@polkadot/api-augment": "11.0.3", "@polkadot/api-base": "11.0.3", "@polkadot/api-derive": "11.0.3", "@polkadot/keyring": "^12.6.2", "@polkadot/rpc-augment": "11.0.3", "@polkadot/rpc-core": "11.0.3", "@polkadot/rpc-provider": "11.0.3", "@polkadot/types": "11.0.3", "@polkadot/types-augment": "11.0.3", "@polkadot/types-codec": "11.0.3", "@polkadot/types-create": "11.0.3", "@polkadot/types-known": "11.0.3", "@polkadot/util": "^12.6.2", "@polkadot/util-crypto": "^12.6.2", "eventemitter3": "^5.0.1", "rxjs": "^7.8.1", "tslib": "^2.6.2"}}, "@polkadot/api-augment": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/api-augment/-/api-augment-11.0.3.tgz", "integrity": "sha512-O1QEUXiHpPJqVe398EQ+tywZLj1vDNZALzh2TKsyFSqEjT4N/EschCck9aFXpB0Bp0K2lm7+fKJf6eWPShNfFQ==", "peer": true, "requires": {"@polkadot/api-base": "11.0.3", "@polkadot/rpc-augment": "11.0.3", "@polkadot/types": "11.0.3", "@polkadot/types-augment": "11.0.3", "@polkadot/types-codec": "11.0.3", "@polkadot/util": "^12.6.2", "tslib": "^2.6.2"}}, "@polkadot/api-base": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/api-base/-/api-base-11.0.3.tgz", "integrity": "sha512-e/KSsDcFIG17SPw+buBXAq5NnBOMMOWljTljazDF7Nq3Sz6P+SFqOSb3kdICtp1fYAxg5my/uTs2OOARxJRXKA==", "peer": true, "requires": {"@polkadot/rpc-core": "11.0.3", "@polkadot/types": "11.0.3", "@polkadot/util": "^12.6.2", "rxjs": "^7.8.1", "tslib": "^2.6.2"}}, "@polkadot/api-derive": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/api-derive/-/api-derive-11.0.3.tgz", "integrity": "sha512-WJriPWX0WnDhHAETAYkgCxIhkKmnRPqSsBKUHZxq+GgMJByOA4wgo3gzq5aQLXgkcCLV3wM4a6hALodYz0urSw==", "peer": true, "requires": {"@polkadot/api": "11.0.3", "@polkadot/api-augment": "11.0.3", "@polkadot/api-base": "11.0.3", "@polkadot/rpc-core": "11.0.3", "@polkadot/types": "11.0.3", "@polkadot/types-codec": "11.0.3", "@polkadot/util": "^12.6.2", "@polkadot/util-crypto": "^12.6.2", "rxjs": "^7.8.1", "tslib": "^2.6.2"}}, "@polkadot/extension-dapp": {"version": "0.44.9", "resolved": "https://registry.npmjs.org/@polkadot/extension-dapp/-/extension-dapp-0.44.9.tgz", "integrity": "sha512-xYY9bg4y2YW1ORWTflrPBypYueCpzajlYsU1CWuPP9fzKsdfd97wwa+dIYYvLbJy7tcivC+uIT3BpaFaJn2mXg==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/extension-inject": "^0.44.9", "@polkadot/util": "^10.4.2", "@polkadot/util-crypto": "^10.4.2"}, "dependencies": {"@noble/hashes": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@noble/hashes/-/hashes-1.2.0.tgz", "integrity": "sha512-FZfhjEDbT5GRswV3C6uvLPHMiVD6lQBmpoX5+eSiPaMTXte/IKqI5dykDxzZB/WBeK/CDuQRBWarPdi3FNY2zQ=="}, "@polkadot/networks": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/networks/-/networks-10.4.2.tgz", "integrity": "sha512-FAh/znrEvWBiA/LbcT5GXHsCFUl//y9KqxLghSr/CreAmAergiJNT0MVUezC7Y36nkATgmsr4ylFwIxhVtuuCw==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/util": "10.4.2", "@substrate/ss58-registry": "^1.38.0"}}, "@polkadot/util": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/util/-/util-10.4.2.tgz", "integrity": "sha512-0r5MGICYiaCdWnx+7Axlpvzisy/bi1wZGXgCSw5+ZTyPTOqvsYRqM2X879yxvMsGfibxzWqNzaiVjToz1jvUaA==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/x-bigint": "10.4.2", "@polkadot/x-global": "10.4.2", "@polkadot/x-textdecoder": "10.4.2", "@polkadot/x-textencoder": "10.4.2", "@types/bn.js": "^5.1.1", "bn.js": "^5.2.1"}}, "@polkadot/util-crypto": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/util-crypto/-/util-crypto-10.4.2.tgz", "integrity": "sha512-RxZvF7C4+EF3fzQv8hZOLrYCBq5+wA+2LWv98nECkroChY3C2ZZvyWDqn8+aonNULt4dCVTWDZM0QIY6y4LUAQ==", "requires": {"@babel/runtime": "^7.20.13", "@noble/hashes": "1.2.0", "@noble/secp256k1": "1.7.1", "@polkadot/networks": "10.4.2", "@polkadot/util": "10.4.2", "@polkadot/wasm-crypto": "^6.4.1", "@polkadot/x-bigint": "10.4.2", "@polkadot/x-randomvalues": "10.4.2", "@scure/base": "1.1.1", "ed2curve": "^0.3.0", "tweetnacl": "^1.0.3"}}, "@polkadot/wasm-bridge": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-bridge/-/wasm-bridge-6.4.1.tgz", "integrity": "sha512-QZDvz6dsUlbYsaMV5biZgZWkYH9BC5AfhT0f0/knv8+LrbAoQdP3Asbvddw8vyU9sbpuCHXrd4bDLBwUCRfrBQ==", "requires": {"@babel/runtime": "^7.20.6"}}, "@polkadot/wasm-crypto": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto/-/wasm-crypto-6.4.1.tgz", "integrity": "sha512-FH+dcDPdhSLJvwL0pMLtn/LIPd62QDPODZRCmDyw+pFjLOMaRBc7raomWUOqyRWJTnqVf/iscc2rLVLNMyt7ag==", "requires": {"@babel/runtime": "^7.20.6", "@polkadot/wasm-bridge": "6.4.1", "@polkadot/wasm-crypto-asmjs": "6.4.1", "@polkadot/wasm-crypto-init": "6.4.1", "@polkadot/wasm-crypto-wasm": "6.4.1", "@polkadot/wasm-util": "6.4.1"}}, "@polkadot/wasm-crypto-asmjs": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto-asmjs/-/wasm-crypto-asmjs-6.4.1.tgz", "integrity": "sha512-UxZTwuBZlnODGIQdCsE2Sn/jU0O2xrNQ/TkhRFELfkZXEXTNu4lw6NpaKq7Iey4L+wKd8h4lT3VPVkMcPBLOvA==", "requires": {"@babel/runtime": "^7.20.6"}}, "@polkadot/wasm-crypto-init": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto-init/-/wasm-crypto-init-6.4.1.tgz", "integrity": "sha512-1ALagSi/nfkyFaH6JDYfy/QbicVbSn99K8PV9rctDUfxc7P06R7CoqbjGQ4OMPX6w1WYVPU7B4jPHGLYBlVuMw==", "requires": {"@babel/runtime": "^7.20.6", "@polkadot/wasm-bridge": "6.4.1", "@polkadot/wasm-crypto-asmjs": "6.4.1", "@polkadot/wasm-crypto-wasm": "6.4.1"}}, "@polkadot/wasm-crypto-wasm": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto-wasm/-/wasm-crypto-wasm-6.4.1.tgz", "integrity": "sha512-3VV9ZGzh0ZY3SmkkSw+0TRXxIpiO0nB8lFwlRgcwaCihwrvLfRnH9GI8WE12mKsHVjWTEVR3ogzILJxccAUjDA==", "requires": {"@babel/runtime": "^7.20.6", "@polkadot/wasm-util": "6.4.1"}}, "@polkadot/wasm-util": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-util/-/wasm-util-6.4.1.tgz", "integrity": "sha512-Uwo+WpEsDmFExWC5kTNvsVhvqXMZEKf4gUHXFn4c6Xz4lmieRT5g+1bO1KJ21pl4msuIgdV3Bksfs/oiqMFqlw==", "requires": {"@babel/runtime": "^7.20.6"}}, "@polkadot/x-bigint": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-bigint/-/x-bigint-10.4.2.tgz", "integrity": "sha512-awRiox+/XSReLzimAU94fPldowiwnnMUkQJe8AebYhNocAj6SJU00GNoj6j6tAho6yleOwrTJXZaWFBaQVJQNg==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/x-global": "10.4.2"}}, "@polkadot/x-randomvalues": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-randomvalues/-/x-randomvalues-10.4.2.tgz", "integrity": "sha512-mf1Wbpe7pRZHO0V3V89isPLqZOy5XGX2bCqsfUWHgb1NvV1MMx5TjVjdaYyNlGTiOkAmJKlOHshcfPU2sYWpNg==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/x-global": "10.4.2"}}, "@polkadot/x-textdecoder": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-textdecoder/-/x-textdecoder-10.4.2.tgz", "integrity": "sha512-d3ADduOKUTU+cliz839+KCFmi23pxTlabH7qh7Vs1GZQvXOELWdqFOqakdiAjtMn68n1KVF4O14Y+OUm7gp/zA==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/x-global": "10.4.2"}}, "@polkadot/x-textencoder": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-textencoder/-/x-textencoder-10.4.2.tgz", "integrity": "sha512-mxcQuA1exnyv74Kasl5vxBq01QwckG088lYjc3KwmND6+pPrW2OWagbxFX5VFoDLDAE+UJtnUHsjdWyOTDhpQA==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/x-global": "10.4.2"}}, "@scure/base": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@scure/base/-/base-1.1.1.tgz", "integrity": "sha512-ZxOhsSyxYwLJj3pLZCefNitxsj093tb2vq90mp2txoYeBqbcjDjqFhyM8eUjq/uFm6zJ+mUuqxlS2FkuSY1MTA=="}}}, "@polkadot/extension-inject": {"version": "0.44.9", "resolved": "https://registry.npmjs.org/@polkadot/extension-inject/-/extension-inject-0.44.9.tgz", "integrity": "sha512-c23vp0C/8R5C3gdqoH2JRlKcvVjJFl9uM3t6rM/uwDs7GEQr9jrsmIOHGhNoI1/M/xBrCm/KuYNYi0dafdm/Vw==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/rpc-provider": "^9.14.2", "@polkadot/types": "^9.14.2", "@polkadot/util": "^10.4.2", "@polkadot/util-crypto": "^10.4.2", "@polkadot/x-global": "^10.4.2"}, "dependencies": {"@noble/hashes": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@noble/hashes/-/hashes-1.2.0.tgz", "integrity": "sha512-FZfhjEDbT5GRswV3C6uvLPHMiVD6lQBmpoX5+eSiPaMTXte/IKqI5dykDxzZB/WBeK/CDuQRBWarPdi3FNY2zQ=="}, "@polkadot/keyring": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/keyring/-/keyring-10.4.2.tgz", "integrity": "sha512-7iHhJuXaHrRTG6cJDbZE9G+c1ts1dujp0qbO4RfAPmT7YUvphHvAtCKueN9UKPz5+TYDL+rP/jDEaSKU8jl/qQ==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/util": "10.4.2", "@polkadot/util-crypto": "10.4.2"}}, "@polkadot/networks": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/networks/-/networks-10.4.2.tgz", "integrity": "sha512-FAh/znrEvWBiA/LbcT5GXHsCFUl//y9KqxLghSr/CreAmAergiJNT0MVUezC7Y36nkATgmsr4ylFwIxhVtuuCw==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/util": "10.4.2", "@substrate/ss58-registry": "^1.38.0"}}, "@polkadot/rpc-provider": {"version": "9.14.2", "resolved": "https://registry.npmjs.org/@polkadot/rpc-provider/-/rpc-provider-9.14.2.tgz", "integrity": "sha512-YTSywjD5PF01V47Ru5tln2LlpUwJiSOdz6rlJXPpMaY53hUp7+xMU01FVAQ1bllSBNisSD1Msv/mYHq84Oai2g==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/keyring": "^10.4.2", "@polkadot/types": "9.14.2", "@polkadot/types-support": "9.14.2", "@polkadot/util": "^10.4.2", "@polkadot/util-crypto": "^10.4.2", "@polkadot/x-fetch": "^10.4.2", "@polkadot/x-global": "^10.4.2", "@polkadot/x-ws": "^10.4.2", "@substrate/connect": "0.7.19", "eventemitter3": "^5.0.0", "mock-socket": "^9.2.1", "nock": "^13.3.0"}}, "@polkadot/types": {"version": "9.14.2", "resolved": "https://registry.npmjs.org/@polkadot/types/-/types-9.14.2.tgz", "integrity": "sha512-hGLddTiJbvowhhUZJ3k+olmmBc1KAjWIQxujIUIYASih8FQ3/YJDKxaofGOzh0VygOKW3jxQBN2VZPofyDP9KQ==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/keyring": "^10.4.2", "@polkadot/types-augment": "9.14.2", "@polkadot/types-codec": "9.14.2", "@polkadot/types-create": "9.14.2", "@polkadot/util": "^10.4.2", "@polkadot/util-crypto": "^10.4.2", "rxjs": "^7.8.0"}}, "@polkadot/types-augment": {"version": "9.14.2", "resolved": "https://registry.npmjs.org/@polkadot/types-augment/-/types-augment-9.14.2.tgz", "integrity": "sha512-WO9d7RJufUeY3iFgt2Wz762kOu1tjEiGBR5TT4AHtpEchVHUeosVTrN9eycC+BhleqYu52CocKz6u3qCT/jKLg==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/types": "9.14.2", "@polkadot/types-codec": "9.14.2", "@polkadot/util": "^10.4.2"}}, "@polkadot/types-codec": {"version": "9.14.2", "resolved": "https://registry.npmjs.org/@polkadot/types-codec/-/types-codec-9.14.2.tgz", "integrity": "sha512-AJ4XF7W1no4PENLBRU955V6gDxJw0h++EN3YoDgThozZ0sj3OxyFupKgNBZcZb2V23H8JxQozzIad8k+nJbO1w==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/util": "^10.4.2", "@polkadot/x-bigint": "^10.4.2"}}, "@polkadot/types-create": {"version": "9.14.2", "resolved": "https://registry.npmjs.org/@polkadot/types-create/-/types-create-9.14.2.tgz", "integrity": "sha512-nSnKpBierlmGBQT8r6/SHf6uamBIzk4WmdMsAsR4uJKJF1PtbIqx2W5PY91xWSiMSNMzjkbCppHkwaDAMwLGaw==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/types-codec": "9.14.2", "@polkadot/util": "^10.4.2"}}, "@polkadot/types-support": {"version": "9.14.2", "resolved": "https://registry.npmjs.org/@polkadot/types-support/-/types-support-9.14.2.tgz", "integrity": "sha512-VWCOPgXDK3XtXT7wMLyIWeNDZxUbNcw/8Pn6n6vMogs7o/n4h6WGbGMeTIQhPWyn831/RmkVs5+2DUC+2LlOhw==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/util": "^10.4.2"}}, "@polkadot/util": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/util/-/util-10.4.2.tgz", "integrity": "sha512-0r5MGICYiaCdWnx+7Axlpvzisy/bi1wZGXgCSw5+ZTyPTOqvsYRqM2X879yxvMsGfibxzWqNzaiVjToz1jvUaA==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/x-bigint": "10.4.2", "@polkadot/x-global": "10.4.2", "@polkadot/x-textdecoder": "10.4.2", "@polkadot/x-textencoder": "10.4.2", "@types/bn.js": "^5.1.1", "bn.js": "^5.2.1"}}, "@polkadot/util-crypto": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/util-crypto/-/util-crypto-10.4.2.tgz", "integrity": "sha512-RxZvF7C4+EF3fzQv8hZOLrYCBq5+wA+2LWv98nECkroChY3C2ZZvyWDqn8+aonNULt4dCVTWDZM0QIY6y4LUAQ==", "requires": {"@babel/runtime": "^7.20.13", "@noble/hashes": "1.2.0", "@noble/secp256k1": "1.7.1", "@polkadot/networks": "10.4.2", "@polkadot/util": "10.4.2", "@polkadot/wasm-crypto": "^6.4.1", "@polkadot/x-bigint": "10.4.2", "@polkadot/x-randomvalues": "10.4.2", "@scure/base": "1.1.1", "ed2curve": "^0.3.0", "tweetnacl": "^1.0.3"}}, "@polkadot/wasm-bridge": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-bridge/-/wasm-bridge-6.4.1.tgz", "integrity": "sha512-QZDvz6dsUlbYsaMV5biZgZWkYH9BC5AfhT0f0/knv8+LrbAoQdP3Asbvddw8vyU9sbpuCHXrd4bDLBwUCRfrBQ==", "requires": {"@babel/runtime": "^7.20.6"}}, "@polkadot/wasm-crypto": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto/-/wasm-crypto-6.4.1.tgz", "integrity": "sha512-FH+dcDPdhSLJvwL0pMLtn/LIPd62QDPODZRCmDyw+pFjLOMaRBc7raomWUOqyRWJTnqVf/iscc2rLVLNMyt7ag==", "requires": {"@babel/runtime": "^7.20.6", "@polkadot/wasm-bridge": "6.4.1", "@polkadot/wasm-crypto-asmjs": "6.4.1", "@polkadot/wasm-crypto-init": "6.4.1", "@polkadot/wasm-crypto-wasm": "6.4.1", "@polkadot/wasm-util": "6.4.1"}}, "@polkadot/wasm-crypto-asmjs": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto-asmjs/-/wasm-crypto-asmjs-6.4.1.tgz", "integrity": "sha512-UxZTwuBZlnODGIQdCsE2Sn/jU0O2xrNQ/TkhRFELfkZXEXTNu4lw6NpaKq7Iey4L+wKd8h4lT3VPVkMcPBLOvA==", "requires": {"@babel/runtime": "^7.20.6"}}, "@polkadot/wasm-crypto-init": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto-init/-/wasm-crypto-init-6.4.1.tgz", "integrity": "sha512-1ALagSi/nfkyFaH6JDYfy/QbicVbSn99K8PV9rctDUfxc7P06R7CoqbjGQ4OMPX6w1WYVPU7B4jPHGLYBlVuMw==", "requires": {"@babel/runtime": "^7.20.6", "@polkadot/wasm-bridge": "6.4.1", "@polkadot/wasm-crypto-asmjs": "6.4.1", "@polkadot/wasm-crypto-wasm": "6.4.1"}}, "@polkadot/wasm-crypto-wasm": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto-wasm/-/wasm-crypto-wasm-6.4.1.tgz", "integrity": "sha512-3VV9ZGzh0ZY3SmkkSw+0TRXxIpiO0nB8lFwlRgcwaCihwrvLfRnH9GI8WE12mKsHVjWTEVR3ogzILJxccAUjDA==", "requires": {"@babel/runtime": "^7.20.6", "@polkadot/wasm-util": "6.4.1"}}, "@polkadot/wasm-util": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/@polkadot/wasm-util/-/wasm-util-6.4.1.tgz", "integrity": "sha512-Uwo+WpEsDmFExWC5kTNvsVhvqXMZEKf4gUHXFn4c6Xz4lmieRT5g+1bO1KJ21pl4msuIgdV3Bksfs/oiqMFqlw==", "requires": {"@babel/runtime": "^7.20.6"}}, "@polkadot/x-bigint": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-bigint/-/x-bigint-10.4.2.tgz", "integrity": "sha512-awRiox+/XSReLzimAU94fPldowiwnnMUkQJe8AebYhNocAj6SJU00GNoj6j6tAho6yleOwrTJXZaWFBaQVJQNg==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/x-global": "10.4.2"}}, "@polkadot/x-fetch": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-fetch/-/x-fetch-10.4.2.tgz", "integrity": "sha512-Ubb64yaM4qwhogNP+4mZ3ibRghEg5UuCYRMNaCFoPgNAY8tQXuDKrHzeks3+frlmeH9YRd89o8wXLtWouwZIcw==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/x-global": "10.4.2", "@types/node-fetch": "^2.6.2", "node-fetch": "^3.3.0"}}, "@polkadot/x-randomvalues": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-randomvalues/-/x-randomvalues-10.4.2.tgz", "integrity": "sha512-mf1Wbpe7pRZHO0V3V89isPLqZOy5XGX2bCqsfUWHgb1NvV1MMx5TjVjdaYyNlGTiOkAmJKlOHshcfPU2sYWpNg==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/x-global": "10.4.2"}}, "@polkadot/x-textdecoder": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-textdecoder/-/x-textdecoder-10.4.2.tgz", "integrity": "sha512-d3ADduOKUTU+cliz839+KCFmi23pxTlabH7qh7Vs1GZQvXOELWdqFOqakdiAjtMn68n1KVF4O14Y+OUm7gp/zA==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/x-global": "10.4.2"}}, "@polkadot/x-textencoder": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-textencoder/-/x-textencoder-10.4.2.tgz", "integrity": "sha512-mxcQuA1exnyv74Kasl5vxBq01QwckG088lYjc3KwmND6+pPrW2OWagbxFX5VFoDLDAE+UJtnUHsjdWyOTDhpQA==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/x-global": "10.4.2"}}, "@polkadot/x-ws": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-ws/-/x-ws-10.4.2.tgz", "integrity": "sha512-3gHSTXAWQu1EMcMVTF5QDKHhEHzKxhAArweEyDXE7VsgKUP/ixxw4hVZBrkX122iI5l5mjSiooRSnp/Zl3xqDQ==", "requires": {"@babel/runtime": "^7.20.13", "@polkadot/x-global": "10.4.2", "@types/websocket": "^1.0.5", "websocket": "^1.0.34"}}, "@scure/base": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@scure/base/-/base-1.1.1.tgz", "integrity": "sha512-ZxOhsSyxYwLJj3pLZCefNitxsj093tb2vq90mp2txoYeBqbcjDjqFhyM8eUjq/uFm6zJ+mUuqxlS2FkuSY1MTA=="}, "@substrate/connect": {"version": "0.7.19", "resolved": "https://registry.npmjs.org/@substrate/connect/-/connect-0.7.19.tgz", "integrity": "sha512-+DDRadc466gCmDU71sHrYOt1HcI2Cbhm7zdCFjZfFVHXhC/E8tOdrVSglAH2HDEHR0x2SiHRxtxOGC7ak2Zjog==", "optional": true, "requires": {"@substrate/connect-extension-protocol": "^1.0.1", "@substrate/smoldot-light": "0.7.9", "eventemitter3": "^4.0.7"}, "dependencies": {"eventemitter3": {"version": "4.0.7", "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz", "integrity": "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==", "optional": true}}}, "@substrate/connect-extension-protocol": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@substrate/connect-extension-protocol/-/connect-extension-protocol-1.0.1.tgz", "integrity": "sha512-161JhCC1csjH3GE5mPLEd7HbWtwNSPJBg3p1Ksz9SFlTzj/bgEwudiRN2y5i0MoLGCIJRYKyKGMxVnd29PzNjg==", "optional": true}, "node-fetch": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-3.3.2.tgz", "integrity": "sha512-dRB78srN/l6gqWulah9SrxeYnxeddIG30+GOqK/9OlLVyLg3HPnr6SqOWTWOXKRwC2eGYCkZ59NNuSgvSrpgOA==", "requires": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}}}}, "@polkadot/keyring": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/keyring/-/keyring-12.6.2.tgz", "integrity": "sha512-O3Q7GVmRYm8q7HuB3S0+Yf/q/EB2egKRRU3fv9b3B7V+A52tKzA+vIwEmNVaD1g5FKW9oB97rmpggs0zaKFqHw==", "peer": true, "requires": {"@polkadot/util": "12.6.2", "@polkadot/util-crypto": "12.6.2", "tslib": "^2.6.2"}}, "@polkadot/networks": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/networks/-/networks-12.6.2.tgz", "integrity": "sha512-1oWtZm1IvPWqvMrldVH6NI2gBoCndl5GEwx7lAuQWGr7eNL+6Bdc5K3Z9T0MzFvDGoi2/CBqjX9dRKo39pDC/w==", "peer": true, "requires": {"@polkadot/util": "12.6.2", "@substrate/ss58-registry": "^1.44.0", "tslib": "^2.6.2"}}, "@polkadot/rpc-augment": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/rpc-augment/-/rpc-augment-11.0.3.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON>EPwdRXMZmleKE0u/qVc20Ut+9O5mLoxKM6jCplg+aWvc51pbS2XOpXEpvWRIWWzCSpqzVc6WcMMYU6MTA==", "peer": true, "requires": {"@polkadot/rpc-core": "11.0.3", "@polkadot/types": "11.0.3", "@polkadot/types-codec": "11.0.3", "@polkadot/util": "^12.6.2", "tslib": "^2.6.2"}}, "@polkadot/rpc-core": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/rpc-core/-/rpc-core-11.0.3.tgz", "integrity": "sha512-DhMUoAVxRw1Cv49lkVQ889eLbrfXZbNCFBTEMyauNUQFZUyIDE8w/FLPiOsyhiXwUww5wTMWte9UnWxvBlppvQ==", "peer": true, "requires": {"@polkadot/rpc-augment": "11.0.3", "@polkadot/rpc-provider": "11.0.3", "@polkadot/types": "11.0.3", "@polkadot/util": "^12.6.2", "rxjs": "^7.8.1", "tslib": "^2.6.2"}}, "@polkadot/rpc-provider": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/rpc-provider/-/rpc-provider-11.0.3.tgz", "integrity": "sha512-xgfHOF4y9jAD9aDDX/i9WUCRw8N6duRs0P/ysw2AafGKUGSBA/pqP/WXN61dQqq+DzZYfzL/FsMyTcUirsEkJA==", "peer": true, "requires": {"@polkadot/keyring": "^12.6.2", "@polkadot/types": "11.0.3", "@polkadot/types-support": "11.0.3", "@polkadot/util": "^12.6.2", "@polkadot/util-crypto": "^12.6.2", "@polkadot/x-fetch": "^12.6.2", "@polkadot/x-global": "^12.6.2", "@polkadot/x-ws": "^12.6.2", "@substrate/connect": "0.8.10", "eventemitter3": "^5.0.1", "mock-socket": "^9.3.1", "nock": "^13.5.0", "tslib": "^2.6.2"}, "dependencies": {"@polkadot/x-global": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-global/-/x-global-12.6.2.tgz", "integrity": "sha512-a8d6m+PW98jmsYDtAWp88qS4dl8DyqUBsd0S+WgyfSMtpEXu6v9nXDgPZgwF5xdDvXhm+P0ZfVkVTnIGrScb5g==", "peer": true, "requires": {"tslib": "^2.6.2"}}}}, "@polkadot/types": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/types/-/types-11.0.3.tgz", "integrity": "sha512-7AGUaObaGKZntOzHX4IRKj+K1AjZpYBgrivnY73tqGCp7biByn8Ht6a74xFngGfay1yRrx7eS3NOeIpp4nKhrw==", "peer": true, "requires": {"@polkadot/keyring": "^12.6.2", "@polkadot/types-augment": "11.0.3", "@polkadot/types-codec": "11.0.3", "@polkadot/types-create": "11.0.3", "@polkadot/util": "^12.6.2", "@polkadot/util-crypto": "^12.6.2", "rxjs": "^7.8.1", "tslib": "^2.6.2"}}, "@polkadot/types-augment": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/types-augment/-/types-augment-11.0.3.tgz", "integrity": "sha512-/LXP/LDDr59ZigfeYXtYWrIr4qttYwlkEV7EMUnse5zYWAGH8mkras0Um7gX7kd/GndJAHvXAbbSdrduhg/uNA==", "peer": true, "requires": {"@polkadot/types": "11.0.3", "@polkadot/types-codec": "11.0.3", "@polkadot/util": "^12.6.2", "tslib": "^2.6.2"}}, "@polkadot/types-codec": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/types-codec/-/types-codec-11.0.3.tgz", "integrity": "sha512-WSYmacSfUnnspKpLbagng8zo84eXLOztCYSppmqLoCrwtQE0zg5P/jF18qIeozvmPh+I/HMordXKt34JPgI/6w==", "peer": true, "requires": {"@polkadot/util": "^12.6.2", "@polkadot/x-bigint": "^12.6.2", "tslib": "^2.6.2"}}, "@polkadot/types-create": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/types-create/-/types-create-11.0.3.tgz", "integrity": "sha512-/uK9BCaivd5kcqZZCeIOlN0pSFyIELk9VGVOM3HsitE6IQrOlYPrYe0RqrQhoU/kjNC5BHaWviYV3Hu3TlOJuw==", "peer": true, "requires": {"@polkadot/types-codec": "11.0.3", "@polkadot/util": "^12.6.2", "tslib": "^2.6.2"}}, "@polkadot/types-known": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/types-known/-/types-known-11.0.3.tgz", "integrity": "sha512-nuv/a32QYtZYCD7IMj+WKgcblgMJse2t3RuWO+ZtT/uscOmR7TjctkS2ayzSJDQ5wWCPGet1eIwYpJYfGFOPkw==", "peer": true, "requires": {"@polkadot/networks": "^12.6.2", "@polkadot/types": "11.0.3", "@polkadot/types-codec": "11.0.3", "@polkadot/types-create": "11.0.3", "@polkadot/util": "^12.6.2", "tslib": "^2.6.2"}}, "@polkadot/types-support": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@polkadot/types-support/-/types-support-11.0.3.tgz", "integrity": "sha512-U9EG48zbbx5Q8B3GgHav8+/8hNYDOTijvSnEhUWsshh8U0Z7sNCmjfD4y0zfEzxoJyP7PTad62lJXfhQtehEsQ==", "peer": true, "requires": {"@polkadot/util": "^12.6.2", "tslib": "^2.6.2"}}, "@polkadot/util": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/util/-/util-12.6.2.tgz", "integrity": "sha512-l8TubR7CLEY47240uki0TQzFvtnxFIO7uI/0GoWzpYD/O62EIAMRsuY01N4DuwgKq2ZWD59WhzsLYmA5K6ksdw==", "peer": true, "requires": {"@polkadot/x-bigint": "12.6.2", "@polkadot/x-global": "12.6.2", "@polkadot/x-textdecoder": "12.6.2", "@polkadot/x-textencoder": "12.6.2", "@types/bn.js": "^5.1.5", "bn.js": "^5.2.1", "tslib": "^2.6.2"}, "dependencies": {"@polkadot/x-global": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-global/-/x-global-12.6.2.tgz", "integrity": "sha512-a8d6m+PW98jmsYDtAWp88qS4dl8DyqUBsd0S+WgyfSMtpEXu6v9nXDgPZgwF5xdDvXhm+P0ZfVkVTnIGrScb5g==", "peer": true, "requires": {"tslib": "^2.6.2"}}}}, "@polkadot/util-crypto": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/util-crypto/-/util-crypto-12.6.2.tgz", "integrity": "sha512-FEWI/dJ7wDMNN1WOzZAjQoIcCP/3vz3wvAp5QQm+lOrzOLj0iDmaIGIcBkz8HVm3ErfSe/uKP0KS4jgV/ib+Mg==", "peer": true, "requires": {"@noble/curves": "^1.3.0", "@noble/hashes": "^1.3.3", "@polkadot/networks": "12.6.2", "@polkadot/util": "12.6.2", "@polkadot/wasm-crypto": "^7.3.2", "@polkadot/wasm-util": "^7.3.2", "@polkadot/x-bigint": "12.6.2", "@polkadot/x-randomvalues": "12.6.2", "@scure/base": "^1.1.5", "tslib": "^2.6.2"}}, "@polkadot/wasm-bridge": {"version": "7.3.2", "resolved": "https://registry.npmjs.org/@polkadot/wasm-bridge/-/wasm-bridge-7.3.2.tgz", "integrity": "sha512-AJEXChcf/nKXd5Q/YLEV5dXQMle3UNT7jcXYmIffZAo/KI394a+/24PaISyQjoNC0fkzS1Q8T5pnGGHmXiVz2g==", "peer": true, "requires": {"@polkadot/wasm-util": "7.3.2", "tslib": "^2.6.2"}}, "@polkadot/wasm-crypto": {"version": "7.3.2", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto/-/wasm-crypto-7.3.2.tgz", "integrity": "sha512-+neIDLSJ6jjVXsjyZ5oLSv16oIpwp+PxFqTUaZdZDoA2EyFRQB8pP7+qLsMNk+WJuhuJ4qXil/7XiOnZYZ+wxw==", "peer": true, "requires": {"@polkadot/wasm-bridge": "7.3.2", "@polkadot/wasm-crypto-asmjs": "7.3.2", "@polkadot/wasm-crypto-init": "7.3.2", "@polkadot/wasm-crypto-wasm": "7.3.2", "@polkadot/wasm-util": "7.3.2", "tslib": "^2.6.2"}}, "@polkadot/wasm-crypto-asmjs": {"version": "7.3.2", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto-asmjs/-/wasm-crypto-asmjs-7.3.2.tgz", "integrity": "sha512-QP5eiUqUFur/2UoF2KKKYJcesc71fXhQFLT3D4ZjG28Mfk2ZPI0QNRUfpcxVQmIUpV5USHg4geCBNuCYsMm20Q==", "peer": true, "requires": {"tslib": "^2.6.2"}}, "@polkadot/wasm-crypto-init": {"version": "7.3.2", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto-init/-/wasm-crypto-init-7.3.2.tgz", "integrity": "sha512-FPq73zGmvZtnuJaFV44brze3Lkrki3b4PebxCy9Fplw8nTmisKo9Xxtfew08r0njyYh+uiJRAxPCXadkC9sc8g==", "peer": true, "requires": {"@polkadot/wasm-bridge": "7.3.2", "@polkadot/wasm-crypto-asmjs": "7.3.2", "@polkadot/wasm-crypto-wasm": "7.3.2", "@polkadot/wasm-util": "7.3.2", "tslib": "^2.6.2"}}, "@polkadot/wasm-crypto-wasm": {"version": "7.3.2", "resolved": "https://registry.npmjs.org/@polkadot/wasm-crypto-wasm/-/wasm-crypto-wasm-7.3.2.tgz", "integrity": "sha512-15wd0EMv9IXs5Abp1ZKpKKAVyZPhATIAHfKsyoWCEFDLSOA0/K0QGOxzrAlsrdUkiKZOq7uzSIgIDgW8okx2Mw==", "peer": true, "requires": {"@polkadot/wasm-util": "7.3.2", "tslib": "^2.6.2"}}, "@polkadot/wasm-util": {"version": "7.3.2", "resolved": "https://registry.npmjs.org/@polkadot/wasm-util/-/wasm-util-7.3.2.tgz", "integrity": "sha512-bmD+Dxo1lTZyZNxbyPE380wd82QsX+43mgCm40boyKrRppXEyQmWT98v/Poc7chLuskYb6X8IQ6lvvK2bGR4Tg==", "peer": true, "requires": {"tslib": "^2.6.2"}}, "@polkadot/x-bigint": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-bigint/-/x-bigint-12.6.2.tgz", "integrity": "sha512-HSIk60uFPX4GOFZSnIF7VYJz7WZA7tpFJsne7SzxOooRwMTWEtw3fUpFy5cYYOeLh17/kHH1Y7SVcuxzVLc74Q==", "peer": true, "requires": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "dependencies": {"@polkadot/x-global": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-global/-/x-global-12.6.2.tgz", "integrity": "sha512-a8d6m+PW98jmsYDtAWp88qS4dl8DyqUBsd0S+WgyfSMtpEXu6v9nXDgPZgwF5xdDvXhm+P0ZfVkVTnIGrScb5g==", "peer": true, "requires": {"tslib": "^2.6.2"}}}}, "@polkadot/x-fetch": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-fetch/-/x-fetch-12.6.2.tgz", "integrity": "sha512-8wM/Z9JJPWN1pzSpU7XxTI1ldj/AfC8hKioBlUahZ8gUiJaOF7K9XEFCrCDLis/A1BoOu7Ne6WMx/vsJJIbDWw==", "peer": true, "requires": {"@polkadot/x-global": "12.6.2", "node-fetch": "^3.3.2", "tslib": "^2.6.2"}, "dependencies": {"@polkadot/x-global": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-global/-/x-global-12.6.2.tgz", "integrity": "sha512-a8d6m+PW98jmsYDtAWp88qS4dl8DyqUBsd0S+WgyfSMtpEXu6v9nXDgPZgwF5xdDvXhm+P0ZfVkVTnIGrScb5g==", "peer": true, "requires": {"tslib": "^2.6.2"}}, "node-fetch": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-3.3.2.tgz", "integrity": "sha512-dRB78srN/l6gqWulah9SrxeYnxeddIG30+GOqK/9OlLVyLg3HPnr6SqOWTWOXKRwC2eGYCkZ59NNuSgvSrpgOA==", "peer": true, "requires": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}}}}, "@polkadot/x-global": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/@polkadot/x-global/-/x-global-10.4.2.tgz", "integrity": "sha512-g6GXHD/ykZvHap3M6wh19dO70Zm43l4jEhlxf5LtTo5/0/UporFCXr2YJYZqfbn9JbQwl1AU+NroYio+vtJdiA==", "requires": {"@babel/runtime": "^7.20.13"}}, "@polkadot/x-randomvalues": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-randomvalues/-/x-randomvalues-12.6.2.tgz", "integrity": "sha512-Vr8uG7rH2IcNJwtyf5ebdODMcr0XjoCpUbI91Zv6AlKVYOGKZlKLYJHIwpTaKKB+7KPWyQrk4Mlym/rS7v9feg==", "peer": true, "requires": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "dependencies": {"@polkadot/x-global": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-global/-/x-global-12.6.2.tgz", "integrity": "sha512-a8d6m+PW98jmsYDtAWp88qS4dl8DyqUBsd0S+WgyfSMtpEXu6v9nXDgPZgwF5xdDvXhm+P0ZfVkVTnIGrScb5g==", "peer": true, "requires": {"tslib": "^2.6.2"}}}}, "@polkadot/x-textdecoder": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-textdecoder/-/x-textdecoder-12.6.2.tgz", "integrity": "sha512-M1Bir7tYvNappfpFWXOJcnxUhBUFWkUFIdJSyH0zs5LmFtFdbKAeiDXxSp2Swp5ddOZdZgPac294/o2TnQKN1w==", "peer": true, "requires": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "dependencies": {"@polkadot/x-global": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-global/-/x-global-12.6.2.tgz", "integrity": "sha512-a8d6m+PW98jmsYDtAWp88qS4dl8DyqUBsd0S+WgyfSMtpEXu6v9nXDgPZgwF5xdDvXhm+P0ZfVkVTnIGrScb5g==", "peer": true, "requires": {"tslib": "^2.6.2"}}}}, "@polkadot/x-textencoder": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-textencoder/-/x-textencoder-12.6.2.tgz", "integrity": "sha512-4N+3UVCpI489tUJ6cv3uf0PjOHvgGp9Dl+SZRLgFGt9mvxnvpW/7+XBADRMtlG4xi5gaRK7bgl5bmY6OMDsNdw==", "peer": true, "requires": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2"}, "dependencies": {"@polkadot/x-global": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-global/-/x-global-12.6.2.tgz", "integrity": "sha512-a8d6m+PW98jmsYDtAWp88qS4dl8DyqUBsd0S+WgyfSMtpEXu6v9nXDgPZgwF5xdDvXhm+P0ZfVkVTnIGrScb5g==", "peer": true, "requires": {"tslib": "^2.6.2"}}}}, "@polkadot/x-ws": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-ws/-/x-ws-12.6.2.tgz", "integrity": "sha512-cGZWo7K5eRRQCRl2LrcyCYsrc3lRbTlixZh3AzgU8uX4wASVGRlNWi/Hf4TtHNe1ExCDmxabJzdIsABIfrr7xw==", "peer": true, "requires": {"@polkadot/x-global": "12.6.2", "tslib": "^2.6.2", "ws": "^8.15.1"}, "dependencies": {"@polkadot/x-global": {"version": "12.6.2", "resolved": "https://registry.npmjs.org/@polkadot/x-global/-/x-global-12.6.2.tgz", "integrity": "sha512-a8d6m+PW98jmsYDtAWp88qS4dl8DyqUBsd0S+WgyfSMtpEXu6v9nXDgPZgwF5xdDvXhm+P0ZfVkVTnIGrScb5g==", "peer": true, "requires": {"tslib": "^2.6.2"}}, "ws": {"version": "8.17.0", "resolved": "https://registry.npmjs.org/ws/-/ws-8.17.0.tgz", "integrity": "sha512-uJq6108EgZMAl20KagGkzCKfMEjxmKvZHG7Tlq0Z6nOky7YF7aq4mOx6xK8TJ/i1LeK4Qus7INktacctDgY8Ow==", "peer": true, "requires": {}}}}, "@scure/base": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/@scure/base/-/base-1.1.6.tgz", "integrity": "sha512-ok9AWwhcgYuGG3Zfhyqg+zwl+Wn5uE+dwC0NV/2qQkx4dABbb/bx96vWu8NSj+BNjjSjno+JRYRjle1jV08k3g==", "peer": true}, "@stablelib/aead": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/aead/-/aead-1.0.1.tgz", "integrity": "sha512-q39ik6sxGHewqtO0nP4BuSe3db5G1fEJE8ukvngS2gLkBXyy6E7pLubhbYgnkDFv6V8cWaxcE4Xn0t6LWcJkyg=="}, "@stablelib/binary": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/binary/-/binary-1.0.1.tgz", "integrity": "sha512-ClJWvmL6UBM/wjkvv/7m5VP3GMr9t0osr4yVgLZsLCOz4hGN9gIAFEqnJ0TsSMAN+n840nf2cHZnA5/KFqHC7Q==", "requires": {"@stablelib/int": "^1.0.1"}}, "@stablelib/bytes": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/bytes/-/bytes-1.0.1.tgz", "integrity": "sha512-<PERSON>re4Y4kdwuqL8BR2E9hV/R5sOrUj6NanZaZis0V6lX5yzqC3hBuVSDXUIBqQv/sCpmuWRiHLwqiT1pqqjuBXoQ=="}, "@stablelib/chacha": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/chacha/-/chacha-1.0.1.tgz", "integrity": "sha512-Pmlrswzr0pBzDofdFuVe1q7KdsHKhhU24e8gkEwnTGOmlC7PADzLVxGdn2PoNVBBabdg0l/IfLKg6sHAbTQugg==", "requires": {"@stablelib/binary": "^1.0.1", "@stablelib/wipe": "^1.0.1"}}, "@stablelib/chacha20poly1305": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/chacha20poly1305/-/chacha20poly1305-1.0.1.tgz", "integrity": "sha512-MmViqnqHd1ymwjOQfghRKw2R/jMIGT3wySN7cthjXCBdO+qErNPUBnRzqNpnvIwg7JBCg3LdeCZZO4de/yEhVA==", "requires": {"@stablelib/aead": "^1.0.1", "@stablelib/binary": "^1.0.1", "@stablelib/chacha": "^1.0.1", "@stablelib/constant-time": "^1.0.1", "@stablelib/poly1305": "^1.0.1", "@stablelib/wipe": "^1.0.1"}}, "@stablelib/constant-time": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/constant-time/-/constant-time-1.0.1.tgz", "integrity": "sha512-tNOs3uD0vSJcK6z1fvef4Y+buN7DXhzHDPqRLSXUel1UfqMB1PWNsnnAezrKfEwTLpN0cGH2p9NNjs6IqeD0eg=="}, "@stablelib/ed25519": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/@stablelib/ed25519/-/ed25519-1.0.3.tgz", "integrity": "sha512-puIMWaX9QlRsbhxfDc5i+mNPMY+0TmQEskunY1rZEBPi1acBCVQAhnsk/1Hk50DGPtVsZtAWQg4NHGlVaO9Hqg==", "requires": {"@stablelib/random": "^1.0.2", "@stablelib/sha512": "^1.0.1", "@stablelib/wipe": "^1.0.1"}}, "@stablelib/hash": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/hash/-/hash-1.0.1.tgz", "integrity": "sha512-eTPJc/stDkdtOcrNMZ6mcMK1e6yBbqRBaNW55XA1jU8w/7QdnCF0CmMmOD1m7VSkBR44PWrMHU2l6r8YEQHMgg=="}, "@stablelib/hkdf": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/hkdf/-/hkdf-1.0.1.tgz", "integrity": "sha512-SBEHYE16ZXlHuaW5RcGk533YlBj4grMeg5TooN80W3NpcHRtLZLLXvKyX0qcRFxf+BGDobJLnwkvgEwHIDBR6g==", "requires": {"@stablelib/hash": "^1.0.1", "@stablelib/hmac": "^1.0.1", "@stablelib/wipe": "^1.0.1"}}, "@stablelib/hmac": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/hmac/-/hmac-1.0.1.tgz", "integrity": "sha512-V2APD9NSnhVpV/QMYgCVMIYKiYG6LSqw1S65wxVoirhU/51ACio6D4yDVSwMzuTJXWZoVHbDdINioBwKy5kVmA==", "requires": {"@stablelib/constant-time": "^1.0.1", "@stablelib/hash": "^1.0.1", "@stablelib/wipe": "^1.0.1"}}, "@stablelib/int": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/int/-/int-1.0.1.tgz", "integrity": "sha512-byr69X/sDtDiIjIV6m4roLVWnNNlRGzsvxw+agj8CIEazqWGOQp2dTYgQhtyVXV9wpO6WyXRQUzLV/JRNumT2w=="}, "@stablelib/keyagreement": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/keyagreement/-/keyagreement-1.0.1.tgz", "integrity": "sha512-VKL6xBwgJnI6l1jKrBAfn265cspaWBPAPEc62VBQrWHLqVgNRE09gQ/AnOEyKUWrrqfD+xSQ3u42gJjLDdMDQg==", "requires": {"@stablelib/bytes": "^1.0.1"}}, "@stablelib/poly1305": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/poly1305/-/poly1305-1.0.1.tgz", "integrity": "sha512-1HlG3oTSuQDOhSnLwJRKeTRSAdFNVB/1djy2ZbS35rBSJ/PFqx9cf9qatinWghC2UbfOYD8AcrtbUQl8WoxabA==", "requires": {"@stablelib/constant-time": "^1.0.1", "@stablelib/wipe": "^1.0.1"}}, "@stablelib/random": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/@stablelib/random/-/random-1.0.2.tgz", "integrity": "sha512-rIsE83Xpb7clHPVRlBj8qNe5L8ISQOzjghYQm/dZ7VaM2KHYwMW5adjQjrzTZCchFnNCNhkwtnOBa9HTMJCI8w==", "requires": {"@stablelib/binary": "^1.0.1", "@stablelib/wipe": "^1.0.1"}}, "@stablelib/sha256": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/sha256/-/sha256-1.0.1.tgz", "integrity": "sha512-GIIH3e6KH+91FqGV42Kcj71Uefd/QEe7Dy42sBTeqppXV95ggCcxLTk39bEr+lZfJmp+ghsR07J++ORkRELsBQ==", "requires": {"@stablelib/binary": "^1.0.1", "@stablelib/hash": "^1.0.1", "@stablelib/wipe": "^1.0.1"}}, "@stablelib/sha512": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/sha512/-/sha512-1.0.1.tgz", "integrity": "sha512-13gl/iawHV9zvDKciLo1fQ8Bgn2Pvf7OV6amaRVKiq3pjQ3UmEpXxWiAfV8tYjUpeZroBxtyrwtdooQT/i3hzw==", "requires": {"@stablelib/binary": "^1.0.1", "@stablelib/hash": "^1.0.1", "@stablelib/wipe": "^1.0.1"}}, "@stablelib/wipe": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@stablelib/wipe/-/wipe-1.0.1.tgz", "integrity": "sha512-WfqfX/eXGiAd3RJe4VU2snh/ZPwtSjLG4ynQ/vYzvghTh7dHFcI1wl+nrkWG6lGhukOxOsUHfv8dUXr58D0ayg=="}, "@stablelib/x25519": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/@stablelib/x25519/-/x25519-1.0.3.tgz", "integrity": "sha512-KnTbKmUhPhHavzobclVJQG5kuivH+qDLpe84iRqX3CLrKp881cF160JvXJ+hjn1aMyCwYOKeIZefIH/P5cJoRw==", "requires": {"@stablelib/keyagreement": "^1.0.1", "@stablelib/random": "^1.0.2", "@stablelib/wipe": "^1.0.1"}}, "@substrate/connect": {"version": "0.8.10", "resolved": "https://registry.npmjs.org/@substrate/connect/-/connect-0.8.10.tgz", "integrity": "sha512-DIyQ13DDlXqVFnLV+S6/JDgiGowVRRrh18kahieJxhgvzcWicw5eLc6jpfQ0moVVLBYkO7rctB5Wreldwpva8w==", "optional": true, "peer": true, "requires": {"@substrate/connect-extension-protocol": "^2.0.0", "@substrate/connect-known-chains": "^1.1.4", "@substrate/light-client-extension-helpers": "^0.0.6", "smoldot": "2.0.22"}}, "@substrate/connect-extension-protocol": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@substrate/connect-extension-protocol/-/connect-extension-protocol-2.0.0.tgz", "integrity": "sha512-nKu8pDrE3LNCEgJjZe1iGXzaD6OSIDD4Xzz/yo4KO9mQ6LBvf49BVrt4qxBFGL6++NneLiWUZGoh+VSd4PyVIg==", "optional": true, "peer": true}, "@substrate/connect-known-chains": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/@substrate/connect-known-chains/-/connect-known-chains-1.1.4.tgz", "integrity": "sha512-iT+BdKqvKl/uBLd8BAJysFM1BaMZXRkaXBP2B7V7ob/EyNs5h0EMhTVbO6MJxV/IEOg5OKsyl6FUqQK7pKnqyw==", "optional": true, "peer": true}, "@substrate/light-client-extension-helpers": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/@substrate/light-client-extension-helpers/-/light-client-extension-helpers-0.0.6.tgz", "integrity": "sha512-girltEuxQ1BvkJWmc8JJlk4ZxnlGXc/wkLcNguhY+UoDEMBK0LsdtfzQKIfrIehi4QdeSBlFEFBoI4RqPmsZzA==", "optional": true, "peer": true, "requires": {"@polkadot-api/json-rpc-provider": "0.0.1", "@polkadot-api/json-rpc-provider-proxy": "0.0.1", "@polkadot-api/observable-client": "0.1.0", "@polkadot-api/substrate-client": "0.0.1", "@substrate/connect-extension-protocol": "^2.0.0", "@substrate/connect-known-chains": "^1.1.4", "rxjs": "^7.8.1"}}, "@substrate/smoldot-light": {"version": "0.7.9", "resolved": "https://registry.npmjs.org/@substrate/smoldot-light/-/smoldot-light-0.7.9.tgz", "integrity": "sha512-HP8iP7sFYlpSgjjbo0lqHyU+gu9lL2hbDNce6dWk5/10mFFF9jKIFGfui4zCecUY808o/Go9pan/31kMJoLbug==", "optional": true, "requires": {"pako": "^2.0.4", "ws": "^8.8.1"}, "dependencies": {"ws": {"version": "8.17.0", "resolved": "https://registry.npmjs.org/ws/-/ws-8.17.0.tgz", "integrity": "sha512-uJq6108EgZMAl20KagGkzCKfMEjxmKvZHG7Tlq0Z6nOky7YF7aq4mOx6xK8TJ/i1LeK4Qus7INktacctDgY8Ow==", "optional": true, "requires": {}}}}, "@substrate/ss58-registry": {"version": "1.47.0", "resolved": "https://registry.npmjs.org/@substrate/ss58-registry/-/ss58-registry-1.47.0.tgz", "integrity": "sha512-6kuIJedRcisUJS2pgksEH2jZf3hfSIVzqtFzs/AyjTW3ETbMg5q1Bb7VWa0WYaT6dTrEXp/6UoXM5B9pSIUmcw=="}, "@types/bn.js": {"version": "5.1.5", "resolved": "https://registry.npmjs.org/@types/bn.js/-/bn.js-5.1.5.tgz", "integrity": "sha512-V46N0zwKRF5Q00AZ6hWtN0T8gGmDUaUzLWQvHFo5yThtVwK/VCenFY3wXVbOvNfajEpsTfQM4IN9k/d6gUVX3A==", "requires": {"@types/node": "*"}}, "@types/node": {"version": "20.12.12", "resolved": "https://registry.npmjs.org/@types/node/-/node-20.12.12.tgz", "integrity": "sha512-eWLDGF/FOSPtAvEqeRAQ4C8LSA7M1I7i0ky1I8U7kD1J5ITyW3AsRhQrKVoWf5pFKZ2kILsEGJhsI9r93PYnOw==", "requires": {"undici-types": "~5.26.4"}}, "@types/node-fetch": {"version": "2.6.11", "resolved": "https://registry.npmjs.org/@types/node-fetch/-/node-fetch-2.6.11.tgz", "integrity": "sha512-24xFj9R5+rfQJLRyM56qh+wnVSYhyXC2tkoBndtY0U+vubqNsYXGjufB2nn8Q6gt0LrARwL6UBtMCSVCwl4B1g==", "requires": {"@types/node": "*", "form-data": "^4.0.0"}}, "@types/prop-types": {"version": "15.7.5", "peer": true}, "@types/react": {"version": "18.2.6", "peer": true, "requires": {"@types/prop-types": "*", "@types/scheduler": "*", "csstype": "^3.0.2"}}, "@types/scheduler": {"version": "0.16.2", "peer": true}, "@types/trusted-types": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/@types/trusted-types/-/trusted-types-2.0.3.tgz", "integrity": "sha512-NfQ4gyz38SL8sDNrSixxU2Os1a5xcdFxipAFxYEuLUlvU2uDwS4NUpsImcf1//SlWItCVMMLiylsxbmNMToV/g=="}, "@types/websocket": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/@types/websocket/-/websocket-1.0.10.tgz", "integrity": "sha512-svjGZvPB7EzuYS94cI7a+qhwgGU1y89wUgjT6E2wVUfmAGIvRfT7obBvRtnhXCSsoMdlG4gBFGE7MfkIXZLoww==", "requires": {"@types/node": "*"}}, "@walletconnect/core": {"version": "2.8.2", "resolved": "https://registry.npmjs.org/@walletconnect/core/-/core-2.8.2.tgz", "integrity": "sha512-24ygQe1RIjcBQEh+I1KlhpLgKONrL0ll+2HIoLlSs/NLvsvNT7Ib2ku+ded8o82Pgji3DSSl5h0RNknkw2L5pQ==", "requires": {"@walletconnect/heartbeat": "1.2.1", "@walletconnect/jsonrpc-provider": "1.0.13", "@walletconnect/jsonrpc-types": "1.0.3", "@walletconnect/jsonrpc-utils": "1.0.8", "@walletconnect/jsonrpc-ws-connection": "^1.0.11", "@walletconnect/keyvaluestorage": "^1.0.2", "@walletconnect/logger": "^2.0.1", "@walletconnect/relay-api": "^1.0.9", "@walletconnect/relay-auth": "^1.0.4", "@walletconnect/safe-json": "^1.0.2", "@walletconnect/time": "^1.0.2", "@walletconnect/types": "2.8.2", "@walletconnect/utils": "2.8.2", "events": "^3.3.0", "lodash.isequal": "4.5.0", "uint8arrays": "^3.1.0"}}, "@walletconnect/environment": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@walletconnect/environment/-/environment-1.0.1.tgz", "integrity": "sha512-T426LLZtHj8e8rYnKfzsw1aG6+M0BT1ZxayMdv/p8yM0MU+eJDISqNY3/bccxRr4LrF9csq02Rhqt08Ibl0VRg==", "requires": {"tslib": "1.14.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "@walletconnect/ethereum-provider": {"version": "2.8.2", "resolved": "https://registry.npmjs.org/@walletconnect/ethereum-provider/-/ethereum-provider-2.8.2.tgz", "integrity": "sha512-MWhjSSbbT60vYdcLuhmTFI6t/UYHIJzILeKuKQnv4j6Pt4X4SzG2uuL7eP9PL5nHBdZxMeA0mwGn/Yhs8mgcng==", "requires": {"@walletconnect/jsonrpc-http-connection": "^1.0.7", "@walletconnect/jsonrpc-provider": "^1.0.13", "@walletconnect/jsonrpc-types": "^1.0.3", "@walletconnect/jsonrpc-utils": "^1.0.8", "@walletconnect/sign-client": "2.8.2", "@walletconnect/types": "2.8.2", "@walletconnect/universal-provider": "2.8.2", "@walletconnect/utils": "2.8.2", "events": "^3.3.0"}}, "@walletconnect/events": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@walletconnect/events/-/events-1.0.1.tgz", "integrity": "sha512-NPTqaoi0oPBVNuLv7qPaJazmGHs5JGyO8eEAk5VGKmJzDR7AHzD4k6ilox5kxk1iwiOnFopBOOMLs86Oa76HpQ==", "requires": {"keyvaluestorage-interface": "^1.0.0", "tslib": "1.14.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "@walletconnect/heartbeat": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/@walletconnect/heartbeat/-/heartbeat-1.2.1.tgz", "integrity": "sha512-yVzws616xsDLJxuG/28FqtZ5rzrTA4gUjdEMTbWB5Y8V1XHRmqq4efAxCw5ie7WjbXFSUyBHaWlMR+2/CpQC5Q==", "requires": {"@walletconnect/events": "^1.0.1", "@walletconnect/time": "^1.0.2", "tslib": "1.14.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "@walletconnect/jsonrpc-http-connection": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/@walletconnect/jsonrpc-http-connection/-/jsonrpc-http-connection-1.0.7.tgz", "integrity": "sha512-qlfh8fCfu8LOM9JRR9KE0s0wxP6ZG9/Jom8M0qsoIQeKF3Ni0FyV4V1qy/cc7nfI46SLQLSl4tgWSfLiE1swyQ==", "requires": {"@walletconnect/jsonrpc-utils": "^1.0.6", "@walletconnect/safe-json": "^1.0.1", "cross-fetch": "^3.1.4", "tslib": "1.14.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "@walletconnect/jsonrpc-provider": {"version": "1.0.13", "resolved": "https://registry.npmjs.org/@walletconnect/jsonrpc-provider/-/jsonrpc-provider-1.0.13.tgz", "integrity": "sha512-K73EpThqHnSR26gOyNEL+acEex3P7VWZe6KE12ZwKzAt2H4e5gldZHbjsu2QR9cLeJ8AXuO7kEMOIcRv1QEc7g==", "requires": {"@walletconnect/jsonrpc-utils": "^1.0.8", "@walletconnect/safe-json": "^1.0.2", "tslib": "1.14.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "@walletconnect/jsonrpc-types": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/@walletconnect/jsonrpc-types/-/jsonrpc-types-1.0.3.tgz", "integrity": "sha512-iIQ8hboBl3o5ufmJ8cuduGad0CQm3ZlsHtujv9Eu16xq89q+BG7Nh5VLxxUgmtpnrePgFkTwXirCTkwJH1v+Yw==", "requires": {"keyvaluestorage-interface": "^1.0.0", "tslib": "1.14.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "@walletconnect/jsonrpc-utils": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/@walletconnect/jsonrpc-utils/-/jsonrpc-utils-1.0.8.tgz", "integrity": "sha512-vdeb03bD8VzJUL6ZtzRYsFMq1eZQcM3EAzT0a3st59dyLfJ0wq+tKMpmGH7HlB7waD858UWgfIcudbPFsbzVdw==", "requires": {"@walletconnect/environment": "^1.0.1", "@walletconnect/jsonrpc-types": "^1.0.3", "tslib": "1.14.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "@walletconnect/jsonrpc-ws-connection": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/@walletconnect/jsonrpc-ws-connection/-/jsonrpc-ws-connection-1.0.11.tgz", "integrity": "sha512-TiFJ6saasKXD+PwGkm5ZGSw0837nc6EeFmurSPgIT/NofnOV4Tv7CVJqGQN0rQYoJUSYu21cwHNYaFkzNpUN+w==", "requires": {"@walletconnect/jsonrpc-utils": "^1.0.6", "@walletconnect/safe-json": "^1.0.2", "events": "^3.3.0", "tslib": "1.14.1", "ws": "^7.5.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "ws": {"version": "7.5.9", "resolved": "https://registry.npmjs.org/ws/-/ws-7.5.9.tgz", "integrity": "sha512-F+P9Jil7UiSKSkppIiD94dN07AwvFixvLIj1Og1Rl9GGMuNipJnV9JzjD6XuqmAeiswGvUmNLjr5cFuXwNS77Q==", "requires": {}}}}, "@walletconnect/keyvaluestorage": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/@walletconnect/keyvaluestorage/-/keyvaluestorage-1.0.2.tgz", "integrity": "sha512-U/nNG+VLWoPFdwwKx0oliT4ziKQCEoQ27L5Hhw8YOFGA2Po9A9pULUYNWhDgHkrb0gYDNt//X7wABcEWWBd3FQ==", "requires": {"safe-json-utils": "^1.1.1", "tslib": "1.14.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "@walletconnect/logger": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/@walletconnect/logger/-/logger-2.0.1.tgz", "integrity": "sha512-SsTKdsgWm+oDTBeNE/zHxxr5eJfZmE9/5yp/Ku+zJtcTAjELb3DXueWkDXmE9h8uHIbJzIb5wj5lPdzyrjT6hQ==", "requires": {"pino": "7.11.0", "tslib": "1.14.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "@walletconnect/modal": {"version": "2.5.2", "resolved": "https://registry.npmjs.org/@walletconnect/modal/-/modal-2.5.2.tgz", "integrity": "sha512-vMLAQFjbMeXZ3+ojb+0OmMRpXCg92vCWJS2t3pF6XyxZrp/qxB9W87HwP7q6ecJtePM1Snil5QlpXipprqzr9g==", "requires": {"@walletconnect/modal-core": "2.5.2", "@walletconnect/modal-ui": "2.5.2"}}, "@walletconnect/modal-core": {"version": "2.5.2", "resolved": "https://registry.npmjs.org/@walletconnect/modal-core/-/modal-core-2.5.2.tgz", "integrity": "sha512-meYjouZxAik0peyhxDUTRY77uu/r4tLe1QoJp/Ra3brHD0i93uwX5U8RlBNDLGQhLGIraZl6xNANcxHGRHFSuQ==", "requires": {"buffer": "6.0.3", "valtio": "1.10.5"}}, "@walletconnect/modal-ui": {"version": "2.5.2", "resolved": "https://registry.npmjs.org/@walletconnect/modal-ui/-/modal-ui-2.5.2.tgz", "integrity": "sha512-ZbHsFP+LWvyJ3wwJf3nJKkwqMOHpJ5ECnAZgopMX+hp/bS+4JEeCzUy1StmzyriT6RImLFRQkI6Zas/NetaUnw==", "requires": {"@walletconnect/modal-core": "2.5.2", "lit": "2.7.5", "motion": "10.16.2", "qrcode": "1.5.3"}}, "@walletconnect/relay-api": {"version": "1.0.9", "resolved": "https://registry.npmjs.org/@walletconnect/relay-api/-/relay-api-1.0.9.tgz", "integrity": "sha512-Q3+rylJOqRkO1D9Su0DPE3mmznbAalYapJ9qmzDgK28mYF9alcP3UwG/og5V7l7CFOqzCLi7B8BvcBUrpDj0Rg==", "requires": {"@walletconnect/jsonrpc-types": "^1.0.2", "tslib": "1.14.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "@walletconnect/relay-auth": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/@walletconnect/relay-auth/-/relay-auth-1.0.4.tgz", "integrity": "sha512-kKJcS6+WxYq5kshpPaxGHdwf5y98ZwbfuS4EE/NkQzqrDFm5Cj+dP8LofzWvjrrLkZq7Afy7WrQMXdLy8Sx7HQ==", "requires": {"@stablelib/ed25519": "^1.0.2", "@stablelib/random": "^1.0.1", "@walletconnect/safe-json": "^1.0.1", "@walletconnect/time": "^1.0.2", "tslib": "1.14.1", "uint8arrays": "^3.0.0"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "@walletconnect/safe-json": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/@walletconnect/safe-json/-/safe-json-1.0.2.tgz", "integrity": "sha512-Ogb7I27kZ3LPC3ibn8ldyUr5544t3/STow9+lzz7Sfo808YD7SBWk7SAsdBFlYgP2zDRy2hS3sKRcuSRM0OTmA==", "requires": {"tslib": "1.14.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "@walletconnect/sign-client": {"version": "2.8.2", "resolved": "https://registry.npmjs.org/@walletconnect/sign-client/-/sign-client-2.8.2.tgz", "integrity": "sha512-TcViLWHE55SqYeFPDny1JTuktMOszffzYK5R22VAGOeHW3PhUqJoMcMXUEhSHuEeLcvGT1F25CiyNOWo2url/g==", "requires": {"@walletconnect/core": "2.8.2", "@walletconnect/events": "^1.0.1", "@walletconnect/heartbeat": "1.2.1", "@walletconnect/jsonrpc-utils": "1.0.8", "@walletconnect/logger": "^2.0.1", "@walletconnect/time": "^1.0.2", "@walletconnect/types": "2.8.2", "@walletconnect/utils": "2.8.2", "events": "^3.3.0"}}, "@walletconnect/time": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/@walletconnect/time/-/time-1.0.2.tgz", "integrity": "sha512-uzdd9woDcJ1AaBZRhqy5rNC9laqWGErfc4dxA9a87mPdKOgWMD85mcFo9dIYIts/Jwocfwn07EC6EzclKubk/g==", "requires": {"tslib": "1.14.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "@walletconnect/types": {"version": "2.8.2", "resolved": "https://registry.npmjs.org/@walletconnect/types/-/types-2.8.2.tgz", "integrity": "sha512-TzFGL2+SEU5jTt/i+kOZhcboqxhkDL+HaFcVl5+CVS6i67dYCjHu2AUkx6NARRmVzJZV5tTIjSDnpPXARoJaZA==", "requires": {"@walletconnect/events": "^1.0.1", "@walletconnect/heartbeat": "1.2.1", "@walletconnect/jsonrpc-types": "1.0.3", "@walletconnect/keyvaluestorage": "^1.0.2", "@walletconnect/logger": "^2.0.1", "events": "^3.3.0"}}, "@walletconnect/universal-provider": {"version": "2.8.2", "resolved": "https://registry.npmjs.org/@walletconnect/universal-provider/-/universal-provider-2.8.2.tgz", "integrity": "sha512-BguG0gp5r3xq49+A201OAx81aqLI6Et0S7derGwBRN8BaNlSqlIY+/hRSnQohjt0Gy57ZikACI9nSNPWNl8nHw==", "requires": {"@walletconnect/jsonrpc-http-connection": "^1.0.7", "@walletconnect/jsonrpc-provider": "1.0.13", "@walletconnect/jsonrpc-types": "^1.0.2", "@walletconnect/jsonrpc-utils": "^1.0.7", "@walletconnect/logger": "^2.0.1", "@walletconnect/sign-client": "2.8.2", "@walletconnect/types": "2.8.2", "@walletconnect/utils": "2.8.2", "events": "^3.3.0"}}, "@walletconnect/utils": {"version": "2.8.2", "resolved": "https://registry.npmjs.org/@walletconnect/utils/-/utils-2.8.2.tgz", "integrity": "sha512-VyOL1iuE7X7BorBlyB5t/FCZsFMihF5JO7gNjpharIZMRoIjiXv2SVKU+qbPT/LyrGswJ0Fkjia+hXUb3tGaWw==", "requires": {"@stablelib/chacha20poly1305": "1.0.1", "@stablelib/hkdf": "1.0.1", "@stablelib/random": "^1.0.2", "@stablelib/sha256": "1.0.1", "@stablelib/x25519": "^1.0.3", "@walletconnect/relay-api": "^1.0.9", "@walletconnect/safe-json": "^1.0.2", "@walletconnect/time": "^1.0.2", "@walletconnect/types": "2.8.2", "@walletconnect/window-getters": "^1.0.1", "@walletconnect/window-metadata": "^1.0.1", "detect-browser": "5.3.0", "query-string": "7.1.3", "uint8arrays": "^3.1.0"}}, "@walletconnect/window-getters": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@walletconnect/window-getters/-/window-getters-1.0.1.tgz", "integrity": "sha512-vHp+HqzGxORPAN8gY03qnbTMnhqIwjeRJNOMOAzePRg4xVEEE2WvYsI9G2NMjOknA8hnuYbU3/hwLcKbjhc8+Q==", "requires": {"tslib": "1.14.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "@walletconnect/window-metadata": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@walletconnect/window-metadata/-/window-metadata-1.0.1.tgz", "integrity": "sha512-9koTqyGrM2cqFRW517BPY/iEtUDx2r1+Pwwu5m7sJ7ka79wi3EyqhqcICk/yDmv6jAS1rjKgTKXlEhanYjijcA==", "requires": {"@walletconnect/window-getters": "^1.0.1", "tslib": "1.14.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="}, "ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "requires": {"color-convert": "^2.0.1"}}, "asynckit": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="}, "atomic-sleep": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/atomic-sleep/-/atomic-sleep-1.0.0.tgz", "integrity": "sha512-kNOjDqAh7px0XWNI+4QbzoiR/nTkHAWNud2uvnJquD1/x5a7EQZMJT0AczqK0Qn67oY/TTQ1LbUKajZpp3I9tQ=="}, "base64-js": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA=="}, "bech32": {"version": "1.1.4"}, "bn.js": {"version": "5.2.1"}, "brorand": {"version": "1.1.0"}, "buffer": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/buffer/-/buffer-6.0.3.tgz", "integrity": "sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==", "requires": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "bufferutil": {"version": "4.0.7", "requires": {"node-gyp-build": "^4.3.0"}}, "camelcase": {"version": "5.3.1", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz", "integrity": "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg=="}, "cliui": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/cliui/-/cliui-6.0.0.tgz", "integrity": "sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ==", "requires": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^6.2.0"}}, "color-convert": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="}, "combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "requires": {"delayed-stream": "~1.0.0"}}, "cross-fetch": {"version": "3.1.6", "resolved": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.6.tgz", "integrity": "sha512-riRvo06crlE8HiqOwIpQhxwdOk4fOeR7FVM/wXoxchFEqMNUjvbs3bfo4OTgMEMHzppd4DxFBDbyySj8Cv781g==", "requires": {"node-fetch": "^2.6.11"}}, "csstype": {"version": "3.1.1", "peer": true}, "d": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/d/-/d-1.0.2.tgz", "integrity": "sha512-MOqHvMWF9/9MX6nza0KgvFH4HpMU0EF5uUDXqX/BtxtU8NfB0QzRtJ8Oe/6SuS4kbhyzVJwjd97EA4PKrzJ8bw==", "requires": {"es5-ext": "^0.10.64", "type": "^2.7.2"}}, "data-uri-to-buffer": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-4.0.1.tgz", "integrity": "sha512-0R9ikRb668HB7QDxT1vkpuUBtqc53YyAwMwGeUFKRojY/NWKvdZ+9UYtRfGmhqNbRkTSVpMbmyhXipFFv2cb/A=="}, "debug": {"version": "4.3.4", "resolved": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "requires": {"ms": "2.1.2"}}, "decamelize": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz", "integrity": "sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA=="}, "decode-uri-component": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/decode-uri-component/-/decode-uri-component-0.2.2.tgz", "integrity": "sha512-FqUYQ+8o158GyGTrMFJms9qh3CqTKvAqgqsTnkLI8sKu0028orqBhxNMFkFen0zGyg6epACD32pjVk58ngIErQ=="}, "delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ=="}, "detect-browser": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/detect-browser/-/detect-browser-5.3.0.tgz", "integrity": "sha512-53rsFbGdwMwlF7qvCt0ypLM5V5/Mbl0szB7GPN8y9NCcbknYOeVVXdrXEq+90IwAfrrzt6Hd+u2E2ntakICU8w=="}, "dijkstrajs": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/dijkstrajs/-/dijkstrajs-1.0.3.tgz", "integrity": "sha512-qiSlmBq9+BCdCA/L46dw8Uy93mloxsPSbwnm5yrKn2vMPiy8KyAskTF6zuV/j5BMsmOGZDPs7KjU+mjb670kfA=="}, "duplexify": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/duplexify/-/duplexify-4.1.2.tgz", "integrity": "sha512-fz3OjcNCHmRP12MJoZMPglx8m4rrFP8rovnk4vT8Fs+aonZoCwGg10dSsQsfP/E62eZcPTMSMP6686fu9Qlqtw==", "requires": {"end-of-stream": "^1.4.1", "inherits": "^2.0.3", "readable-stream": "^3.1.1", "stream-shift": "^1.0.0"}}, "ed2curve": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/ed2curve/-/ed2curve-0.3.0.tgz", "integrity": "sha512-8w2fmmq3hv9rCrcI7g9hms2pMunQr1JINfcjwR9tAyZqhtyaMN991lF/ZfHfr5tzZQ8c7y7aBgZbjfbd0fjFwQ==", "requires": {"tweetnacl": "1.x.x"}}, "elliptic": {"version": "6.5.4", "requires": {"bn.js": "^4.11.9", "brorand": "^1.1.0", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.1", "inherits": "^2.0.4", "minimalistic-assert": "^1.0.1", "minimalistic-crypto-utils": "^1.0.1"}, "dependencies": {"bn.js": {"version": "4.12.0"}}}, "emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="}, "encode-utf8": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/encode-utf8/-/encode-utf8-1.0.3.tgz", "integrity": "sha512-ucAnuBEhUK4boH2HjVYG5Q2mQyPorvv0u/ocS+zhdw0S8AlHYY+GOFhP1Gio5z4icpP2ivFSvhtFjQi8+T9ppw=="}, "end-of-stream": {"version": "1.4.4", "resolved": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz", "integrity": "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==", "requires": {"once": "^1.4.0"}}, "es5-ext": {"version": "0.10.64", "resolved": "https://registry.npmjs.org/es5-ext/-/es5-ext-0.10.64.tgz", "integrity": "sha512-p2snDhiLaXe6dahss1LddxqEm+SkuDvV8dnIQG0MWjyHpcMNfXKPE+/Cc0y+PhxJX3A4xGNeFCj5oc0BUh6deg==", "requires": {"es6-iterator": "^2.0.3", "es6-symbol": "^3.1.3", "esniff": "^2.0.1", "next-tick": "^1.1.0"}}, "es6-iterator": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/es6-iterator/-/es6-iterator-2.0.3.tgz", "integrity": "sha512-zw4SRzoUkd+cl+ZoE15A9o1oQd920Bb0iOJMQkQhl3jNc03YqVjAhG7scf9C5KWRU/R13Orf588uCC6525o02g==", "requires": {"d": "1", "es5-ext": "^0.10.35", "es6-symbol": "^3.1.1"}}, "es6-symbol": {"version": "3.1.4", "resolved": "https://registry.npmjs.org/es6-symbol/-/es6-symbol-3.1.4.tgz", "integrity": "sha512-U9bFFjX8tFiATgtkJ1zg25+KviIXpgRvRHS8sau3GfhVzThRQrOeksPeT0BWW2MNZs1OEWJ1DPXOQMn0KKRkvg==", "requires": {"d": "^1.0.2", "ext": "^1.7.0"}}, "esniff": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/esniff/-/esniff-2.0.1.tgz", "integrity": "sha512-kTUIGKQ/mDPFoJ0oVfcmyJn4iBDRptjNVIzwIFR7tqWXdVI9xfA2RMwY/gbSpJG3lkdWNEjLap/NqVHZiJsdfg==", "requires": {"d": "^1.0.1", "es5-ext": "^0.10.62", "event-emitter": "^0.3.5", "type": "^2.7.2"}}, "ethers": {"version": "5.7.2", "requires": {"@ethersproject/abi": "5.7.0", "@ethersproject/abstract-provider": "5.7.0", "@ethersproject/abstract-signer": "5.7.0", "@ethersproject/address": "5.7.0", "@ethersproject/base64": "5.7.0", "@ethersproject/basex": "5.7.0", "@ethersproject/bignumber": "5.7.0", "@ethersproject/bytes": "5.7.0", "@ethersproject/constants": "5.7.0", "@ethersproject/contracts": "5.7.0", "@ethersproject/hash": "5.7.0", "@ethersproject/hdnode": "5.7.0", "@ethersproject/json-wallets": "5.7.0", "@ethersproject/keccak256": "5.7.0", "@ethersproject/logger": "5.7.0", "@ethersproject/networks": "5.7.1", "@ethersproject/pbkdf2": "5.7.0", "@ethersproject/properties": "5.7.0", "@ethersproject/providers": "5.7.2", "@ethersproject/random": "5.7.0", "@ethersproject/rlp": "5.7.0", "@ethersproject/sha2": "5.7.0", "@ethersproject/signing-key": "5.7.0", "@ethersproject/solidity": "5.7.0", "@ethersproject/strings": "5.7.0", "@ethersproject/transactions": "5.7.0", "@ethersproject/units": "5.7.0", "@ethersproject/wallet": "5.7.0", "@ethersproject/web": "5.7.1", "@ethersproject/wordlists": "5.7.0"}}, "event-emitter": {"version": "0.3.5", "resolved": "https://registry.npmjs.org/event-emitter/-/event-emitter-0.3.5.tgz", "integrity": "sha512-D9rRn9y7kLPnJ+hMq7S/nhvoKwwvVJahBi2BPmx3bvbsEdK3W9ii8cBSGjP+72/LnM4n6fo3+dkCX5FeTQruXA==", "requires": {"d": "1", "es5-ext": "~0.10.14"}}, "eventemitter3": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-5.0.1.tgz", "integrity": "sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA=="}, "events": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/events/-/events-3.3.0.tgz", "integrity": "sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q=="}, "ext": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/ext/-/ext-1.7.0.tgz", "integrity": "sha512-6hxeJYaL110a9b5TEJSj0gojyHQAmA2ch5Os+ySCiA1QGdS697XWY1pzsrSjqA9LDEEgdB/KypIlR59RcLuHYw==", "requires": {"type": "^2.7.2"}}, "fast-redact": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/fast-redact/-/fast-redact-3.2.0.tgz", "integrity": "sha512-zaTadChr+NekyzallAMXATXLOR8MNx3zqpZ0MUF2aGf4EathnG0f32VLODNlY8IuGY3HoRO2L6/6fSzNsLaHIw=="}, "fetch-blob": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz", "integrity": "sha512-7yAQpD2UMJzLi1Dqv7qFYnPbaPx7ZfFK6PiIxQ4PfkGPyNyl2Ugx+a/umUonmKqjhM4DnfbMvdX6otXq83soQQ==", "requires": {"node-domexception": "^1.0.0", "web-streams-polyfill": "^3.0.3"}}, "filter-obj": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/filter-obj/-/filter-obj-1.1.0.tgz", "integrity": "sha512-8rXg1ZnX7xzy2NGDVkBVaAy+lSlPNwad13BtgSlLuxfIslyt5Vg64U7tFcCt4WS1R0hvtnQybT/IyCkGZ3DpXQ=="}, "find-up": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz", "integrity": "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==", "requires": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}}, "form-data": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/form-data/-/form-data-4.0.0.tgz", "integrity": "sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==", "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}}, "formdata-polyfill": {"version": "4.0.10", "resolved": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz", "integrity": "sha512-buewHzMvYL29jdeQTVILecSaZKnt/RJWjoZCF5OW60Z67/GmSLBkOFM7qh1PI3zFNtJbaZL5eQu1vLfazOwj4g==", "requires": {"fetch-blob": "^3.1.2"}}, "get-caller-file": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg=="}, "hash.js": {"version": "1.1.7", "requires": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "hey-listen": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/hey-listen/-/hey-listen-1.0.8.tgz", "integrity": "sha512-COpmrF2NOg4TBWUJ5UVyaCU2A88wEMkUPK4hNqyCkqHbxT92BbvfjoSozkAIIm6XhicGlJHhFdullInrdhwU8Q=="}, "hmac-drbg": {"version": "1.0.1", "requires": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}}, "ieee754": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA=="}, "inherits": {"version": "2.0.4"}, "is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg=="}, "is-typedarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz", "integrity": "sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA=="}, "js-sha3": {"version": "0.8.0"}, "json-stringify-safe": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "integrity": "sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA=="}, "keyvaluestorage-interface": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/keyvaluestorage-interface/-/keyvaluestorage-interface-1.0.0.tgz", "integrity": "sha512-8t6Q3TclQ4uZynJY9IGr2+SsIGwK9JHcO6ootkHCGA0CrQCRy+VkouYNO2xicET6b9al7QKzpebNow+gkpCL8g=="}, "lit": {"version": "2.7.5", "resolved": "https://registry.npmjs.org/lit/-/lit-2.7.5.tgz", "integrity": "sha512-i/cH7Ye6nBDUASMnfwcictBnsTN91+aBjXoTHF2xARghXScKxpD4F4WYI+VLXg9lqbMinDfvoI7VnZXjyHgdfQ==", "requires": {"@lit/reactive-element": "^1.6.0", "lit-element": "^3.3.0", "lit-html": "^2.7.0"}}, "lit-element": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/lit-element/-/lit-element-3.3.2.tgz", "integrity": "sha512-xXAeVWKGr4/njq0rGC9dethMnYCq5hpKYrgQZYTzawt9YQhMiXfD+T1RgrdY3NamOxwq2aXlb0vOI6e29CKgVQ==", "requires": {"@lit-labs/ssr-dom-shim": "^1.1.0", "@lit/reactive-element": "^1.3.0", "lit-html": "^2.7.0"}}, "lit-html": {"version": "2.7.4", "resolved": "https://registry.npmjs.org/lit-html/-/lit-html-2.7.4.tgz", "integrity": "sha512-/Jw+FBpeEN+z8X6PJva5n7+0MzCVAH2yypN99qHYYkq8bI+j7I39GH+68Z/MZD6rGKDK9RpzBw7CocfmHfq6+g==", "requires": {"@types/trusted-types": "^2.0.2"}}, "locate-path": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==", "requires": {"p-locate": "^4.1.0"}}, "lodash.isequal": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/lodash.isequal/-/lodash.isequal-4.5.0.tgz", "integrity": "sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ=="}, "mime-db": {"version": "1.52.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="}, "mime-types": {"version": "2.1.35", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "requires": {"mime-db": "1.52.0"}}, "minimalistic-assert": {"version": "1.0.1"}, "minimalistic-crypto-utils": {"version": "1.0.1"}, "mock-socket": {"version": "9.3.1", "resolved": "https://registry.npmjs.org/mock-socket/-/mock-socket-9.3.1.tgz", "integrity": "sha512-qxBgB7Qa2sEQgHFjj0dSigq7fX4k6Saisd5Nelwp2q8mlbAFh5dHV9JTTlF8viYJLSSWgMCZFUom8PJcMNBoJw=="}, "motion": {"version": "10.16.2", "resolved": "https://registry.npmjs.org/motion/-/motion-10.16.2.tgz", "integrity": "sha512-p+PurYqfUdcJZvtnmAqu5fJgV2kR0uLFQuBKtLeFVTrYEVllI99tiOTSefVNYuip9ELTEkepIIDftNdze76NAQ==", "requires": {"@motionone/animation": "^10.15.1", "@motionone/dom": "^10.16.2", "@motionone/svelte": "^10.16.2", "@motionone/types": "^10.15.1", "@motionone/utils": "^10.15.1", "@motionone/vue": "^10.16.2"}}, "ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}, "multiformats": {"version": "9.9.0", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-9.9.0.tgz", "integrity": "sha512-HoMUjhH9T8DDBNT+6xzkrd9ga/XiBI4xLr58LJACwK6G3HTOPeMz4nB4KJs33L2BelrIJa7P0VuNaVF3hMYfjg=="}, "next-tick": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/next-tick/-/next-tick-1.1.0.tgz", "integrity": "sha512-CXdUiJembsNjuToQvxayPZF9Vqht7hewsvy2sOWafLvi2awflj9mOC6bHIg50orX8IJvWKY9wYQ/zB2kogPslQ=="}, "nock": {"version": "13.5.4", "resolved": "https://registry.npmjs.org/nock/-/nock-13.5.4.tgz", "integrity": "sha512-yAyTfdeNJGGBFxWdzSKCBYxs5FxLbCg5X5Q4ets974hcQzG1+qCxvIyOo4j2Ry6MUlhWVMX4OoYDefAIIwupjw==", "requires": {"debug": "^4.1.0", "json-stringify-safe": "^5.0.1", "propagate": "^2.0.0"}}, "node-domexception": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/node-domexception/-/node-domexception-1.0.0.tgz", "integrity": "sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ=="}, "node-fetch": {"version": "2.6.11", "resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.6.11.tgz", "integrity": "sha512-4I6pdBY1EthSqDmJkiNk3JIT8cswwR9nfeW/cPdUagJYEQG7R95WRH74wpz7ma8Gh/9dI9FP+OU+0E4FvtA55w==", "requires": {"whatwg-url": "^5.0.0"}}, "node-gyp-build": {"version": "4.5.0"}, "on-exit-leak-free": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/on-exit-leak-free/-/on-exit-leak-free-0.2.0.tgz", "integrity": "sha512-dqaz3u44QbRXQooZLTUKU41ZrzYrcvLISVgbrzbyCMxpmSLJvZ3ZamIJIZ29P6OhZIkNIQKosdeM6t1LYbA9hg=="}, "once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "requires": {"wrappy": "1"}}, "p-limit": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==", "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==", "requires": {"p-limit": "^2.2.0"}}, "p-try": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz", "integrity": "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ=="}, "pako": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/pako/-/pako-2.1.0.tgz", "integrity": "sha512-w+eufiZ1WuJYgPXbV/PO3NCMEc3xqylkKHzp8bxp1uW4qaSNQUkwmLLEc3kKsfz8lpV1F8Ht3U1Cm+9Srog2ug==", "optional": true}, "path-exists": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w=="}, "pino": {"version": "7.11.0", "resolved": "https://registry.npmjs.org/pino/-/pino-7.11.0.tgz", "integrity": "sha512-dMACeu63HtRLmCG8VKdy4cShCPKaYDR4youZqoSWLxl5Gu99HUw8bw75thbPv9Nip+H+QYX8o3ZJbTdVZZ2TVg==", "requires": {"atomic-sleep": "^1.0.0", "fast-redact": "^3.0.0", "on-exit-leak-free": "^0.2.0", "pino-abstract-transport": "v0.5.0", "pino-std-serializers": "^4.0.0", "process-warning": "^1.0.0", "quick-format-unescaped": "^4.0.3", "real-require": "^0.1.0", "safe-stable-stringify": "^2.1.0", "sonic-boom": "^2.2.1", "thread-stream": "^0.15.1"}}, "pino-abstract-transport": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/pino-abstract-transport/-/pino-abstract-transport-0.5.0.tgz", "integrity": "sha512-+KAgmVeqXYbTtU2FScx1XS3kNyfZ5TrXY07V96QnUSFqo2gAqlvmaxH67Lj7SWazqsMabf+58ctdTcBgnOLUOQ==", "requires": {"duplexify": "^4.1.2", "split2": "^4.0.0"}}, "pino-std-serializers": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/pino-std-serializers/-/pino-std-serializers-4.0.0.tgz", "integrity": "sha512-cK0pekc1Kjy5w9V2/n+8MkZwusa6EyyxfeQCB799CQRhRt/CqYKiWs5adeu8Shve2ZNffvfC/7J64A2PJo1W/Q=="}, "pngjs": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/pngjs/-/pngjs-5.0.0.tgz", "integrity": "sha512-40QW5YalBNfQo5yRYmiw7Yz6TKKVr3h6970B2YE+3fQpsWcrbj1PzJgxeJ19DRQjhMbKPIuMY8rFaXc8moolVw=="}, "process-warning": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/process-warning/-/process-warning-1.0.0.tgz", "integrity": "sha512-du4wfLyj4yCZq1VupnVSZmRsPJsNuxoDQFdCFHLaYiEbFBD7QE0a+I4D7hOxrVnh78QE/YipFAj9lXHiXocV+Q=="}, "propagate": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/propagate/-/propagate-2.0.1.tgz", "integrity": "sha512-vGrhOavPSTz4QVNuBNdcNXePNdNMaO1xj9yBeH1ScQPjk/rhg9sSlCXPhMkFuaNNW/syTvYqsnbIJxMBfRbbag=="}, "proxy-compare": {"version": "2.5.1", "resolved": "https://registry.npmjs.org/proxy-compare/-/proxy-compare-2.5.1.tgz", "integrity": "sha512-oyfc0Tx87Cpwva5ZXezSp5V9vht1c7dZBhvuV/y3ctkgMVUmiAGDVeeB0dKhGSyT0v1ZTEQYpe/RXlBVBNuCLA=="}, "qrcode": {"version": "1.5.3", "resolved": "https://registry.npmjs.org/qrcode/-/qrcode-1.5.3.tgz", "integrity": "sha512-puyri6ApkEHYiVl4CFzo1tDkAZ+ATcnbJrJ6RiBM1Fhctdn/ix9MTE3hRph33omisEbC/2fcfemsseiKgBPKZg==", "requires": {"dijkstrajs": "^1.0.1", "encode-utf8": "^1.0.3", "pngjs": "^5.0.0", "yargs": "^15.3.1"}}, "query-string": {"version": "7.1.3", "resolved": "https://registry.npmjs.org/query-string/-/query-string-7.1.3.tgz", "integrity": "sha512-hh2WYhq4fi8+b+/2Kg9CEge4fDPvHS534aOOvOZeQ3+Vf2mCFsaFBYj0i+iXcAq6I9Vzp5fjMFBlONvayDC1qg==", "requires": {"decode-uri-component": "^0.2.2", "filter-obj": "^1.1.0", "split-on-first": "^1.0.0", "strict-uri-encode": "^2.0.0"}}, "quick-format-unescaped": {"version": "4.0.4", "resolved": "https://registry.npmjs.org/quick-format-unescaped/-/quick-format-unescaped-4.0.4.tgz", "integrity": "sha512-tYC1Q1hgyRuHgloV/YXs2w15unPVh8qfu/qCTfhTYamaw7fyhumKa2yGpdSo87vY32rIclj+4fWYQXUMs9EHvg=="}, "react": {"version": "file:../public-ui/node_modules/react", "requires": {"loose-envify": "^1.1.0"}}, "readable-stream": {"version": "3.6.2", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "integrity": "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==", "requires": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}}, "real-require": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/real-require/-/real-require-0.1.0.tgz", "integrity": "sha512-r/H9MzAWtrv8aSVjPCMFpDMl5q66GqtmmRkRjpHTsp4zBAa+snZyiQNlMONiUmEJcsnaw0wCauJ2GWODr/aFkg=="}, "regenerator-runtime": {"version": "0.14.1", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz", "integrity": "sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw=="}, "require-directory": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q=="}, "require-main-filename": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/require-main-filename/-/require-main-filename-2.0.0.tgz", "integrity": "sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg=="}, "rxjs": {"version": "7.8.1", "resolved": "https://registry.npmjs.org/rxjs/-/rxjs-7.8.1.tgz", "integrity": "sha512-AA3TVj+0A2iuIoQkWEK/tqFjBq2j+6PO6Y0zJcvzLAFhEFIO3HL0vls9hWLncZbAAbK0mar7oZ4V079I/qPMxg==", "requires": {"tslib": "^2.1.0"}}, "safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ=="}, "safe-json-utils": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/safe-json-utils/-/safe-json-utils-1.1.1.tgz", "integrity": "sha512-SAJWGKDs50tAbiDXLf89PDwt9XYkWyANFWVzn4dTXl5QyI8t2o/bW5/OJl3lvc2WVU4MEpTo9Yz5NVFNsp+OJQ=="}, "safe-stable-stringify": {"version": "2.4.3", "resolved": "https://registry.npmjs.org/safe-stable-stringify/-/safe-stable-stringify-2.4.3.tgz", "integrity": "sha512-e2bDA2WJT0wxseVd4lsDP4+3ONX6HpMXQa1ZhFQ7SU+GjvORCmShbCMltrtIDfkYhVHrOcPtj+KhmDBdPdZD1g=="}, "scale-ts": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/scale-ts/-/scale-ts-1.6.0.tgz", "integrity": "sha512-Ja5VCjNZR8TGKhUumy9clVVxcDpM+YFjAnkMuwQy68Hixio3VRRvWdE3g8T/yC+HXA0ZDQl2TGyUmtmbcVl40Q==", "optional": true, "peer": true}, "scrypt-js": {"version": "3.0.1"}, "set-blocking": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz", "integrity": "sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw=="}, "smoldot": {"version": "2.0.22", "resolved": "https://registry.npmjs.org/smoldot/-/smoldot-2.0.22.tgz", "integrity": "sha512-B50vRgTY6v3baYH6uCgL15tfaag5tcS2o/P5q1OiXcKGv1axZDfz2dzzMuIkVpyMR2ug11F6EAtQlmYBQd292g==", "optional": true, "peer": true, "requires": {"ws": "^8.8.1"}, "dependencies": {"ws": {"version": "8.17.0", "resolved": "https://registry.npmjs.org/ws/-/ws-8.17.0.tgz", "integrity": "sha512-uJq6108EgZMAl20KagGkzCKfMEjxmKvZHG7Tlq0Z6nOky7YF7aq4mOx6xK8TJ/i1LeK4Qus7INktacctDgY8Ow==", "optional": true, "peer": true, "requires": {}}}}, "sonic-boom": {"version": "2.8.0", "resolved": "https://registry.npmjs.org/sonic-boom/-/sonic-boom-2.8.0.tgz", "integrity": "sha512-kuonw1YOYYNOve5iHdSahXPOK49GqwA+LZhI6Wz/l0rP57iKyXXIHaRagOBHAPmGwJC6od2Z9zgvZ5loSgMlVg==", "requires": {"atomic-sleep": "^1.0.0"}}, "split-on-first": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/split-on-first/-/split-on-first-1.1.0.tgz", "integrity": "sha512-43ZssAJaMusuKWL8sKUBQXHWOpq8d6CfN/u1p4gUzfJkM05C8rxTmYrkIPTXapZpORA6LkkzcUulJ8FqA7Uudw=="}, "split2": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/split2/-/split2-4.2.0.tgz", "integrity": "sha512-UcjcJOWknrNkF6PLX83qcHM6KHgVKNkV62Y8a5uYDVv9ydGQVwAHMKqHdJje1VTWpljG0WYpCDhrCdAOYH4TWg=="}, "stream-shift": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/stream-shift/-/stream-shift-1.0.1.tgz", "integrity": "sha512-AiisoFqQ0vbGcZgQPY1cdP2I76glaVA/RauYR4G4thNFgkTqr90yXTo4LYX60Jl+sIlPNHHdGSwo01AvbKUSVQ=="}, "strict-uri-encode": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/strict-uri-encode/-/strict-uri-encode-2.0.0.tgz", "integrity": "sha512-QwiXZgpRcKkhTj2Scnn++4PKtWsH0kpzZ62L2R6c/LUVYv7hVnZqcg2+sMuT6R7Jusu1vviK/MFsu6kNJfWlEQ=="}, "string_decoder": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==", "requires": {"safe-buffer": "~5.2.0"}}, "string-width": {"version": "4.2.3", "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "requires": {"ansi-regex": "^5.0.1"}}, "thread-stream": {"version": "0.15.2", "resolved": "https://registry.npmjs.org/thread-stream/-/thread-stream-0.15.2.tgz", "integrity": "sha512-UkEhKIg2pD+fjkHQKyJO3yoIvAP3N6RlNFt2dUhcS1FGvCD1cQa1M/PGknCLFIyZdtJOWQjejp7bdNqmN7zwdA==", "requires": {"real-require": "^0.1.0"}}, "tr46": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz", "integrity": "sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw=="}, "tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "tweetnacl": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-1.0.3.tgz", "integrity": "sha512-6rt+RN7aOi1nGMyC4Xa5DdYiukl2UWCbcJft7YhxReBGQD7OAM8Pbxw6YMo4r2diNEA8FEmu32YOn9rhaiE5yw=="}, "type": {"version": "2.7.2", "resolved": "https://registry.npmjs.org/type/-/type-2.7.2.tgz", "integrity": "sha512-dzlvlNlt6AXU7EBSfpAscydQ7gXB+pPGsPnfJnZpiNJBDj7IaJzQlBZYGdEi4R9HmPdBv2XmWJ6YUtoTa7lmCw=="}, "typedarray-to-buffer": {"version": "3.1.5", "resolved": "https://registry.npmjs.org/typedarray-to-buffer/-/typedarray-to-buffer-3.1.5.tgz", "integrity": "sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q==", "requires": {"is-typedarray": "^1.0.0"}}, "uint8arrays": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-3.1.1.tgz", "integrity": "sha512-+QJa8QRnbdXVpHYjLoTpJIdCTiw9Ir62nocClWuXIq2JIh4Uta0cQsTSpFL678p2CN8B+XSApwcU+pQEqVpKWg==", "requires": {"multiformats": "^9.4.2"}}, "undici-types": {"version": "5.26.5", "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz", "integrity": "sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA=="}, "use-sync-external-store": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.2.0.tgz", "integrity": "sha512-eEgnFxGQ1Ife9bzYs6VLi8/4X6CObHMw9Qr9tPY43iKwsPw8xE8+EFsf/2cFZ5S3esXgpWgtSCtLNS41F+sKPA==", "requires": {}}, "utf-8-validate": {"version": "5.0.10", "requires": {"node-gyp-build": "^4.3.0"}}, "util-deprecate": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw=="}, "valtio": {"version": "1.10.5", "resolved": "https://registry.npmjs.org/valtio/-/valtio-1.10.5.tgz", "integrity": "sha512-jTp0k63VXf4r5hPoaC6a6LCG4POkVSh629WLi1+d5PlajLsbynTMd7qAgEiOSPxzoX5iNvbN7iZ/k/g29wrNiQ==", "requires": {"proxy-compare": "2.5.1", "use-sync-external-store": "1.2.0"}}, "web-streams-polyfill": {"version": "3.3.3", "resolved": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz", "integrity": "sha512-d2JWLCivmZYTSIoge9MsgFCZrt571BikcWGYkjC1khllbTeDlGqZ2D8vD8E/lJa8WGWbb7Plm8/XJYV7IJHZZw=="}, "webidl-conversions": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ=="}, "websocket": {"version": "1.0.35", "resolved": "https://registry.npmjs.org/websocket/-/websocket-1.0.35.tgz", "integrity": "sha512-/REy6amwPZl44DDzvRCkaI1q1bIiQB0mEFQLUrhz3z2EK91cp3n72rAjUlrTP0zV22HJIUOVHQGPxhFRjxjt+Q==", "requires": {"bufferutil": "^4.0.1", "debug": "^2.2.0", "es5-ext": "^0.10.63", "typedarray-to-buffer": "^3.1.5", "utf-8-validate": "^5.0.2", "yaeti": "^0.0.6"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="}}}, "whatwg-url": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz", "integrity": "sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==", "requires": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "which-module": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/which-module/-/which-module-2.0.1.tgz", "integrity": "sha512-iBdZ57RDvnOR9AGBhML2vFZf7h8vmBjhoaZqODJBFWHVtKkDmKuHai3cx5PgVMrX5YDNp27AofYbAwctSS+vhQ=="}, "wrap-ansi": {"version": "6.2.0", "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz", "integrity": "sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==", "requires": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}}, "wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ=="}, "ws": {"version": "7.4.6", "requires": {}}, "y18n": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/y18n/-/y18n-4.0.3.tgz", "integrity": "sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ=="}, "yaeti": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/yaeti/-/yaeti-0.0.6.tgz", "integrity": "sha512-MvQa//+KcZCUkBTIC9blM+CU9J2GzuTytsOUwf2lidtvkx/6gnEp1QvJv34t9vdjhFmha/mUiNDbN0D0mJWdug=="}, "yargs": {"version": "15.4.1", "resolved": "https://registry.npmjs.org/yargs/-/yargs-15.4.1.tgz", "integrity": "sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A==", "requires": {"cliui": "^6.0.0", "decamelize": "^1.2.0", "find-up": "^4.1.0", "get-caller-file": "^2.0.1", "require-directory": "^2.1.1", "require-main-filename": "^2.0.0", "set-blocking": "^2.0.0", "string-width": "^4.2.0", "which-module": "^2.0.0", "y18n": "^4.0.0", "yargs-parser": "^18.1.2"}}, "yargs-parser": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-18.1.3.tgz", "integrity": "sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ==", "requires": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}}, "zustand": {"version": "4.1.4", "requires": {"use-sync-external-store": "1.2.0"}}}}