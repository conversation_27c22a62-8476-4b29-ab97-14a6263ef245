/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["components_Auth_SubwalletLogin_tsx"],{

/***/ "./components/Auth/SubwalletLogin.tsx":
/*!********************************************!*\
  !*** ./components/Auth/SubwalletLogin.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_SocialIcons_Subwallet__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/SocialIcons/Subwallet */ \"./components/SocialIcons/Subwallet.tsx\");\n/* harmony import */ var _Hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Hooks/useAuth */ \"./hooks/useAuth.ts\");\n/* harmony import */ var _Root_helpers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Root/helpers */ \"./helpers/index.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var react_device_detect__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-device-detect */ \"./node_modules/react-device-detect/dist/lib.js\");\n/* harmony import */ var _AuthItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./AuthItem */ \"./components/Auth/AuthItem.tsx\");\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @airlyft/web3-evm-hooks */ \"../web3-evm-hooks/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst SubWalletLogin = ()=>{\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation)(\"translation\", {\n        keyPrefix: \"auth\"\n    });\n    const walletType = _airlyft_types__WEBPACK_IMPORTED_MODULE_4__.Web3WalletType.EVM_SUBWALLET;\n    const subWalletLogin = (0,_Hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useEVMAuth)((0,_Root_helpers__WEBPACK_IMPORTED_MODULE_3__.getInjectedWalletName)(walletType));\n    const isInjected = (0,_Root_helpers__WEBPACK_IMPORTED_MODULE_3__.isWalletInjected)(walletType);\n    const openDeepLink = ()=>window.open((0,_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_7__.getSubWalletDeepLink)(window.location.href), \"_blank\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthItem__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        onClick: ()=>react_device_detect__WEBPACK_IMPORTED_MODULE_5__.isMobile && !isInjected ? openDeepLink() : subWalletLogin(),\n        title: t(\"subwallet.evm\"),\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_SocialIcons_Subwallet__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            className: \"h-8 w-8\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Auth\\\\SubwalletLogin.tsx\",\n            lineNumber: 24,\n            columnNumber: 13\n        }, void 0)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Auth\\\\SubwalletLogin.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SubWalletLogin, \"BVyt4WkZ4yfPJJSfq3dBA+jS2v8=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation,\n        _Hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useEVMAuth\n    ];\n});\n_c = SubWalletLogin;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SubWalletLogin);\nvar _c;\n$RefreshReg$(_c, \"SubWalletLogin\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Auth/SubwalletLogin.tsx\n"));

/***/ }),

/***/ "./node_modules/react-device-detect/dist/lib.js":
/*!******************************************************!*\
  !*** ./node_modules/react-device-detect/dist/lib.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nfunction _interopDefault (ex) { return (ex && (typeof ex === 'object') && 'default' in ex) ? ex['default'] : ex; }\n\nvar React = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\nvar React__default = _interopDefault(React);\n\nvar UAParser = __webpack_require__(/*! ua-parser-js/dist/ua-parser.min */ \"./node_modules/react-device-detect/node_modules/ua-parser-js/dist/ua-parser.min.js\");\n\nvar ClientUAInstance = new UAParser();\nvar browser = ClientUAInstance.getBrowser();\nvar cpu = ClientUAInstance.getCPU();\nvar device = ClientUAInstance.getDevice();\nvar engine = ClientUAInstance.getEngine();\nvar os = ClientUAInstance.getOS();\nvar ua = ClientUAInstance.getUA();\nvar setUa = function setUa(userAgentString) {\n  return ClientUAInstance.setUA(userAgentString);\n};\nvar parseUserAgent = function parseUserAgent(userAgent) {\n  if (!userAgent) {\n    console.error('No userAgent string was provided');\n    return;\n  }\n\n  var UserAgentInstance = new UAParser(userAgent);\n  return {\n    UA: UserAgentInstance,\n    browser: UserAgentInstance.getBrowser(),\n    cpu: UserAgentInstance.getCPU(),\n    device: UserAgentInstance.getDevice(),\n    engine: UserAgentInstance.getEngine(),\n    os: UserAgentInstance.getOS(),\n    ua: UserAgentInstance.getUA(),\n    setUserAgent: function setUserAgent(userAgentString) {\n      return UserAgentInstance.setUA(userAgentString);\n    }\n  };\n};\n\nvar UAHelper = /*#__PURE__*/Object.freeze({\n  ClientUAInstance: ClientUAInstance,\n  browser: browser,\n  cpu: cpu,\n  device: device,\n  engine: engine,\n  os: os,\n  ua: ua,\n  setUa: setUa,\n  parseUserAgent: parseUserAgent\n});\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\n\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n\n  return _assertThisInitialized(self);\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n\n  var _s, _e;\n\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar DeviceTypes = {\n  Mobile: 'mobile',\n  Tablet: 'tablet',\n  SmartTv: 'smarttv',\n  Console: 'console',\n  Wearable: 'wearable',\n  Embedded: 'embedded',\n  Browser: undefined\n};\nvar BrowserTypes = {\n  Chrome: 'Chrome',\n  Firefox: 'Firefox',\n  Opera: 'Opera',\n  Yandex: 'Yandex',\n  Safari: 'Safari',\n  InternetExplorer: 'Internet Explorer',\n  Edge: 'Edge',\n  Chromium: 'Chromium',\n  Ie: 'IE',\n  MobileSafari: 'Mobile Safari',\n  EdgeChromium: 'Edge Chromium',\n  MIUI: 'MIUI Browser',\n  SamsungBrowser: 'Samsung Browser'\n};\nvar OsTypes = {\n  IOS: 'iOS',\n  Android: 'Android',\n  WindowsPhone: 'Windows Phone',\n  Windows: 'Windows',\n  MAC_OS: 'Mac OS'\n};\nvar InitialDeviceTypes = {\n  isMobile: false,\n  isTablet: false,\n  isBrowser: false,\n  isSmartTV: false,\n  isConsole: false,\n  isWearable: false\n};\n\nvar checkDeviceType = function checkDeviceType(type) {\n  switch (type) {\n    case DeviceTypes.Mobile:\n      return {\n        isMobile: true\n      };\n\n    case DeviceTypes.Tablet:\n      return {\n        isTablet: true\n      };\n\n    case DeviceTypes.SmartTv:\n      return {\n        isSmartTV: true\n      };\n\n    case DeviceTypes.Console:\n      return {\n        isConsole: true\n      };\n\n    case DeviceTypes.Wearable:\n      return {\n        isWearable: true\n      };\n\n    case DeviceTypes.Browser:\n      return {\n        isBrowser: true\n      };\n\n    case DeviceTypes.Embedded:\n      return {\n        isEmbedded: true\n      };\n\n    default:\n      return InitialDeviceTypes;\n  }\n};\nvar setUserAgent = function setUserAgent(userAgent) {\n  return setUa(userAgent);\n};\nvar setDefaults = function setDefaults(p) {\n  var d = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'none';\n  return p ? p : d;\n};\nvar getNavigatorInstance = function getNavigatorInstance() {\n  if (typeof window !== 'undefined') {\n    if (window.navigator || navigator) {\n      return window.navigator || navigator;\n    }\n  }\n\n  return false;\n};\nvar isIOS13Check = function isIOS13Check(type) {\n  var nav = getNavigatorInstance();\n  return nav && nav.platform && (nav.platform.indexOf(type) !== -1 || nav.platform === 'MacIntel' && nav.maxTouchPoints > 1 && !window.MSStream);\n};\n\nvar browserPayload = function browserPayload(isBrowser, browser, engine, os, ua) {\n  return {\n    isBrowser: isBrowser,\n    browserMajorVersion: setDefaults(browser.major),\n    browserFullVersion: setDefaults(browser.version),\n    browserName: setDefaults(browser.name),\n    engineName: setDefaults(engine.name),\n    engineVersion: setDefaults(engine.version),\n    osName: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    userAgent: setDefaults(ua)\n  };\n};\nvar mobilePayload = function mobilePayload(type, device, os, ua) {\n  return _objectSpread2({}, type, {\n    vendor: setDefaults(device.vendor),\n    model: setDefaults(device.model),\n    os: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    ua: setDefaults(ua)\n  });\n};\nvar smartTvPayload = function smartTvPayload(isSmartTV, engine, os, ua) {\n  return {\n    isSmartTV: isSmartTV,\n    engineName: setDefaults(engine.name),\n    engineVersion: setDefaults(engine.version),\n    osName: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    userAgent: setDefaults(ua)\n  };\n};\nvar consolePayload = function consolePayload(isConsole, engine, os, ua) {\n  return {\n    isConsole: isConsole,\n    engineName: setDefaults(engine.name),\n    engineVersion: setDefaults(engine.version),\n    osName: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    userAgent: setDefaults(ua)\n  };\n};\nvar wearablePayload = function wearablePayload(isWearable, engine, os, ua) {\n  return {\n    isWearable: isWearable,\n    engineName: setDefaults(engine.name),\n    engineVersion: setDefaults(engine.version),\n    osName: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    userAgent: setDefaults(ua)\n  };\n};\nvar embeddedPayload = function embeddedPayload(isEmbedded, device, engine, os, ua) {\n  return {\n    isEmbedded: isEmbedded,\n    vendor: setDefaults(device.vendor),\n    model: setDefaults(device.model),\n    engineName: setDefaults(engine.name),\n    engineVersion: setDefaults(engine.version),\n    osName: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    userAgent: setDefaults(ua)\n  };\n};\n\nfunction deviceDetect(userAgent) {\n  var _ref = userAgent ? parseUserAgent(userAgent) : UAHelper,\n      device = _ref.device,\n      browser = _ref.browser,\n      engine = _ref.engine,\n      os = _ref.os,\n      ua = _ref.ua;\n\n  var type = checkDeviceType(device.type);\n  var isBrowser = type.isBrowser,\n      isMobile = type.isMobile,\n      isTablet = type.isTablet,\n      isSmartTV = type.isSmartTV,\n      isConsole = type.isConsole,\n      isWearable = type.isWearable,\n      isEmbedded = type.isEmbedded;\n\n  if (isBrowser) {\n    return browserPayload(isBrowser, browser, engine, os, ua);\n  }\n\n  if (isSmartTV) {\n    return smartTvPayload(isSmartTV, engine, os, ua);\n  }\n\n  if (isConsole) {\n    return consolePayload(isConsole, engine, os, ua);\n  }\n\n  if (isMobile) {\n    return mobilePayload(type, device, os, ua);\n  }\n\n  if (isTablet) {\n    return mobilePayload(type, device, os, ua);\n  }\n\n  if (isWearable) {\n    return wearablePayload(isWearable, engine, os, ua);\n  }\n\n  if (isEmbedded) {\n    return embeddedPayload(isEmbedded, device, engine, os, ua);\n  }\n}\n\nvar isMobileType = function isMobileType(_ref) {\n  var type = _ref.type;\n  return type === DeviceTypes.Mobile;\n};\nvar isTabletType = function isTabletType(_ref2) {\n  var type = _ref2.type;\n  return type === DeviceTypes.Tablet;\n};\nvar isMobileAndTabletType = function isMobileAndTabletType(_ref3) {\n  var type = _ref3.type;\n  return type === DeviceTypes.Mobile || type === DeviceTypes.Tablet;\n};\nvar isSmartTVType = function isSmartTVType(_ref4) {\n  var type = _ref4.type;\n  return type === DeviceTypes.SmartTv;\n};\nvar isBrowserType = function isBrowserType(_ref5) {\n  var type = _ref5.type;\n  return type === DeviceTypes.Browser;\n};\nvar isWearableType = function isWearableType(_ref6) {\n  var type = _ref6.type;\n  return type === DeviceTypes.Wearable;\n};\nvar isConsoleType = function isConsoleType(_ref7) {\n  var type = _ref7.type;\n  return type === DeviceTypes.Console;\n};\nvar isEmbeddedType = function isEmbeddedType(_ref8) {\n  var type = _ref8.type;\n  return type === DeviceTypes.Embedded;\n};\nvar getMobileVendor = function getMobileVendor(_ref9) {\n  var vendor = _ref9.vendor;\n  return setDefaults(vendor);\n};\nvar getMobileModel = function getMobileModel(_ref10) {\n  var model = _ref10.model;\n  return setDefaults(model);\n};\nvar getDeviceType = function getDeviceType(_ref11) {\n  var type = _ref11.type;\n  return setDefaults(type, 'browser');\n}; // os types\n\nvar isAndroidType = function isAndroidType(_ref12) {\n  var name = _ref12.name;\n  return name === OsTypes.Android;\n};\nvar isWindowsType = function isWindowsType(_ref13) {\n  var name = _ref13.name;\n  return name === OsTypes.Windows;\n};\nvar isMacOsType = function isMacOsType(_ref14) {\n  var name = _ref14.name;\n  return name === OsTypes.MAC_OS;\n};\nvar isWinPhoneType = function isWinPhoneType(_ref15) {\n  var name = _ref15.name;\n  return name === OsTypes.WindowsPhone;\n};\nvar isIOSType = function isIOSType(_ref16) {\n  var name = _ref16.name;\n  return name === OsTypes.IOS;\n};\nvar getOsVersion = function getOsVersion(_ref17) {\n  var version = _ref17.version;\n  return setDefaults(version);\n};\nvar getOsName = function getOsName(_ref18) {\n  var name = _ref18.name;\n  return setDefaults(name);\n}; // browser types\n\nvar isChromeType = function isChromeType(_ref19) {\n  var name = _ref19.name;\n  return name === BrowserTypes.Chrome;\n};\nvar isFirefoxType = function isFirefoxType(_ref20) {\n  var name = _ref20.name;\n  return name === BrowserTypes.Firefox;\n};\nvar isChromiumType = function isChromiumType(_ref21) {\n  var name = _ref21.name;\n  return name === BrowserTypes.Chromium;\n};\nvar isEdgeType = function isEdgeType(_ref22) {\n  var name = _ref22.name;\n  return name === BrowserTypes.Edge;\n};\nvar isYandexType = function isYandexType(_ref23) {\n  var name = _ref23.name;\n  return name === BrowserTypes.Yandex;\n};\nvar isSafariType = function isSafariType(_ref24) {\n  var name = _ref24.name;\n  return name === BrowserTypes.Safari || name === BrowserTypes.MobileSafari;\n};\nvar isMobileSafariType = function isMobileSafariType(_ref25) {\n  var name = _ref25.name;\n  return name === BrowserTypes.MobileSafari;\n};\nvar isOperaType = function isOperaType(_ref26) {\n  var name = _ref26.name;\n  return name === BrowserTypes.Opera;\n};\nvar isIEType = function isIEType(_ref27) {\n  var name = _ref27.name;\n  return name === BrowserTypes.InternetExplorer || name === BrowserTypes.Ie;\n};\nvar isMIUIType = function isMIUIType(_ref28) {\n  var name = _ref28.name;\n  return name === BrowserTypes.MIUI;\n};\nvar isSamsungBrowserType = function isSamsungBrowserType(_ref29) {\n  var name = _ref29.name;\n  return name === BrowserTypes.SamsungBrowser;\n};\nvar getBrowserFullVersion = function getBrowserFullVersion(_ref30) {\n  var version = _ref30.version;\n  return setDefaults(version);\n};\nvar getBrowserVersion = function getBrowserVersion(_ref31) {\n  var major = _ref31.major;\n  return setDefaults(major);\n};\nvar getBrowserName = function getBrowserName(_ref32) {\n  var name = _ref32.name;\n  return setDefaults(name);\n}; // engine types\n\nvar getEngineName = function getEngineName(_ref33) {\n  var name = _ref33.name;\n  return setDefaults(name);\n};\nvar getEngineVersion = function getEngineVersion(_ref34) {\n  var version = _ref34.version;\n  return setDefaults(version);\n};\nvar isElectronType = function isElectronType() {\n  var nav = getNavigatorInstance();\n  var ua = nav && nav.userAgent && nav.userAgent.toLowerCase();\n  return typeof ua === 'string' ? /electron/.test(ua) : false;\n};\nvar isEdgeChromiumType = function isEdgeChromiumType(ua) {\n  return typeof ua === 'string' && ua.indexOf('Edg/') !== -1;\n};\nvar getIOS13 = function getIOS13() {\n  var nav = getNavigatorInstance();\n  return nav && (/iPad|iPhone|iPod/.test(nav.platform) || nav.platform === 'MacIntel' && nav.maxTouchPoints > 1) && !window.MSStream;\n};\nvar getIPad13 = function getIPad13() {\n  return isIOS13Check('iPad');\n};\nvar getIphone13 = function getIphone13() {\n  return isIOS13Check('iPhone');\n};\nvar getIPod13 = function getIPod13() {\n  return isIOS13Check('iPod');\n};\nvar getUseragent = function getUseragent(userAg) {\n  return setDefaults(userAg);\n};\n\nfunction buildSelectorsObject(options) {\n  var _ref = options ? options : UAHelper,\n      device = _ref.device,\n      browser = _ref.browser,\n      os = _ref.os,\n      engine = _ref.engine,\n      ua = _ref.ua;\n\n  return {\n    isSmartTV: isSmartTVType(device),\n    isConsole: isConsoleType(device),\n    isWearable: isWearableType(device),\n    isEmbedded: isEmbeddedType(device),\n    isMobileSafari: isMobileSafariType(browser) || getIPad13(),\n    isChromium: isChromiumType(browser),\n    isMobile: isMobileAndTabletType(device) || getIPad13(),\n    isMobileOnly: isMobileType(device),\n    isTablet: isTabletType(device) || getIPad13(),\n    isBrowser: isBrowserType(device),\n    isDesktop: isBrowserType(device),\n    isAndroid: isAndroidType(os),\n    isWinPhone: isWinPhoneType(os),\n    isIOS: isIOSType(os) || getIPad13(),\n    isChrome: isChromeType(browser),\n    isFirefox: isFirefoxType(browser),\n    isSafari: isSafariType(browser),\n    isOpera: isOperaType(browser),\n    isIE: isIEType(browser),\n    osVersion: getOsVersion(os),\n    osName: getOsName(os),\n    fullBrowserVersion: getBrowserFullVersion(browser),\n    browserVersion: getBrowserVersion(browser),\n    browserName: getBrowserName(browser),\n    mobileVendor: getMobileVendor(device),\n    mobileModel: getMobileModel(device),\n    engineName: getEngineName(engine),\n    engineVersion: getEngineVersion(engine),\n    getUA: getUseragent(ua),\n    isEdge: isEdgeType(browser) || isEdgeChromiumType(ua),\n    isYandex: isYandexType(browser),\n    deviceType: getDeviceType(device),\n    isIOS13: getIOS13(),\n    isIPad13: getIPad13(),\n    isIPhone13: getIphone13(),\n    isIPod13: getIPod13(),\n    isElectron: isElectronType(),\n    isEdgeChromium: isEdgeChromiumType(ua),\n    isLegacyEdge: isEdgeType(browser) && !isEdgeChromiumType(ua),\n    isWindows: isWindowsType(os),\n    isMacOs: isMacOsType(os),\n    isMIUI: isMIUIType(browser),\n    isSamsungBrowser: isSamsungBrowserType(browser)\n  };\n}\n\nvar isSmartTV = isSmartTVType(device);\nvar isConsole = isConsoleType(device);\nvar isWearable = isWearableType(device);\nvar isEmbedded = isEmbeddedType(device);\nvar isMobileSafari = isMobileSafariType(browser) || getIPad13();\nvar isChromium = isChromiumType(browser);\nvar isMobile = isMobileAndTabletType(device) || getIPad13();\nvar isMobileOnly = isMobileType(device);\nvar isTablet = isTabletType(device) || getIPad13();\nvar isBrowser = isBrowserType(device);\nvar isDesktop = isBrowserType(device);\nvar isAndroid = isAndroidType(os);\nvar isWinPhone = isWinPhoneType(os);\nvar isIOS = isIOSType(os) || getIPad13();\nvar isChrome = isChromeType(browser);\nvar isFirefox = isFirefoxType(browser);\nvar isSafari = isSafariType(browser);\nvar isOpera = isOperaType(browser);\nvar isIE = isIEType(browser);\nvar osVersion = getOsVersion(os);\nvar osName = getOsName(os);\nvar fullBrowserVersion = getBrowserFullVersion(browser);\nvar browserVersion = getBrowserVersion(browser);\nvar browserName = getBrowserName(browser);\nvar mobileVendor = getMobileVendor(device);\nvar mobileModel = getMobileModel(device);\nvar engineName = getEngineName(engine);\nvar engineVersion = getEngineVersion(engine);\nvar getUA = getUseragent(ua);\nvar isEdge = isEdgeType(browser) || isEdgeChromiumType(ua);\nvar isYandex = isYandexType(browser);\nvar deviceType = getDeviceType(device);\nvar isIOS13 = getIOS13();\nvar isIPad13 = getIPad13();\nvar isIPhone13 = getIphone13();\nvar isIPod13 = getIPod13();\nvar isElectron = isElectronType();\nvar isEdgeChromium = isEdgeChromiumType(ua);\nvar isLegacyEdge = isEdgeType(browser) && !isEdgeChromiumType(ua);\nvar isWindows = isWindowsType(os);\nvar isMacOs = isMacOsType(os);\nvar isMIUI = isMIUIType(browser);\nvar isSamsungBrowser = isSamsungBrowserType(browser);\nvar getSelectorsByUserAgent = function getSelectorsByUserAgent(userAgent) {\n  if (!userAgent || typeof userAgent !== 'string') {\n    console.error('No valid user agent string was provided');\n    return;\n  }\n\n  var _UAHelper$parseUserAg = parseUserAgent(userAgent),\n      device = _UAHelper$parseUserAg.device,\n      browser = _UAHelper$parseUserAg.browser,\n      os = _UAHelper$parseUserAg.os,\n      engine = _UAHelper$parseUserAg.engine,\n      ua = _UAHelper$parseUserAg.ua;\n\n  return buildSelectorsObject({\n    device: device,\n    browser: browser,\n    os: os,\n    engine: engine,\n    ua: ua\n  });\n};\n\nvar AndroidView = function AndroidView(_ref) {\n  var renderWithFragment = _ref.renderWithFragment,\n      children = _ref.children,\n      props = _objectWithoutProperties(_ref, [\"renderWithFragment\", \"children\"]);\n\n  return isAndroid ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar BrowserView = function BrowserView(_ref2) {\n  var renderWithFragment = _ref2.renderWithFragment,\n      children = _ref2.children,\n      props = _objectWithoutProperties(_ref2, [\"renderWithFragment\", \"children\"]);\n\n  return isBrowser ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar IEView = function IEView(_ref3) {\n  var renderWithFragment = _ref3.renderWithFragment,\n      children = _ref3.children,\n      props = _objectWithoutProperties(_ref3, [\"renderWithFragment\", \"children\"]);\n\n  return isIE ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar IOSView = function IOSView(_ref4) {\n  var renderWithFragment = _ref4.renderWithFragment,\n      children = _ref4.children,\n      props = _objectWithoutProperties(_ref4, [\"renderWithFragment\", \"children\"]);\n\n  return isIOS ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar MobileView = function MobileView(_ref5) {\n  var renderWithFragment = _ref5.renderWithFragment,\n      children = _ref5.children,\n      props = _objectWithoutProperties(_ref5, [\"renderWithFragment\", \"children\"]);\n\n  return isMobile ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar TabletView = function TabletView(_ref6) {\n  var renderWithFragment = _ref6.renderWithFragment,\n      children = _ref6.children,\n      props = _objectWithoutProperties(_ref6, [\"renderWithFragment\", \"children\"]);\n\n  return isTablet ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar WinPhoneView = function WinPhoneView(_ref7) {\n  var renderWithFragment = _ref7.renderWithFragment,\n      children = _ref7.children,\n      props = _objectWithoutProperties(_ref7, [\"renderWithFragment\", \"children\"]);\n\n  return isWinPhone ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar MobileOnlyView = function MobileOnlyView(_ref8) {\n  var renderWithFragment = _ref8.renderWithFragment,\n      children = _ref8.children,\n      viewClassName = _ref8.viewClassName,\n      style = _ref8.style,\n      props = _objectWithoutProperties(_ref8, [\"renderWithFragment\", \"children\", \"viewClassName\", \"style\"]);\n\n  return isMobileOnly ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar SmartTVView = function SmartTVView(_ref9) {\n  var renderWithFragment = _ref9.renderWithFragment,\n      children = _ref9.children,\n      props = _objectWithoutProperties(_ref9, [\"renderWithFragment\", \"children\"]);\n\n  return isSmartTV ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar ConsoleView = function ConsoleView(_ref10) {\n  var renderWithFragment = _ref10.renderWithFragment,\n      children = _ref10.children,\n      props = _objectWithoutProperties(_ref10, [\"renderWithFragment\", \"children\"]);\n\n  return isConsole ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar WearableView = function WearableView(_ref11) {\n  var renderWithFragment = _ref11.renderWithFragment,\n      children = _ref11.children,\n      props = _objectWithoutProperties(_ref11, [\"renderWithFragment\", \"children\"]);\n\n  return isWearable ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar CustomView = function CustomView(_ref12) {\n  var renderWithFragment = _ref12.renderWithFragment,\n      children = _ref12.children,\n      viewClassName = _ref12.viewClassName,\n      style = _ref12.style,\n      condition = _ref12.condition,\n      props = _objectWithoutProperties(_ref12, [\"renderWithFragment\", \"children\", \"viewClassName\", \"style\", \"condition\"]);\n\n  return condition ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\n\nfunction withOrientationChange(WrappedComponent) {\n  return /*#__PURE__*/function (_React$Component) {\n    _inherits(_class, _React$Component);\n\n    function _class(props) {\n      var _this;\n\n      _classCallCheck(this, _class);\n\n      _this = _possibleConstructorReturn(this, _getPrototypeOf(_class).call(this, props));\n      _this.isEventListenerAdded = false;\n      _this.handleOrientationChange = _this.handleOrientationChange.bind(_assertThisInitialized(_this));\n      _this.onOrientationChange = _this.onOrientationChange.bind(_assertThisInitialized(_this));\n      _this.onPageLoad = _this.onPageLoad.bind(_assertThisInitialized(_this));\n      _this.state = {\n        isLandscape: false,\n        isPortrait: false\n      };\n      return _this;\n    }\n\n    _createClass(_class, [{\n      key: \"handleOrientationChange\",\n      value: function handleOrientationChange() {\n        if (!this.isEventListenerAdded) {\n          this.isEventListenerAdded = true;\n        }\n\n        var orientation = window.innerWidth > window.innerHeight ? 90 : 0;\n        this.setState({\n          isPortrait: orientation === 0,\n          isLandscape: orientation === 90\n        });\n      }\n    }, {\n      key: \"onOrientationChange\",\n      value: function onOrientationChange() {\n        this.handleOrientationChange();\n      }\n    }, {\n      key: \"onPageLoad\",\n      value: function onPageLoad() {\n        this.handleOrientationChange();\n      }\n    }, {\n      key: \"componentDidMount\",\n      value: function componentDidMount() {\n        if ((typeof window === \"undefined\" ? \"undefined\" : _typeof(window)) !== undefined && isMobile) {\n          if (!this.isEventListenerAdded) {\n            this.handleOrientationChange();\n            window.addEventListener(\"load\", this.onPageLoad, false);\n          } else {\n            window.removeEventListener(\"load\", this.onPageLoad, false);\n          }\n\n          window.addEventListener(\"resize\", this.onOrientationChange, false);\n        }\n      }\n    }, {\n      key: \"componentWillUnmount\",\n      value: function componentWillUnmount() {\n        window.removeEventListener(\"resize\", this.onOrientationChange, false);\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        return React__default.createElement(WrappedComponent, _extends({}, this.props, {\n          isLandscape: this.state.isLandscape,\n          isPortrait: this.state.isPortrait\n        }));\n      }\n    }]);\n\n    return _class;\n  }(React__default.Component);\n}\n\nfunction useMobileOrientation() {\n  var _useState = React.useState(function () {\n    var orientation = window.innerWidth > window.innerHeight ? 90 : 0;\n    return {\n      isPortrait: orientation === 0,\n      isLandscape: orientation === 90,\n      orientation: orientation === 0 ? 'portrait' : 'landscape'\n    };\n  }),\n      _useState2 = _slicedToArray(_useState, 2),\n      state = _useState2[0],\n      setState = _useState2[1];\n\n  var handleOrientationChange = React.useCallback(function () {\n    var orientation = window.innerWidth > window.innerHeight ? 90 : 0;\n    var next = {\n      isPortrait: orientation === 0,\n      isLandscape: orientation === 90,\n      orientation: orientation === 0 ? 'portrait' : 'landscape'\n    };\n    state.orientation !== next.orientation && setState(next);\n  }, [state.orientation]);\n  React.useEffect(function () {\n    if ((typeof window === \"undefined\" ? \"undefined\" : _typeof(window)) !== undefined && isMobile) {\n      handleOrientationChange();\n      window.addEventListener(\"load\", handleOrientationChange, false);\n      window.addEventListener(\"resize\", handleOrientationChange, false);\n    }\n\n    return function () {\n      window.removeEventListener(\"resize\", handleOrientationChange, false);\n      window.removeEventListener(\"load\", handleOrientationChange, false);\n    };\n  }, [handleOrientationChange]);\n  return state;\n}\n\nfunction useDeviceData(userAgent) {\n  var hookUserAgent = userAgent ? userAgent : window.navigator.userAgent;\n  return parseUserAgent(hookUserAgent);\n}\n\nfunction useDeviceSelectors(userAgent) {\n  var hookUserAgent = userAgent ? userAgent : window.navigator.userAgent;\n  var deviceData = useDeviceData(hookUserAgent);\n  var selectors = buildSelectorsObject(deviceData);\n  return [selectors, deviceData];\n}\n\nexports.AndroidView = AndroidView;\nexports.BrowserTypes = BrowserTypes;\nexports.BrowserView = BrowserView;\nexports.ConsoleView = ConsoleView;\nexports.CustomView = CustomView;\nexports.IEView = IEView;\nexports.IOSView = IOSView;\nexports.MobileOnlyView = MobileOnlyView;\nexports.MobileView = MobileView;\nexports.OsTypes = OsTypes;\nexports.SmartTVView = SmartTVView;\nexports.TabletView = TabletView;\nexports.WearableView = WearableView;\nexports.WinPhoneView = WinPhoneView;\nexports.browserName = browserName;\nexports.browserVersion = browserVersion;\nexports.deviceDetect = deviceDetect;\nexports.deviceType = deviceType;\nexports.engineName = engineName;\nexports.engineVersion = engineVersion;\nexports.fullBrowserVersion = fullBrowserVersion;\nexports.getSelectorsByUserAgent = getSelectorsByUserAgent;\nexports.getUA = getUA;\nexports.isAndroid = isAndroid;\nexports.isBrowser = isBrowser;\nexports.isChrome = isChrome;\nexports.isChromium = isChromium;\nexports.isConsole = isConsole;\nexports.isDesktop = isDesktop;\nexports.isEdge = isEdge;\nexports.isEdgeChromium = isEdgeChromium;\nexports.isElectron = isElectron;\nexports.isEmbedded = isEmbedded;\nexports.isFirefox = isFirefox;\nexports.isIE = isIE;\nexports.isIOS = isIOS;\nexports.isIOS13 = isIOS13;\nexports.isIPad13 = isIPad13;\nexports.isIPhone13 = isIPhone13;\nexports.isIPod13 = isIPod13;\nexports.isLegacyEdge = isLegacyEdge;\nexports.isMIUI = isMIUI;\nexports.isMacOs = isMacOs;\nexports.isMobile = isMobile;\nexports.isMobileOnly = isMobileOnly;\nexports.isMobileSafari = isMobileSafari;\nexports.isOpera = isOpera;\nexports.isSafari = isSafari;\nexports.isSamsungBrowser = isSamsungBrowser;\nexports.isSmartTV = isSmartTV;\nexports.isTablet = isTablet;\nexports.isWearable = isWearable;\nexports.isWinPhone = isWinPhone;\nexports.isWindows = isWindows;\nexports.isYandex = isYandex;\nexports.mobileModel = mobileModel;\nexports.mobileVendor = mobileVendor;\nexports.osName = osName;\nexports.osVersion = osVersion;\nexports.parseUserAgent = parseUserAgent;\nexports.setUserAgent = setUserAgent;\nexports.useDeviceData = useDeviceData;\nexports.useDeviceSelectors = useDeviceSelectors;\nexports.useMobileOrientation = useMobileOrientation;\nexports.withOrientationChange = withOrientationChange;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/react-device-detect/dist/lib.js\n"));

/***/ }),

/***/ "./node_modules/react-device-detect/node_modules/ua-parser-js/dist/ua-parser.min.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/react-device-detect/node_modules/ua-parser-js/dist/ua-parser.min.js ***!
  \******************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("var __WEBPACK_AMD_DEFINE_RESULT__;/* UAParser.js v1.0.35\n   Copyright © 2012-2021 Faisal Salman <<EMAIL>>\n   MIT License */\n(function(window,undefined){\"use strict\";var LIBVERSION=\"1.0.35\",EMPTY=\"\",UNKNOWN=\"?\",FUNC_TYPE=\"function\",UNDEF_TYPE=\"undefined\",OBJ_TYPE=\"object\",STR_TYPE=\"string\",MAJOR=\"major\",MODEL=\"model\",NAME=\"name\",TYPE=\"type\",VENDOR=\"vendor\",VERSION=\"version\",ARCHITECTURE=\"architecture\",CONSOLE=\"console\",MOBILE=\"mobile\",TABLET=\"tablet\",SMARTTV=\"smarttv\",WEARABLE=\"wearable\",EMBEDDED=\"embedded\",UA_MAX_LENGTH=350;var AMAZON=\"Amazon\",APPLE=\"Apple\",ASUS=\"ASUS\",BLACKBERRY=\"BlackBerry\",BROWSER=\"Browser\",CHROME=\"Chrome\",EDGE=\"Edge\",FIREFOX=\"Firefox\",GOOGLE=\"Google\",HUAWEI=\"Huawei\",LG=\"LG\",MICROSOFT=\"Microsoft\",MOTOROLA=\"Motorola\",OPERA=\"Opera\",SAMSUNG=\"Samsung\",SHARP=\"Sharp\",SONY=\"Sony\",VIERA=\"Viera\",XIAOMI=\"Xiaomi\",ZEBRA=\"Zebra\",FACEBOOK=\"Facebook\",CHROMIUM_OS=\"Chromium OS\",MAC_OS=\"Mac OS\";var extend=function(regexes,extensions){var mergedRegexes={};for(var i in regexes){if(extensions[i]&&extensions[i].length%2===0){mergedRegexes[i]=extensions[i].concat(regexes[i])}else{mergedRegexes[i]=regexes[i]}}return mergedRegexes},enumerize=function(arr){var enums={};for(var i=0;i<arr.length;i++){enums[arr[i].toUpperCase()]=arr[i]}return enums},has=function(str1,str2){return typeof str1===STR_TYPE?lowerize(str2).indexOf(lowerize(str1))!==-1:false},lowerize=function(str){return str.toLowerCase()},majorize=function(version){return typeof version===STR_TYPE?version.replace(/[^\\d\\.]/g,EMPTY).split(\".\")[0]:undefined},trim=function(str,len){if(typeof str===STR_TYPE){str=str.replace(/^\\s\\s*/,EMPTY);return typeof len===UNDEF_TYPE?str:str.substring(0,UA_MAX_LENGTH)}};var rgxMapper=function(ua,arrays){var i=0,j,k,p,q,matches,match;while(i<arrays.length&&!matches){var regex=arrays[i],props=arrays[i+1];j=k=0;while(j<regex.length&&!matches){if(!regex[j]){break}matches=regex[j++].exec(ua);if(!!matches){for(p=0;p<props.length;p++){match=matches[++k];q=props[p];if(typeof q===OBJ_TYPE&&q.length>0){if(q.length===2){if(typeof q[1]==FUNC_TYPE){this[q[0]]=q[1].call(this,match)}else{this[q[0]]=q[1]}}else if(q.length===3){if(typeof q[1]===FUNC_TYPE&&!(q[1].exec&&q[1].test)){this[q[0]]=match?q[1].call(this,match,q[2]):undefined}else{this[q[0]]=match?match.replace(q[1],q[2]):undefined}}else if(q.length===4){this[q[0]]=match?q[3].call(this,match.replace(q[1],q[2])):undefined}}else{this[q]=match?match:undefined}}}}i+=2}},strMapper=function(str,map){for(var i in map){if(typeof map[i]===OBJ_TYPE&&map[i].length>0){for(var j=0;j<map[i].length;j++){if(has(map[i][j],str)){return i===UNKNOWN?undefined:i}}}else if(has(map[i],str)){return i===UNKNOWN?undefined:i}}return str};var oldSafariMap={\"1.0\":\"/8\",1.2:\"/1\",1.3:\"/3\",\"2.0\":\"/412\",\"2.0.2\":\"/416\",\"2.0.3\":\"/417\",\"2.0.4\":\"/419\",\"?\":\"/\"},windowsVersionMap={ME:\"4.90\",\"NT 3.11\":\"NT3.51\",\"NT 4.0\":\"NT4.0\",2e3:\"NT 5.0\",XP:[\"NT 5.1\",\"NT 5.2\"],Vista:\"NT 6.0\",7:\"NT 6.1\",8:\"NT 6.2\",8.1:\"NT 6.3\",10:[\"NT 6.4\",\"NT 10.0\"],RT:\"ARM\"};var regexes={browser:[[/\\b(?:crmo|crios)\\/([\\w\\.]+)/i],[VERSION,[NAME,\"Chrome\"]],[/edg(?:e|ios|a)?\\/([\\w\\.]+)/i],[VERSION,[NAME,\"Edge\"]],[/(opera mini)\\/([-\\w\\.]+)/i,/(opera [mobiletab]{3,6})\\b.+version\\/([-\\w\\.]+)/i,/(opera)(?:.+version\\/|[\\/ ]+)([\\w\\.]+)/i],[NAME,VERSION],[/opios[\\/ ]+([\\w\\.]+)/i],[VERSION,[NAME,OPERA+\" Mini\"]],[/\\bopr\\/([\\w\\.]+)/i],[VERSION,[NAME,OPERA]],[/(kindle)\\/([\\w\\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\\/ ]?([\\w\\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\\/ ]?([\\w\\.]*)/i,/(ba?idubrowser)[\\/ ]?([\\w\\.]+)/i,/(?:ms|\\()(ie) ([\\w\\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\\/([-\\w\\.]+)/i,/(heytap|ovi)browser\\/([\\d\\.]+)/i,/(weibo)__([\\d\\.]+)/i],[NAME,VERSION],[/(?:\\buc? ?browser|(?:juc.+)ucweb)[\\/ ]?([\\w\\.]+)/i],[VERSION,[NAME,\"UC\"+BROWSER]],[/microm.+\\bqbcore\\/([\\w\\.]+)/i,/\\bqbcore\\/([\\w\\.]+).+microm/i],[VERSION,[NAME,\"WeChat(Win) Desktop\"]],[/micromessenger\\/([\\w\\.]+)/i],[VERSION,[NAME,\"WeChat\"]],[/konqueror\\/([\\w\\.]+)/i],[VERSION,[NAME,\"Konqueror\"]],[/trident.+rv[: ]([\\w\\.]{1,9})\\b.+like gecko/i],[VERSION,[NAME,\"IE\"]],[/ya(?:search)?browser\\/([\\w\\.]+)/i],[VERSION,[NAME,\"Yandex\"]],[/(avast|avg)\\/([\\w\\.]+)/i],[[NAME,/(.+)/,\"$1 Secure \"+BROWSER],VERSION],[/\\bfocus\\/([\\w\\.]+)/i],[VERSION,[NAME,FIREFOX+\" Focus\"]],[/\\bopt\\/([\\w\\.]+)/i],[VERSION,[NAME,OPERA+\" Touch\"]],[/coc_coc\\w+\\/([\\w\\.]+)/i],[VERSION,[NAME,\"Coc Coc\"]],[/dolfin\\/([\\w\\.]+)/i],[VERSION,[NAME,\"Dolphin\"]],[/coast\\/([\\w\\.]+)/i],[VERSION,[NAME,OPERA+\" Coast\"]],[/miuibrowser\\/([\\w\\.]+)/i],[VERSION,[NAME,\"MIUI \"+BROWSER]],[/fxios\\/([-\\w\\.]+)/i],[VERSION,[NAME,FIREFOX]],[/\\bqihu|(qi?ho?o?|360)browser/i],[[NAME,\"360 \"+BROWSER]],[/(oculus|samsung|sailfish|huawei)browser\\/([\\w\\.]+)/i],[[NAME,/(.+)/,\"$1 \"+BROWSER],VERSION],[/(comodo_dragon)\\/([\\w\\.]+)/i],[[NAME,/_/g,\" \"],VERSION],[/(electron)\\/([\\w\\.]+) safari/i,/(tesla)(?: qtcarbrowser|\\/(20\\d\\d\\.[-\\w\\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\\/ ]?([\\w\\.]+)/i],[NAME,VERSION],[/(metasr)[\\/ ]?([\\w\\.]+)/i,/(lbbrowser)/i,/\\[(linkedin)app\\]/i],[NAME],[/((?:fban\\/fbios|fb_iab\\/fb4a)(?!.+fbav)|;fbav\\/([\\w\\.]+);)/i],[[NAME,FACEBOOK],VERSION],[/(kakao(?:talk|story))[\\/ ]([\\w\\.]+)/i,/(naver)\\(.*?(\\d+\\.[\\w\\.]+).*\\)/i,/safari (line)\\/([\\w\\.]+)/i,/\\b(line)\\/([\\w\\.]+)\\/iab/i,/(chromium|instagram)[\\/ ]([-\\w\\.]+)/i],[NAME,VERSION],[/\\bgsa\\/([\\w\\.]+) .*safari\\//i],[VERSION,[NAME,\"GSA\"]],[/musical_ly(?:.+app_?version\\/|_)([\\w\\.]+)/i],[VERSION,[NAME,\"TikTok\"]],[/headlesschrome(?:\\/([\\w\\.]+)| )/i],[VERSION,[NAME,CHROME+\" Headless\"]],[/ wv\\).+(chrome)\\/([\\w\\.]+)/i],[[NAME,CHROME+\" WebView\"],VERSION],[/droid.+ version\\/([\\w\\.]+)\\b.+(?:mobile safari|safari)/i],[VERSION,[NAME,\"Android \"+BROWSER]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\\/v?([\\w\\.]+)/i],[NAME,VERSION],[/version\\/([\\w\\.\\,]+) .*mobile\\/\\w+ (safari)/i],[VERSION,[NAME,\"Mobile Safari\"]],[/version\\/([\\w(\\.|\\,)]+) .*(mobile ?safari|safari)/i],[VERSION,NAME],[/webkit.+?(mobile ?safari|safari)(\\/[\\w\\.]+)/i],[NAME,[VERSION,strMapper,oldSafariMap]],[/(webkit|khtml)\\/([\\w\\.]+)/i],[NAME,VERSION],[/(navigator|netscape\\d?)\\/([-\\w\\.]+)/i],[[NAME,\"Netscape\"],VERSION],[/mobile vr; rv:([\\w\\.]+)\\).+firefox/i],[VERSION,[NAME,FIREFOX+\" Reality\"]],[/ekiohf.+(flow)\\/([\\w\\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\\/ ]?([\\w\\.\\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\\/([-\\w\\.]+)$/i,/(firefox)\\/([\\w\\.]+)/i,/(mozilla)\\/([\\w\\.]+) .+rv\\:.+gecko\\/\\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\\. ]?browser)[-\\/ ]?v?([\\w\\.]+)/i,/(links) \\(([\\w\\.]+)/i,/panasonic;(viera)/i],[NAME,VERSION],[/(cobalt)\\/([\\w\\.]+)/i],[NAME,[VERSION,/master.|lts./,\"\"]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\\)]/i],[[ARCHITECTURE,\"amd64\"]],[/(ia32(?=;))/i],[[ARCHITECTURE,lowerize]],[/((?:i[346]|x)86)[;\\)]/i],[[ARCHITECTURE,\"ia32\"]],[/\\b(aarch64|arm(v?8e?l?|_?64))\\b/i],[[ARCHITECTURE,\"arm64\"]],[/\\b(arm(?:v[67])?ht?n?[fl]p?)\\b/i],[[ARCHITECTURE,\"armhf\"]],[/windows (ce|mobile); ppc;/i],[[ARCHITECTURE,\"arm\"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\\))/i],[[ARCHITECTURE,/ower/,EMPTY,lowerize]],[/(sun4\\w)[;\\)]/i],[[ARCHITECTURE,\"sparc\"]],[/((?:avr32|ia64(?=;))|68k(?=\\))|\\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\\b|pa-risc)/i],[[ARCHITECTURE,lowerize]]],device:[[/\\b(sch-i[89]0\\d|shw-m380s|sm-[ptx]\\w{2,4}|gt-[pn]\\d{2,4}|sgh-t8[56]9|nexus 10)/i],[MODEL,[VENDOR,SAMSUNG],[TYPE,TABLET]],[/\\b((?:s[cgp]h|gt|sm)-\\w+|sc[g-]?[\\d]+a?|galaxy nexus)/i,/samsung[- ]([-\\w]+)/i,/sec-(sgh\\w+)/i],[MODEL,[VENDOR,SAMSUNG],[TYPE,MOBILE]],[/(?:\\/|\\()(ip(?:hone|od)[\\w, ]*)(?:\\/|;)/i],[MODEL,[VENDOR,APPLE],[TYPE,MOBILE]],[/\\((ipad);[-\\w\\),; ]+apple/i,/applecoremedia\\/[\\w\\.]+ \\((ipad)/i,/\\b(ipad)\\d\\d?,\\d\\d?[;\\]].+ios/i],[MODEL,[VENDOR,APPLE],[TYPE,TABLET]],[/(macintosh);/i],[MODEL,[VENDOR,APPLE]],[/\\b(sh-?[altvz]?\\d\\d[a-ekm]?)/i],[MODEL,[VENDOR,SHARP],[TYPE,MOBILE]],[/\\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\\d{2})\\b(?!.+d\\/s)/i],[MODEL,[VENDOR,HUAWEI],[TYPE,TABLET]],[/(?:huawei|honor)([-\\w ]+)[;\\)]/i,/\\b(nexus 6p|\\w{2,4}e?-[atu]?[ln][\\dx][012359c][adn]?)\\b(?!.+d\\/s)/i],[MODEL,[VENDOR,HUAWEI],[TYPE,MOBILE]],[/\\b(poco[\\w ]+)(?: bui|\\))/i,/\\b; (\\w+) build\\/hm\\1/i,/\\b(hm[-_ ]?note?[_ ]?(?:\\d\\w)?) bui/i,/\\b(redmi[\\-_ ]?(?:note|k)?[\\w_ ]+)(?: bui|\\))/i,/\\b(mi[-_ ]?(?:a\\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\\d?\\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\\))/i],[[MODEL,/_/g,\" \"],[VENDOR,XIAOMI],[TYPE,MOBILE]],[/\\b(mi[-_ ]?(?:pad)(?:[\\w_ ]+))(?: bui|\\))/i],[[MODEL,/_/g,\" \"],[VENDOR,XIAOMI],[TYPE,TABLET]],[/; (\\w+) bui.+ oppo/i,/\\b(cph[12]\\d{3}|p(?:af|c[al]|d\\w|e[ar])[mt]\\d0|x9007|a101op)\\b/i],[MODEL,[VENDOR,\"OPPO\"],[TYPE,MOBILE]],[/vivo (\\w+)(?: bui|\\))/i,/\\b(v[12]\\d{3}\\w?[at])(?: bui|;)/i],[MODEL,[VENDOR,\"Vivo\"],[TYPE,MOBILE]],[/\\b(rmx[12]\\d{3})(?: bui|;|\\))/i],[MODEL,[VENDOR,\"Realme\"],[TYPE,MOBILE]],[/\\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\\b[\\w ]+build\\//i,/\\bmot(?:orola)?[- ](\\w*)/i,/((?:moto[\\w\\(\\) ]+|xt\\d{3,4}|nexus 6)(?= bui|\\)))/i],[MODEL,[VENDOR,MOTOROLA],[TYPE,MOBILE]],[/\\b(mz60\\d|xoom[2 ]{0,2}) build\\//i],[MODEL,[VENDOR,MOTOROLA],[TYPE,TABLET]],[/((?=lg)?[vl]k\\-?\\d{3}) bui| 3\\.[-\\w; ]{10}lg?-([06cv9]{3,4})/i],[MODEL,[VENDOR,LG],[TYPE,TABLET]],[/(lm(?:-?f100[nv]?|-[\\w\\.]+)(?= bui|\\))|nexus [45])/i,/\\blg[-e;\\/ ]+((?!browser|netcast|android tv)\\w+)/i,/\\blg-?([\\d\\w]+) bui/i],[MODEL,[VENDOR,LG],[TYPE,MOBILE]],[/(ideatab[-\\w ]+)/i,/lenovo ?(s[56]000[-\\w]+|tab(?:[\\w ]+)|yt[-\\d\\w]{6}|tb[-\\d\\w]{6})/i],[MODEL,[VENDOR,\"Lenovo\"],[TYPE,TABLET]],[/(?:maemo|nokia).*(n900|lumia \\d+)/i,/nokia[-_ ]?([-\\w\\.]*)/i],[[MODEL,/_/g,\" \"],[VENDOR,\"Nokia\"],[TYPE,MOBILE]],[/(pixel c)\\b/i],[MODEL,[VENDOR,GOOGLE],[TYPE,TABLET]],[/droid.+; (pixel[\\daxl ]{0,6})(?: bui|\\))/i],[MODEL,[VENDOR,GOOGLE],[TYPE,MOBILE]],[/droid.+ (a?\\d[0-2]{2}so|[c-g]\\d{4}|so[-gl]\\w+|xq-a\\w[4-7][12])(?= bui|\\).+chrome\\/(?![1-6]{0,1}\\d\\.))/i],[MODEL,[VENDOR,SONY],[TYPE,MOBILE]],[/sony tablet [ps]/i,/\\b(?:sony)?sgp\\w+(?: bui|\\))/i],[[MODEL,\"Xperia Tablet\"],[VENDOR,SONY],[TYPE,TABLET]],[/ (kb2005|in20[12]5|be20[12][59])\\b/i,/(?:one)?(?:plus)? (a\\d0\\d\\d)(?: b|\\))/i],[MODEL,[VENDOR,\"OnePlus\"],[TYPE,MOBILE]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\\))/i,/(kf[a-z]+)( bui|\\)).+silk\\//i],[MODEL,[VENDOR,AMAZON],[TYPE,TABLET]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\\)).+silk\\//i],[[MODEL,/(.+)/g,\"Fire Phone $1\"],[VENDOR,AMAZON],[TYPE,MOBILE]],[/(playbook);[-\\w\\),; ]+(rim)/i],[MODEL,VENDOR,[TYPE,TABLET]],[/\\b((?:bb[a-f]|st[hv])100-\\d)/i,/\\(bb10; (\\w+)/i],[MODEL,[VENDOR,BLACKBERRY],[TYPE,MOBILE]],[/(?:\\b|asus_)(transfo[prime ]{4,10} \\w+|eeepc|slider \\w+|nexus 7|padfone|p00[cj])/i],[MODEL,[VENDOR,ASUS],[TYPE,TABLET]],[/ (z[bes]6[027][012][km][ls]|zenfone \\d\\w?)\\b/i],[MODEL,[VENDOR,ASUS],[TYPE,MOBILE]],[/(nexus 9)/i],[MODEL,[VENDOR,\"HTC\"],[TYPE,TABLET]],[/(htc)[-;_ ]{1,2}([\\w ]+(?=\\)| bui)|\\w+)/i,/(zte)[- ]([\\w ]+?)(?: bui|\\/|\\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\\.))|sony(?!-bra))[-_ ]?([-\\w]*)/i],[VENDOR,[MODEL,/_/g,\" \"],[TYPE,MOBILE]],[/droid.+; ([ab][1-7]-?[0178a]\\d\\d?)/i],[MODEL,[VENDOR,\"Acer\"],[TYPE,TABLET]],[/droid.+; (m[1-5] note) bui/i,/\\bmz-([-\\w]{2,})/i],[MODEL,[VENDOR,\"Meizu\"],[TYPE,MOBILE]],[/(blackberry|benq|palm(?=\\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\\w]*)/i,/(hp) ([\\w ]+\\w)/i,/(asus)-?(\\w+)/i,/(microsoft); (lumia[\\w ]+)/i,/(lenovo)[-_ ]?([-\\w]+)/i,/(jolla)/i,/(oppo) ?([\\w ]+) bui/i],[VENDOR,MODEL,[TYPE,MOBILE]],[/(kobo)\\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\\/([\\w\\.]+)/i,/(nook)[\\w ]+build\\/(\\w+)/i,/(dell) (strea[kpr\\d ]*[\\dko])/i,/(le[- ]+pan)[- ]+(\\w{1,9}) bui/i,/(trinity)[- ]*(t\\d{3}) bui/i,/(gigaset)[- ]+(q\\w{1,9}) bui/i,/(vodafone) ([\\w ]+)(?:\\)| bui)/i],[VENDOR,MODEL,[TYPE,TABLET]],[/(surface duo)/i],[MODEL,[VENDOR,MICROSOFT],[TYPE,TABLET]],[/droid [\\d\\.]+; (fp\\du?)(?: b|\\))/i],[MODEL,[VENDOR,\"Fairphone\"],[TYPE,MOBILE]],[/(u304aa)/i],[MODEL,[VENDOR,\"AT&T\"],[TYPE,MOBILE]],[/\\bsie-(\\w*)/i],[MODEL,[VENDOR,\"Siemens\"],[TYPE,MOBILE]],[/\\b(rct\\w+) b/i],[MODEL,[VENDOR,\"RCA\"],[TYPE,TABLET]],[/\\b(venue[\\d ]{2,7}) b/i],[MODEL,[VENDOR,\"Dell\"],[TYPE,TABLET]],[/\\b(q(?:mv|ta)\\w+) b/i],[MODEL,[VENDOR,\"Verizon\"],[TYPE,TABLET]],[/\\b(?:barnes[& ]+noble |bn[rt])([\\w\\+ ]*) b/i],[MODEL,[VENDOR,\"Barnes & Noble\"],[TYPE,TABLET]],[/\\b(tm\\d{3}\\w+) b/i],[MODEL,[VENDOR,\"NuVision\"],[TYPE,TABLET]],[/\\b(k88) b/i],[MODEL,[VENDOR,\"ZTE\"],[TYPE,TABLET]],[/\\b(nx\\d{3}j) b/i],[MODEL,[VENDOR,\"ZTE\"],[TYPE,MOBILE]],[/\\b(gen\\d{3}) b.+49h/i],[MODEL,[VENDOR,\"Swiss\"],[TYPE,MOBILE]],[/\\b(zur\\d{3}) b/i],[MODEL,[VENDOR,\"Swiss\"],[TYPE,TABLET]],[/\\b((zeki)?tb.*\\b) b/i],[MODEL,[VENDOR,\"Zeki\"],[TYPE,TABLET]],[/\\b([yr]\\d{2}) b/i,/\\b(dragon[- ]+touch |dt)(\\w{5}) b/i],[[VENDOR,\"Dragon Touch\"],MODEL,[TYPE,TABLET]],[/\\b(ns-?\\w{0,9}) b/i],[MODEL,[VENDOR,\"Insignia\"],[TYPE,TABLET]],[/\\b((nxa|next)-?\\w{0,9}) b/i],[MODEL,[VENDOR,\"NextBook\"],[TYPE,TABLET]],[/\\b(xtreme\\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[VENDOR,\"Voice\"],MODEL,[TYPE,MOBILE]],[/\\b(lvtel\\-)?(v1[12]) b/i],[[VENDOR,\"LvTel\"],MODEL,[TYPE,MOBILE]],[/\\b(ph-1) /i],[MODEL,[VENDOR,\"Essential\"],[TYPE,MOBILE]],[/\\b(v(100md|700na|7011|917g).*\\b) b/i],[MODEL,[VENDOR,\"Envizen\"],[TYPE,TABLET]],[/\\b(trio[-\\w\\. ]+) b/i],[MODEL,[VENDOR,\"MachSpeed\"],[TYPE,TABLET]],[/\\btu_(1491) b/i],[MODEL,[VENDOR,\"Rotor\"],[TYPE,TABLET]],[/(shield[\\w ]+) b/i],[MODEL,[VENDOR,\"Nvidia\"],[TYPE,TABLET]],[/(sprint) (\\w+)/i],[VENDOR,MODEL,[TYPE,MOBILE]],[/(kin\\.[onetw]{3})/i],[[MODEL,/\\./g,\" \"],[VENDOR,MICROSOFT],[TYPE,MOBILE]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\\)/i],[MODEL,[VENDOR,ZEBRA],[TYPE,TABLET]],[/droid.+; (ec30|ps20|tc[2-8]\\d[kx])\\)/i],[MODEL,[VENDOR,ZEBRA],[TYPE,MOBILE]],[/smart-tv.+(samsung)/i],[VENDOR,[TYPE,SMARTTV]],[/hbbtv.+maple;(\\d+)/i],[[MODEL,/^/,\"SmartTV\"],[VENDOR,SAMSUNG],[TYPE,SMARTTV]],[/(nux; netcast.+smarttv|lg (netcast\\.tv-201\\d|android tv))/i],[[VENDOR,LG],[TYPE,SMARTTV]],[/(apple) ?tv/i],[VENDOR,[MODEL,APPLE+\" TV\"],[TYPE,SMARTTV]],[/crkey/i],[[MODEL,CHROME+\"cast\"],[VENDOR,GOOGLE],[TYPE,SMARTTV]],[/droid.+aft(\\w)( bui|\\))/i],[MODEL,[VENDOR,AMAZON],[TYPE,SMARTTV]],[/\\(dtv[\\);].+(aquos)/i,/(aquos-tv[\\w ]+)\\)/i],[MODEL,[VENDOR,SHARP],[TYPE,SMARTTV]],[/(bravia[\\w ]+)( bui|\\))/i],[MODEL,[VENDOR,SONY],[TYPE,SMARTTV]],[/(mitv-\\w{5}) bui/i],[MODEL,[VENDOR,XIAOMI],[TYPE,SMARTTV]],[/Hbbtv.*(technisat) (.*);/i],[VENDOR,MODEL,[TYPE,SMARTTV]],[/\\b(roku)[\\dx]*[\\)\\/]((?:dvp-)?[\\d\\.]*)/i,/hbbtv\\/\\d+\\.\\d+\\.\\d+ +\\([\\w\\+ ]*; *([\\w\\d][^;]*);([^;]*)/i],[[VENDOR,trim],[MODEL,trim],[TYPE,SMARTTV]],[/\\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\\b/i],[[TYPE,SMARTTV]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[VENDOR,MODEL,[TYPE,CONSOLE]],[/droid.+; (shield) bui/i],[MODEL,[VENDOR,\"Nvidia\"],[TYPE,CONSOLE]],[/(playstation [345portablevi]+)/i],[MODEL,[VENDOR,SONY],[TYPE,CONSOLE]],[/\\b(xbox(?: one)?(?!; xbox))[\\); ]/i],[MODEL,[VENDOR,MICROSOFT],[TYPE,CONSOLE]],[/((pebble))app/i],[VENDOR,MODEL,[TYPE,WEARABLE]],[/(watch)(?: ?os[,\\/]|\\d,\\d\\/)[\\d\\.]+/i],[MODEL,[VENDOR,APPLE],[TYPE,WEARABLE]],[/droid.+; (glass) \\d/i],[MODEL,[VENDOR,GOOGLE],[TYPE,WEARABLE]],[/droid.+; (wt63?0{2,3})\\)/i],[MODEL,[VENDOR,ZEBRA],[TYPE,WEARABLE]],[/(quest( 2| pro)?)/i],[MODEL,[VENDOR,FACEBOOK],[TYPE,WEARABLE]],[/(tesla)(?: qtcarbrowser|\\/[-\\w\\.]+)/i],[VENDOR,[TYPE,EMBEDDED]],[/(aeobc)\\b/i],[MODEL,[VENDOR,AMAZON],[TYPE,EMBEDDED]],[/droid .+?; ([^;]+?)(?: bui|\\) applew).+? mobile safari/i],[MODEL,[TYPE,MOBILE]],[/droid .+?; ([^;]+?)(?: bui|\\) applew).+?(?! mobile) safari/i],[MODEL,[TYPE,TABLET]],[/\\b((tablet|tab)[;\\/]|focus\\/\\d(?!.+mobile))/i],[[TYPE,TABLET]],[/(phone|mobile(?:[;\\/]| [ \\w\\/\\.]*safari)|pda(?=.+windows ce))/i],[[TYPE,MOBILE]],[/(android[-\\w\\. ]{0,9});.+buil/i],[MODEL,[VENDOR,\"Generic\"]]],engine:[[/windows.+ edge\\/([\\w\\.]+)/i],[VERSION,[NAME,EDGE+\"HTML\"]],[/webkit\\/537\\.36.+chrome\\/(?!27)([\\w\\.]+)/i],[VERSION,[NAME,\"Blink\"]],[/(presto)\\/([\\w\\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\\/([\\w\\.]+)/i,/ekioh(flow)\\/([\\w\\.]+)/i,/(khtml|tasman|links)[\\/ ]\\(?([\\w\\.]+)/i,/(icab)[\\/ ]([23]\\.[\\d\\.]+)/i,/\\b(libweb)/i],[NAME,VERSION],[/rv\\:([\\w\\.]{1,9})\\b.+(gecko)/i],[VERSION,NAME]],os:[[/microsoft (windows) (vista|xp)/i],[NAME,VERSION],[/(windows) nt 6\\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\\/ ]?([\\d\\.\\w ]*)/i,/(windows)[\\/ ]?([ntce\\d\\. ]+\\w)(?!.+xbox)/i],[NAME,[VERSION,strMapper,windowsVersionMap]],[/(win(?=3|9|n)|win 9x )([nt\\d\\.]+)/i],[[NAME,\"Windows\"],[VERSION,strMapper,windowsVersionMap]],[/ip[honead]{2,4}\\b(?:.*os ([\\w]+) like mac|; opera)/i,/ios;fbsv\\/([\\d\\.]+)/i,/cfnetwork\\/.+darwin/i],[[VERSION,/_/g,\".\"],[NAME,\"iOS\"]],[/(mac os x) ?([\\w\\. ]*)/i,/(macintosh|mac_powerpc\\b)(?!.+haiku)/i],[[NAME,MAC_OS],[VERSION,/_/g,\".\"]],[/droid ([\\w\\.]+)\\b.+(android[- ]x86|harmonyos)/i],[VERSION,NAME],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\\/ ]?([\\w\\.]*)/i,/(blackberry)\\w*\\/([\\w\\.]*)/i,/(tizen|kaios)[\\/ ]([\\w\\.]+)/i,/\\((series40);/i],[NAME,VERSION],[/\\(bb(10);/i],[VERSION,[NAME,BLACKBERRY]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\\/ ]?([\\w\\.]*)/i],[VERSION,[NAME,\"Symbian\"]],[/mozilla\\/[\\d\\.]+ \\((?:mobile|tablet|tv|mobile; [\\w ]+); rv:.+ gecko\\/([\\w\\.]+)/i],[VERSION,[NAME,FIREFOX+\" OS\"]],[/web0s;.+rt(tv)/i,/\\b(?:hp)?wos(?:browser)?\\/([\\w\\.]+)/i],[VERSION,[NAME,\"webOS\"]],[/watch(?: ?os[,\\/]|\\d,\\d\\/)([\\d\\.]+)/i],[VERSION,[NAME,\"watchOS\"]],[/crkey\\/([\\d\\.]+)/i],[VERSION,[NAME,CHROME+\"cast\"]],[/(cros) [\\w]+(?:\\)| ([\\w\\.]+)\\b)/i],[[NAME,CHROMIUM_OS],VERSION],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\\/(\\d+\\.[\\w\\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\\);]+)/i,/\\b(joli|palm)\\b ?(?:os)?\\/?([\\w\\.]*)/i,/(mint)[\\/\\(\\) ]?(\\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\\/ ]?(?!chrom|package)([-\\w\\.]*)/i,/(hurd|linux) ?([\\w\\.]*)/i,/(gnu) ?([\\w\\.]*)/i,/\\b([-frentopcghs]{0,5}bsd|dragonfly)[\\/ ]?(?!amd|[ix346]{1,2}86)([\\w\\.]*)/i,/(haiku) (\\w+)/i],[NAME,VERSION],[/(sunos) ?([\\w\\.\\d]*)/i],[[NAME,\"Solaris\"],VERSION],[/((?:open)?solaris)[-\\/ ]?([\\w\\.]*)/i,/(aix) ((\\d)(?=\\.|\\)| )[\\w\\.])*/i,/\\b(beos|os\\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\\w\\.]*)/i],[NAME,VERSION]]};var UAParser=function(ua,extensions){if(typeof ua===OBJ_TYPE){extensions=ua;ua=undefined}if(!(this instanceof UAParser)){return new UAParser(ua,extensions).getResult()}var _navigator=typeof window!==UNDEF_TYPE&&window.navigator?window.navigator:undefined;var _ua=ua||(_navigator&&_navigator.userAgent?_navigator.userAgent:EMPTY);var _uach=_navigator&&_navigator.userAgentData?_navigator.userAgentData:undefined;var _rgxmap=extensions?extend(regexes,extensions):regexes;var _isSelfNav=_navigator&&_navigator.userAgent==_ua;this.getBrowser=function(){var _browser={};_browser[NAME]=undefined;_browser[VERSION]=undefined;rgxMapper.call(_browser,_ua,_rgxmap.browser);_browser[MAJOR]=majorize(_browser[VERSION]);if(_isSelfNav&&_navigator&&_navigator.brave&&typeof _navigator.brave.isBrave==FUNC_TYPE){_browser[NAME]=\"Brave\"}return _browser};this.getCPU=function(){var _cpu={};_cpu[ARCHITECTURE]=undefined;rgxMapper.call(_cpu,_ua,_rgxmap.cpu);return _cpu};this.getDevice=function(){var _device={};_device[VENDOR]=undefined;_device[MODEL]=undefined;_device[TYPE]=undefined;rgxMapper.call(_device,_ua,_rgxmap.device);if(_isSelfNav&&!_device[TYPE]&&_uach&&_uach.mobile){_device[TYPE]=MOBILE}if(_isSelfNav&&_device[MODEL]==\"Macintosh\"&&_navigator&&typeof _navigator.standalone!==UNDEF_TYPE&&_navigator.maxTouchPoints&&_navigator.maxTouchPoints>2){_device[MODEL]=\"iPad\";_device[TYPE]=TABLET}return _device};this.getEngine=function(){var _engine={};_engine[NAME]=undefined;_engine[VERSION]=undefined;rgxMapper.call(_engine,_ua,_rgxmap.engine);return _engine};this.getOS=function(){var _os={};_os[NAME]=undefined;_os[VERSION]=undefined;rgxMapper.call(_os,_ua,_rgxmap.os);if(_isSelfNav&&!_os[NAME]&&_uach&&_uach.platform!=\"Unknown\"){_os[NAME]=_uach.platform.replace(/chrome os/i,CHROMIUM_OS).replace(/macos/i,MAC_OS)}return _os};this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}};this.getUA=function(){return _ua};this.setUA=function(ua){_ua=typeof ua===STR_TYPE&&ua.length>UA_MAX_LENGTH?trim(ua,UA_MAX_LENGTH):ua;return this};this.setUA(_ua);return this};UAParser.VERSION=LIBVERSION;UAParser.BROWSER=enumerize([NAME,VERSION,MAJOR]);UAParser.CPU=enumerize([ARCHITECTURE]);UAParser.DEVICE=enumerize([MODEL,VENDOR,TYPE,CONSOLE,MOBILE,SMARTTV,TABLET,WEARABLE,EMBEDDED]);UAParser.ENGINE=UAParser.OS=enumerize([NAME,VERSION]);if(typeof exports!==UNDEF_TYPE){if(\"object\"!==UNDEF_TYPE&&module.exports){exports=module.exports=UAParser}exports.UAParser=UAParser}else{if(\"function\"===FUNC_TYPE&&__webpack_require__.amdO){!(__WEBPACK_AMD_DEFINE_RESULT__ = (function(){return UAParser}).call(exports, __webpack_require__, exports, module),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__))}else if(typeof window!==UNDEF_TYPE){window.UAParser=UAParser}}var $=typeof window!==UNDEF_TYPE&&(window.jQuery||window.Zepto);if($&&!$.ua){var parser=new UAParser;$.ua=parser.getResult();$.ua.get=function(){return parser.getUA()};$.ua.set=function(ua){parser.setUA(ua);var result=parser.getResult();for(var prop in result){$.ua[prop]=result[prop]}}}})(typeof window===\"object\"?window:this);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/react-device-detect/node_modules/ua-parser-js/dist/ua-parser.min.js\n"));

/***/ })

}]);