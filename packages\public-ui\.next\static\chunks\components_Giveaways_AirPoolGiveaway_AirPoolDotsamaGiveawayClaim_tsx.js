/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["components_Giveaways_AirPoolGiveaway_AirPoolDotsamaGiveawayClaim_tsx"],{

/***/ "./components/Web3Wallet lazy recursive ^\\.\\/.*$":
/*!***************************************************************!*\
  !*** ./components/Web3Wallet/ lazy ^\.\/.*$ namespace object ***!
  \***************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

var map = {
	"./Dotsama/DotsamaAccountList": [
		"./components/Web3Wallet/Dotsama/DotsamaAccountList.tsx",
		"components_Web3Wallet_Dotsama_DotsamaAccountList_tsx"
	],
	"./Dotsama/DotsamaAccountList.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaAccountList.tsx",
		"components_Web3Wallet_Dotsama_DotsamaAccountList_tsx"
	],
	"./Dotsama/DotsamaManual": [
		"./components/Web3Wallet/Dotsama/DotsamaManual.tsx",
		"components_Web3Wallet_Dotsama_DotsamaManual_tsx"
	],
	"./Dotsama/DotsamaManual.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaManual.tsx",
		"components_Web3Wallet_Dotsama_DotsamaManual_tsx"
	],
	"./Dotsama/DotsamaNova": [
		"./components/Web3Wallet/Dotsama/DotsamaNova.tsx",
		"components_Web3Wallet_Dotsama_DotsamaNova_tsx"
	],
	"./Dotsama/DotsamaNova.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaNova.tsx",
		"components_Web3Wallet_Dotsama_DotsamaNova_tsx"
	],
	"./Dotsama/DotsamaPolkadotJs": [
		"./components/Web3Wallet/Dotsama/DotsamaPolkadotJs.tsx",
		"components_Web3Wallet_Dotsama_DotsamaPolkadotJs_tsx"
	],
	"./Dotsama/DotsamaPolkadotJs.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaPolkadotJs.tsx",
		"components_Web3Wallet_Dotsama_DotsamaPolkadotJs_tsx"
	],
	"./Dotsama/DotsamaRaw": [
		"./components/Web3Wallet/Dotsama/DotsamaRaw.tsx",
		"components_Web3Wallet_Dotsama_DotsamaRaw_tsx"
	],
	"./Dotsama/DotsamaRaw.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaRaw.tsx",
		"components_Web3Wallet_Dotsama_DotsamaRaw_tsx"
	],
	"./Dotsama/DotsamaSubwallet": [
		"./components/Web3Wallet/Dotsama/DotsamaSubwallet.tsx",
		"components_Web3Wallet_Dotsama_DotsamaSubwallet_tsx"
	],
	"./Dotsama/DotsamaSubwallet.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaSubwallet.tsx",
		"components_Web3Wallet_Dotsama_DotsamaSubwallet_tsx"
	],
	"./Dotsama/DotsamaTalisman": [
		"./components/Web3Wallet/Dotsama/DotsamaTalisman.tsx",
		"components_Web3Wallet_Dotsama_DotsamaTalisman_tsx"
	],
	"./Dotsama/DotsamaTalisman.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaTalisman.tsx",
		"components_Web3Wallet_Dotsama_DotsamaTalisman_tsx"
	],
	"./Dotsama/DotsamaWallet": [
		"./components/Web3Wallet/Dotsama/DotsamaWallet.tsx",
		"components_Web3Wallet_Dotsama_DotsamaWallet_tsx"
	],
	"./Dotsama/DotsamaWallet.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaWallet.tsx",
		"components_Web3Wallet_Dotsama_DotsamaWallet_tsx"
	],
	"./Dotsama/WalletNotFound": [
		"./components/Web3Wallet/Dotsama/WalletNotFound.tsx",
		"components_Web3Wallet_Dotsama_WalletNotFound_tsx"
	],
	"./Dotsama/WalletNotFound.tsx": [
		"./components/Web3Wallet/Dotsama/WalletNotFound.tsx",
		"components_Web3Wallet_Dotsama_WalletNotFound_tsx"
	],
	"./Evm/EvmManual": [
		"./components/Web3Wallet/Evm/EvmManual.tsx",
		"components_Web3Wallet_Evm_EvmManual_tsx"
	],
	"./Evm/EvmManual.tsx": [
		"./components/Web3Wallet/Evm/EvmManual.tsx",
		"components_Web3Wallet_Evm_EvmManual_tsx"
	],
	"./Evm/EvmMetamask": [
		"./components/Web3Wallet/Evm/EvmMetamask.tsx",
		"components_Web3Wallet_Evm_EvmMetamask_tsx"
	],
	"./Evm/EvmMetamask.tsx": [
		"./components/Web3Wallet/Evm/EvmMetamask.tsx",
		"components_Web3Wallet_Evm_EvmMetamask_tsx"
	],
	"./Evm/EvmSubwallet": [
		"./components/Web3Wallet/Evm/EvmSubwallet.tsx",
		"components_Web3Wallet_Evm_EvmSubwallet_tsx"
	],
	"./Evm/EvmSubwallet.tsx": [
		"./components/Web3Wallet/Evm/EvmSubwallet.tsx",
		"components_Web3Wallet_Evm_EvmSubwallet_tsx"
	],
	"./Evm/EvmTalisman": [
		"./components/Web3Wallet/Evm/EvmTalisman.tsx",
		"components_Web3Wallet_Evm_EvmTalisman_tsx"
	],
	"./Evm/EvmTalisman.tsx": [
		"./components/Web3Wallet/Evm/EvmTalisman.tsx",
		"components_Web3Wallet_Evm_EvmTalisman_tsx"
	],
	"./Evm/EvmWallet": [
		"./components/Web3Wallet/Evm/EvmWallet.tsx",
		"components_Web3Wallet_Evm_EvmWallet_tsx"
	],
	"./Evm/EvmWallet.tsx": [
		"./components/Web3Wallet/Evm/EvmWallet.tsx",
		"components_Web3Wallet_Evm_EvmWallet_tsx"
	],
	"./Evm/EvmWalletConnect": [
		"./components/Web3Wallet/Evm/EvmWalletConnect.tsx",
		"components_Web3Wallet_Evm_EvmWalletConnect_tsx"
	],
	"./Evm/EvmWalletConnect.tsx": [
		"./components/Web3Wallet/Evm/EvmWalletConnect.tsx",
		"components_Web3Wallet_Evm_EvmWalletConnect_tsx"
	],
	"./Evm/GenericInjectedEvm": [
		"./components/Web3Wallet/Evm/GenericInjectedEvm.tsx",
		"components_Web3Wallet_Evm_GenericInjectedEvm_tsx"
	],
	"./Evm/GenericInjectedEvm.tsx": [
		"./components/Web3Wallet/Evm/GenericInjectedEvm.tsx",
		"components_Web3Wallet_Evm_GenericInjectedEvm_tsx"
	],
	"./Web3WalletRenderer": [
		"./components/Web3Wallet/Web3WalletRenderer.tsx"
	],
	"./Web3WalletRenderer.tsx": [
		"./components/Web3Wallet/Web3WalletRenderer.tsx"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(function() {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return Promise.all(ids.slice(1).map(__webpack_require__.e)).then(function() {
		return __webpack_require__(id);
	});
}
webpackAsyncContext.keys = function() { return Object.keys(map); };
webpackAsyncContext.id = "./components/Web3Wallet lazy recursive ^\\.\\/.*$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "./components/AlertBox.tsx":
/*!*********************************!*\
  !*** ./components/AlertBox.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleWarningAlertBox: function() { return /* binding */ SimpleWarningAlertBox; },\n/* harmony export */   SuccessAlertBox: function() { return /* binding */ SuccessAlertBox; },\n/* harmony export */   WarningAlertBox: function() { return /* binding */ WarningAlertBox; },\n/* harmony export */   \"default\": function() { return /* binding */ AlertBox; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"./node_modules/@phosphor-icons/react/dist/index.es.js\");\n\n\n\nfunction AlertBox(param) {\n    let { title, subtitle, icon, className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex flex-col p-4 py-8 rounded-2xl relative overflow-hidden text-primary-foreground\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -top-[40px] -right-[26px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -bottom-[66px] -right-[60px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"primary-gradient-box-circle-icon\",\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg mb-0\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, this),\n                            subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-opacity-80\",\n                                children: subtitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n_c = AlertBox;\nconst SimpleWarningAlertBox = (param)=>{\n    let { title, description } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-red-100 border border-red-300 text-red-600 px-4 py-3 rounded relative mt-4 space-x-2\",\n        role: \"alert\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                className: \"font-bold\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 51,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"block sm:inline\",\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 52,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = SimpleWarningAlertBox;\nfunction SuccessAlertBox(param) {\n    let { title, subtitle } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertBox, {\n        className: \"gradient-primary\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n            className: \"h-10 text-primary-foreground\",\n            size: 32\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n            lineNumber: 66,\n            columnNumber: 13\n        }, void 0),\n        title: title,\n        subtitle: subtitle\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n_c2 = SuccessAlertBox;\nfunction WarningAlertBox(param) {\n    let { title, subtitle } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertBox, {\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Info, {\n            fontSize: 28\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n            lineNumber: 82,\n            columnNumber: 13\n        }, void 0),\n        title: title,\n        subtitle: subtitle,\n        className: \"gradient-warning text-white\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n_c3 = WarningAlertBox;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"AlertBox\");\n$RefreshReg$(_c1, \"SimpleWarningAlertBox\");\n$RefreshReg$(_c2, \"SuccessAlertBox\");\n$RefreshReg$(_c3, \"WarningAlertBox\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/AlertBox.tsx\n"));

/***/ }),

/***/ "./components/BlockExplorerLink.tsx":
/*!******************************************!*\
  !*** ./components/BlockExplorerLink.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BlockExplorerLink; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction BlockExplorerLink(param) {\n    let { hash, blockExplorerUrls } = param;\n    return hash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        className: \"underline inline-flex text-sm\",\n        target: \"_blank\",\n        href: (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.getExplorerLink)(blockExplorerUrls, hash, _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.ExplorerDataType.TRANSACTION),\n        rel: \"noreferrer\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-1 inline-block\",\n                children: \"View on Explorer \"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-4 w-4 inline-block\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_c = BlockExplorerLink;\nvar _c;\n$RefreshReg$(_c, \"BlockExplorerLink\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/BlockExplorerLink.tsx\n"));

/***/ }),

/***/ "./components/Giveaways/AirPoolGiveaway/AirPoolDotsamaGiveawayClaim.tsx":
/*!******************************************************************************!*\
  !*** ./components/Giveaways/AirPoolGiveaway/AirPoolDotsamaGiveawayClaim.tsx ***!
  \******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/AlertBox */ \"./components/AlertBox.tsx\");\n/* harmony import */ var _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Toaster/Toaster */ \"./components/Toaster/Toaster.tsx\");\n/* harmony import */ var _Components_Web3Wallet_Dotsama_DotsamaWallet__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/Web3Wallet/Dotsama/DotsamaWallet */ \"./components/Web3Wallet/Dotsama/DotsamaWallet.tsx\");\n/* harmony import */ var _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Root/helpers/dotsama */ \"./helpers/dotsama.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _GiveawayTransactionHash__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../GiveawayTransactionHash */ \"./components/Giveaways/GiveawayTransactionHash.tsx\");\n/* harmony import */ var _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../hooks/useGetUserEventRewards */ \"./components/Giveaways/hooks/useGetUserEventRewards.ts\");\n/* harmony import */ var _useAirPoolGiveawayClaim__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./useAirPoolGiveawayClaim */ \"./components/Giveaways/AirPoolGiveaway/useAirPoolGiveawayClaim.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-google-recaptcha-v3 */ \"./node_modules/react-google-recaptcha-v3/dist/react-google-recaptcha-v3.esm.js\");\n/* harmony import */ var _Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @Components/RecaptchaDeclaration */ \"./components/RecaptchaDeclaration.tsx\");\n/* harmony import */ var _Root_services_tracking__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @Root/services/tracking */ \"./services/tracking.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AirPoolDotsamaGiveawayClaim = (param)=>{\n    let { giveaway, projectEvent, blockchain, asset, amount, airPool } = param;\n    var _sort_find;\n    _s();\n    const { claimRewardTrack } = (0,_Root_services_tracking__WEBPACK_IMPORTED_MODULE_14__.useGtmTrack)();\n    const { claim } = (0,_useAirPoolGiveawayClaim__WEBPACK_IMPORTED_MODULE_10__.useDotsamaAirPoolGiveawayClaim)(projectEvent.project.id, projectEvent.id, giveaway.id, asset, airPool);\n    const [isClaiming, setIsClaiming] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const { executeRecaptcha } = (0,react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_12__.useGoogleReCaptcha)();\n    const { data: userEventRewardsData, loading: isUserEventRewardsLoading } = (0,_hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_9__.useGetUserEventRewards)(projectEvent.id);\n    const processing = userEventRewardsData === null || userEventRewardsData === void 0 ? void 0 : userEventRewardsData.userEventRewards.find((item)=>item.status === _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.RewardStatus.PROCESSING);\n    const txHash = (_sort_find = [\n        ...(userEventRewardsData === null || userEventRewardsData === void 0 ? void 0 : userEventRewardsData.userEventRewards) || []\n    ].sort((a, b)=>{\n        const dateA = +new Date(a.updatedAt);\n        const dateB = +new Date(b.updatedAt);\n        return dateB - dateA;\n    }).find((reward)=>reward.txHash)) === null || _sort_find === void 0 ? void 0 : _sort_find.txHash;\n    const handleSubmit = async (connectorData)=>{\n        setIsClaiming(true);\n        let captcha;\n        if (projectEvent.ipProtect) {\n            if (!executeRecaptcha) {\n                (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                    title: \"Failed\",\n                    text: \"Recaptcha not initialized\",\n                    type: \"error\"\n                });\n                setIsClaiming(false);\n                return;\n            }\n            captcha = await executeRecaptcha(\"airpool_dotsama_giveaway_claim\");\n        }\n        const { account } = connectorData;\n        if (account) {\n            const formattedAddress = (0,_Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__.convertToSs58Address)(account, blockchain.chainId);\n            const formattedConnectorData = {\n                ...connectorData,\n                account: formattedAddress\n            };\n            claim({\n                connectorData: formattedConnectorData,\n                onError: (err)=>{\n                    (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                        title: \"Failed\",\n                        text: err.message,\n                        type: \"error\"\n                    });\n                    setIsClaiming(false);\n                },\n                onSuccess: ()=>{\n                    claimRewardTrack({\n                        projectId: projectEvent.project.id,\n                        eventId: projectEvent.id,\n                        projectTitle: projectEvent.project.name,\n                        eventTitle: projectEvent.title,\n                        giveawayId: giveaway.id,\n                        giveawayTitle: giveaway.title || \"\"\n                    });\n                    (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                        title: \"Submitted\",\n                        text: \"Your claim request has been submitted, check your notifications for an update.\",\n                        type: \"success\"\n                    });\n                    setIsClaiming(false);\n                },\n                captcha\n            });\n        } else {\n            setIsClaiming(false);\n        }\n    };\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_11__.useTranslation)(\"translation\");\n    if (processing) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__.SuccessAlertBox, {\n            title: t(\"giveaway.airTokenPool.successTitle\"),\n            subtitle: t(\"giveaway.airTokenPool.successSubtitle\")\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolDotsamaGiveawayClaim.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!amount.isZero()) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Web3Wallet_Dotsama_DotsamaWallet__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    blockchain: blockchain,\n                    button: {\n                        confirm: {\n                            enable: true,\n                            loading: isClaiming || isUserEventRewardsLoading,\n                            text: \"Claim \".concat((0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__.formatAmount)(amount === null || amount === void 0 ? void 0 : amount.toString(), asset.decimals), \" \").concat(asset.ticker, \" using \")\n                        }\n                    },\n                    onSuccess: handleSubmit,\n                    excludedWallets: [\n                        _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.Web3WalletType.DOTSAMA_MANUAL\n                    ]\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolDotsamaGiveawayClaim.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, undefined),\n                projectEvent.ipProtect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_13__.RecaptchaDeclaration, {\n                    className: \"text-xs text-cs text-center\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolDotsamaGiveawayClaim.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolDotsamaGiveawayClaim.tsx\",\n            lineNumber: 140,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawayTransactionHash__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        txHash: txHash,\n        blockchain: blockchain\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirPoolGiveaway\\\\AirPoolDotsamaGiveawayClaim.tsx\",\n        lineNumber: 163,\n        columnNumber: 10\n    }, undefined);\n};\n_s(AirPoolDotsamaGiveawayClaim, \"e3A7VQwTADC/tvJJT7zh4kkeIFc=\", false, function() {\n    return [\n        _Root_services_tracking__WEBPACK_IMPORTED_MODULE_14__.useGtmTrack,\n        _useAirPoolGiveawayClaim__WEBPACK_IMPORTED_MODULE_10__.useDotsamaAirPoolGiveawayClaim,\n        react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_12__.useGoogleReCaptcha,\n        _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_9__.useGetUserEventRewards,\n        next_i18next__WEBPACK_IMPORTED_MODULE_11__.useTranslation\n    ];\n});\n_c = AirPoolDotsamaGiveawayClaim;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AirPoolDotsamaGiveawayClaim);\nvar _c;\n$RefreshReg$(_c, \"AirPoolDotsamaGiveawayClaim\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0dpdmVhd2F5cy9BaXJQb29sR2l2ZWF3YXkvQWlyUG9vbERvdHNhbWFHaXZlYXdheUNsYWltLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBdUQ7QUFDTDtBQUN1QjtBQUNaO0FBU3JDO0FBQ3lCO0FBR2hCO0FBQ2dDO0FBQ1E7QUFDRTtBQUM3QjtBQUNpQjtBQUNTO0FBQ2xCO0FBRXRELE1BQU1lLDhCQUE4QjtRQUFDLEVBQ25DQyxRQUFRLEVBQ1JDLFlBQVksRUFDWkMsVUFBVSxFQUNWQyxLQUFLLEVBQ0xDLE1BQU0sRUFDTkMsT0FBTyxFQVFSO1FBa0JnQjs7SUFqQmYsTUFBTSxFQUFFQyxnQkFBZ0IsRUFBRSxHQUFHUixxRUFBV0E7SUFDeEMsTUFBTSxFQUFFUyxLQUFLLEVBQUUsR0FBR2IseUZBQThCQSxDQUM5Q08sYUFBYU8sT0FBTyxDQUFDQyxFQUFFLEVBQ3ZCUixhQUFhUSxFQUFFLEVBQ2ZULFNBQVNTLEVBQUUsRUFDWE4sT0FDQUU7SUFFRixNQUFNLENBQUNLLFlBQVlDLGNBQWMsR0FBR3BCLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sRUFBRXFCLGdCQUFnQixFQUFFLEdBQUdoQiw4RUFBa0JBO0lBQy9DLE1BQU0sRUFBRWlCLE1BQU1DLG9CQUFvQixFQUFFQyxTQUFTQyx5QkFBeUIsRUFBRSxHQUN0RXZCLHFGQUFzQkEsQ0FBQ1EsYUFBYVEsRUFBRTtJQUV4QyxNQUFNUSxhQUFhSCxpQ0FBQUEsMkNBQUFBLHFCQUFzQkksZ0JBQWdCLENBQUNDLElBQUksQ0FDNUQsQ0FBQ0MsT0FBU0EsS0FBS0MsTUFBTSxLQUFLakMsd0RBQVlBLENBQUNrQyxVQUFVO0lBR25ELE1BQU1DLFVBQVM7V0FBS1QsQ0FBQUEsaUNBQUFBLDJDQUFBQSxxQkFBc0JJLGdCQUFnQixLQUFJLEVBQUU7S0FBRSxDQUMvRE0sSUFBSSxDQUFDLENBQUNDLEdBQUdDO1FBQ1IsTUFBTUMsUUFBUSxDQUFDLElBQUlDLEtBQUtILEVBQUVJLFNBQVM7UUFDbkMsTUFBTUMsUUFBUSxDQUFDLElBQUlGLEtBQUtGLEVBQUVHLFNBQVM7UUFDbkMsT0FBT0MsUUFBUUg7SUFDakIsR0FDQ1IsSUFBSSxDQUFDLENBQUNZLFNBQVdBLE9BQU9SLE1BQU0sZUFObEIsNENBTXFCQSxNQUFNO0lBRTFDLE1BQU1TLGVBQWUsT0FBT0M7UUFDMUJ0QixjQUFjO1FBQ2QsSUFBSXVCO1FBRUosSUFBSWpDLGFBQWFrQyxTQUFTLEVBQUU7WUFDMUIsSUFBSSxDQUFDdkIsa0JBQWtCO2dCQUNyQjNCLHVFQUFPQSxDQUFDO29CQUNObUQsT0FBTztvQkFDUEMsTUFBTTtvQkFDTkMsTUFBTTtnQkFDUjtnQkFDQTNCLGNBQWM7Z0JBQ2Q7WUFDRjtZQUNBdUIsVUFBVSxNQUFNdEIsaUJBQWlCO1FBQ25DO1FBRUEsTUFBTSxFQUFFMkIsT0FBTyxFQUFFLEdBQUdOO1FBQ3BCLElBQUlNLFNBQVM7WUFDWCxNQUFNQyxtQkFBbUJyRCwyRUFBb0JBLENBQzNDb0QsU0FDQXJDLFdBQVd1QyxPQUFPO1lBRXBCLE1BQU1DLHlCQUF5QjtnQkFDN0IsR0FBR1QsYUFBYTtnQkFDaEJNLFNBQVNDO1lBQ1g7WUFFQWpDLE1BQU07Z0JBQ0owQixlQUFlUztnQkFDZkMsU0FBUyxDQUFDQztvQkFDUjNELHVFQUFPQSxDQUFDO3dCQUNObUQsT0FBTzt3QkFDUEMsTUFBTU8sSUFBSUMsT0FBTzt3QkFDakJQLE1BQU07b0JBQ1I7b0JBQ0EzQixjQUFjO2dCQUNoQjtnQkFDQW1DLFdBQVc7b0JBQ1R4QyxpQkFBaUI7d0JBQ2Z5QyxXQUFXOUMsYUFBYU8sT0FBTyxDQUFDQyxFQUFFO3dCQUNsQ3VDLFNBQVMvQyxhQUFhUSxFQUFFO3dCQUN4QndDLGNBQWNoRCxhQUFhTyxPQUFPLENBQUMwQyxJQUFJO3dCQUN2Q0MsWUFBWWxELGFBQWFtQyxLQUFLO3dCQUM5QmdCLFlBQVlwRCxTQUFTUyxFQUFFO3dCQUN2QjRDLGVBQWVyRCxTQUFTb0MsS0FBSyxJQUFJO29CQUNuQztvQkFDQW5ELHVFQUFPQSxDQUFDO3dCQUNObUQsT0FBTzt3QkFDUEMsTUFBTTt3QkFDTkMsTUFBTTtvQkFDUjtvQkFDQTNCLGNBQWM7Z0JBQ2hCO2dCQUNBdUI7WUFDRjtRQUNGLE9BQU87WUFDTHZCLGNBQWM7UUFDaEI7SUFDRjtJQUVBLE1BQU0sRUFBRTJDLENBQUMsRUFBRSxHQUFHM0QsNkRBQWNBLENBQUM7SUFFN0IsSUFBSXNCLFlBQVk7UUFDZCxxQkFDRSw4REFBQ2pDLGlFQUFlQTtZQUNkb0QsT0FBT2tCLEVBQUU7WUFDVEMsVUFBVUQsRUFBRTs7Ozs7O0lBR2xCO0lBRUEsSUFBSSxDQUFDbEQsT0FBT29ELE1BQU0sSUFBSTtRQUNwQixxQkFDRSw4REFBQ0M7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUN4RSxvRkFBYUE7b0JBQ1pnQixZQUFZQTtvQkFDWnlELFFBQVE7d0JBQ05DLFNBQVM7NEJBQ1BDLFFBQVE7NEJBQ1I5QyxTQUFTTCxjQUFjTTs0QkFDdkJxQixNQUFNLFNBR0RsQyxPQUhVYiwrREFBWUEsQ0FDekJjLG1CQUFBQSw2QkFBQUEsT0FBUTBELFFBQVEsSUFDaEIzRCxNQUFNNEQsUUFBUSxHQUNkLEtBQWdCLE9BQWI1RCxNQUFNNkQsTUFBTSxFQUFDO3dCQUNwQjtvQkFDRjtvQkFDQWxCLFdBQVdkO29CQUNYaUMsaUJBQWlCO3dCQUFDNUUsMERBQWNBLENBQUM2RSxjQUFjO3FCQUFDOzs7Ozs7Z0JBRWpEakUsYUFBYWtDLFNBQVMsa0JBQ3JCLDhEQUFDdEMsbUZBQW9CQTtvQkFBQzZELFdBQVU7Ozs7Ozs7Ozs7OztJQUl4QztJQUVBLHFCQUFPLDhEQUFDbEUsZ0VBQXVCQTtRQUFDK0IsUUFBUUE7UUFBUXJCLFlBQVlBOzs7Ozs7QUFDOUQ7R0ExSU1IOztRQWV5QkQsaUVBQVdBO1FBQ3RCSixxRkFBOEJBO1FBUW5CRSwwRUFBa0JBO1FBRTdDSCxpRkFBc0JBO1FBMkVWRSx5REFBY0E7OztLQXJHeEJJO0FBNElOLCtEQUFlQSwyQkFBMkJBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9HaXZlYXdheXMvQWlyUG9vbEdpdmVhd2F5L0FpclBvb2xEb3RzYW1hR2l2ZWF3YXlDbGFpbS50c3g/ODZiNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTdWNjZXNzQWxlcnRCb3ggfSBmcm9tICdAQ29tcG9uZW50cy9BbGVydEJveCc7XHJcbmltcG9ydCB0b2FzdGVyIGZyb20gJ0BDb21wb25lbnRzL1RvYXN0ZXIvVG9hc3Rlcic7XHJcbmltcG9ydCBEb3RzYW1hV2FsbGV0IGZyb20gJ0BDb21wb25lbnRzL1dlYjNXYWxsZXQvRG90c2FtYS9Eb3RzYW1hV2FsbGV0JztcclxuaW1wb3J0IHsgY29udmVydFRvU3M1OEFkZHJlc3MgfSBmcm9tICdAUm9vdC9oZWxwZXJzL2RvdHNhbWEnO1xyXG5pbXBvcnQge1xyXG4gIEJsb2NrY2hhaW4sXHJcbiAgQmxvY2tjaGFpbkFzc2V0LFxyXG4gIEJsb2NrY2hhaW5Qb29sLFxyXG4gIEdpdmVhd2F5LFxyXG4gIFByb2plY3RFdmVudCxcclxuICBSZXdhcmRTdGF0dXMsXHJcbiAgV2ViM1dhbGxldFR5cGUsXHJcbn0gZnJvbSAnQGFpcmx5ZnQvdHlwZXMnO1xyXG5pbXBvcnQgeyBmb3JtYXRBbW91bnQgfSBmcm9tICdAYWlybHlmdC93ZWIzLWV2bSc7XHJcbmltcG9ydCB7IERvdHNhbWFDb25uZWN0b3JEYXRhIH0gZnJvbSAnQGFpcmx5ZnQvd2ViMy1ldm0taG9va3MnO1xyXG5pbXBvcnQgeyBCaWdOdW1iZXIgfSBmcm9tICdldGhlcnMnO1xyXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IEdpdmVhd2F5VHJhbnNhY3Rpb25IYXNoIGZyb20gJy4uL0dpdmVhd2F5VHJhbnNhY3Rpb25IYXNoJztcclxuaW1wb3J0IHsgdXNlR2V0VXNlckV2ZW50UmV3YXJkcyB9IGZyb20gJy4uL2hvb2tzL3VzZUdldFVzZXJFdmVudFJld2FyZHMnO1xyXG5pbXBvcnQgeyB1c2VEb3RzYW1hQWlyUG9vbEdpdmVhd2F5Q2xhaW0gfSBmcm9tICcuL3VzZUFpclBvb2xHaXZlYXdheUNsYWltJztcclxuaW1wb3J0IHsgdXNlVHJhbnNsYXRpb24gfSBmcm9tICduZXh0LWkxOG5leHQnO1xyXG5pbXBvcnQgeyB1c2VHb29nbGVSZUNhcHRjaGEgfSBmcm9tICdyZWFjdC1nb29nbGUtcmVjYXB0Y2hhLXYzJztcclxuaW1wb3J0IHsgUmVjYXB0Y2hhRGVjbGFyYXRpb24gfSBmcm9tICdAQ29tcG9uZW50cy9SZWNhcHRjaGFEZWNsYXJhdGlvbic7XHJcbmltcG9ydCB7IHVzZUd0bVRyYWNrIH0gZnJvbSAnQFJvb3Qvc2VydmljZXMvdHJhY2tpbmcnO1xyXG5cclxuY29uc3QgQWlyUG9vbERvdHNhbWFHaXZlYXdheUNsYWltID0gKHtcclxuICBnaXZlYXdheSxcclxuICBwcm9qZWN0RXZlbnQsXHJcbiAgYmxvY2tjaGFpbixcclxuICBhc3NldCxcclxuICBhbW91bnQsXHJcbiAgYWlyUG9vbCxcclxufToge1xyXG4gIGJsb2NrY2hhaW46IEJsb2NrY2hhaW47XHJcbiAgZ2l2ZWF3YXk6IEdpdmVhd2F5O1xyXG4gIHByb2plY3RFdmVudDogUHJvamVjdEV2ZW50O1xyXG4gIGFzc2V0OiBCbG9ja2NoYWluQXNzZXQ7XHJcbiAgYW1vdW50OiBCaWdOdW1iZXI7XHJcbiAgYWlyUG9vbDogQmxvY2tjaGFpblBvb2w7XHJcbn0pID0+IHtcclxuICBjb25zdCB7IGNsYWltUmV3YXJkVHJhY2sgfSA9IHVzZUd0bVRyYWNrKCk7XHJcbiAgY29uc3QgeyBjbGFpbSB9ID0gdXNlRG90c2FtYUFpclBvb2xHaXZlYXdheUNsYWltKFxyXG4gICAgcHJvamVjdEV2ZW50LnByb2plY3QuaWQsXHJcbiAgICBwcm9qZWN0RXZlbnQuaWQsXHJcbiAgICBnaXZlYXdheS5pZCxcclxuICAgIGFzc2V0LFxyXG4gICAgYWlyUG9vbCxcclxuICApO1xyXG4gIGNvbnN0IFtpc0NsYWltaW5nLCBzZXRJc0NsYWltaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCB7IGV4ZWN1dGVSZWNhcHRjaGEgfSA9IHVzZUdvb2dsZVJlQ2FwdGNoYSgpO1xyXG4gIGNvbnN0IHsgZGF0YTogdXNlckV2ZW50UmV3YXJkc0RhdGEsIGxvYWRpbmc6IGlzVXNlckV2ZW50UmV3YXJkc0xvYWRpbmcgfSA9XHJcbiAgICB1c2VHZXRVc2VyRXZlbnRSZXdhcmRzKHByb2plY3RFdmVudC5pZCk7XHJcblxyXG4gIGNvbnN0IHByb2Nlc3NpbmcgPSB1c2VyRXZlbnRSZXdhcmRzRGF0YT8udXNlckV2ZW50UmV3YXJkcy5maW5kKFxyXG4gICAgKGl0ZW0pID0+IGl0ZW0uc3RhdHVzID09PSBSZXdhcmRTdGF0dXMuUFJPQ0VTU0lORyxcclxuICApO1xyXG5cclxuICBjb25zdCB0eEhhc2ggPSBbLi4uKHVzZXJFdmVudFJld2FyZHNEYXRhPy51c2VyRXZlbnRSZXdhcmRzIHx8IFtdKV1cclxuICAgIC5zb3J0KChhLCBiKSA9PiB7XHJcbiAgICAgIGNvbnN0IGRhdGVBID0gK25ldyBEYXRlKGEudXBkYXRlZEF0KTtcclxuICAgICAgY29uc3QgZGF0ZUIgPSArbmV3IERhdGUoYi51cGRhdGVkQXQpO1xyXG4gICAgICByZXR1cm4gZGF0ZUIgLSBkYXRlQTtcclxuICAgIH0pXHJcbiAgICAuZmluZCgocmV3YXJkKSA9PiByZXdhcmQudHhIYXNoKT8udHhIYXNoO1xyXG5cclxuICBjb25zdCBoYW5kbGVTdWJtaXQgPSBhc3luYyAoY29ubmVjdG9yRGF0YTogRG90c2FtYUNvbm5lY3RvckRhdGEpID0+IHtcclxuICAgIHNldElzQ2xhaW1pbmcodHJ1ZSk7XHJcbiAgICBsZXQgY2FwdGNoYTogc3RyaW5nIHwgdW5kZWZpbmVkO1xyXG5cclxuICAgIGlmIChwcm9qZWN0RXZlbnQuaXBQcm90ZWN0KSB7XHJcbiAgICAgIGlmICghZXhlY3V0ZVJlY2FwdGNoYSkge1xyXG4gICAgICAgIHRvYXN0ZXIoe1xyXG4gICAgICAgICAgdGl0bGU6ICdGYWlsZWQnLFxyXG4gICAgICAgICAgdGV4dDogJ1JlY2FwdGNoYSBub3QgaW5pdGlhbGl6ZWQnLFxyXG4gICAgICAgICAgdHlwZTogJ2Vycm9yJyxcclxuICAgICAgICB9KTtcclxuICAgICAgICBzZXRJc0NsYWltaW5nKGZhbHNlKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuICAgICAgY2FwdGNoYSA9IGF3YWl0IGV4ZWN1dGVSZWNhcHRjaGEoJ2FpcnBvb2xfZG90c2FtYV9naXZlYXdheV9jbGFpbScpO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHsgYWNjb3VudCB9ID0gY29ubmVjdG9yRGF0YTtcclxuICAgIGlmIChhY2NvdW50KSB7XHJcbiAgICAgIGNvbnN0IGZvcm1hdHRlZEFkZHJlc3MgPSBjb252ZXJ0VG9TczU4QWRkcmVzcyhcclxuICAgICAgICBhY2NvdW50LFxyXG4gICAgICAgIGJsb2NrY2hhaW4uY2hhaW5JZCxcclxuICAgICAgKTtcclxuICAgICAgY29uc3QgZm9ybWF0dGVkQ29ubmVjdG9yRGF0YSA9IHtcclxuICAgICAgICAuLi5jb25uZWN0b3JEYXRhLFxyXG4gICAgICAgIGFjY291bnQ6IGZvcm1hdHRlZEFkZHJlc3MsXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBjbGFpbSh7XHJcbiAgICAgICAgY29ubmVjdG9yRGF0YTogZm9ybWF0dGVkQ29ubmVjdG9yRGF0YSxcclxuICAgICAgICBvbkVycm9yOiAoZXJyKSA9PiB7XHJcbiAgICAgICAgICB0b2FzdGVyKHtcclxuICAgICAgICAgICAgdGl0bGU6ICdGYWlsZWQnLFxyXG4gICAgICAgICAgICB0ZXh0OiBlcnIubWVzc2FnZSxcclxuICAgICAgICAgICAgdHlwZTogJ2Vycm9yJyxcclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgc2V0SXNDbGFpbWluZyhmYWxzZSk7XHJcbiAgICAgICAgfSxcclxuICAgICAgICBvblN1Y2Nlc3M6ICgpID0+IHtcclxuICAgICAgICAgIGNsYWltUmV3YXJkVHJhY2soe1xyXG4gICAgICAgICAgICBwcm9qZWN0SWQ6IHByb2plY3RFdmVudC5wcm9qZWN0LmlkLFxyXG4gICAgICAgICAgICBldmVudElkOiBwcm9qZWN0RXZlbnQuaWQsXHJcbiAgICAgICAgICAgIHByb2plY3RUaXRsZTogcHJvamVjdEV2ZW50LnByb2plY3QubmFtZSxcclxuICAgICAgICAgICAgZXZlbnRUaXRsZTogcHJvamVjdEV2ZW50LnRpdGxlLFxyXG4gICAgICAgICAgICBnaXZlYXdheUlkOiBnaXZlYXdheS5pZCxcclxuICAgICAgICAgICAgZ2l2ZWF3YXlUaXRsZTogZ2l2ZWF3YXkudGl0bGUgfHwgJycsXHJcbiAgICAgICAgICB9KTtcclxuICAgICAgICAgIHRvYXN0ZXIoe1xyXG4gICAgICAgICAgICB0aXRsZTogJ1N1Ym1pdHRlZCcsXHJcbiAgICAgICAgICAgIHRleHQ6ICdZb3VyIGNsYWltIHJlcXVlc3QgaGFzIGJlZW4gc3VibWl0dGVkLCBjaGVjayB5b3VyIG5vdGlmaWNhdGlvbnMgZm9yIGFuIHVwZGF0ZS4nLFxyXG4gICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsXHJcbiAgICAgICAgICB9KTtcclxuICAgICAgICAgIHNldElzQ2xhaW1pbmcoZmFsc2UpO1xyXG4gICAgICAgIH0sXHJcbiAgICAgICAgY2FwdGNoYSxcclxuICAgICAgfSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBzZXRJc0NsYWltaW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCB7IHQgfSA9IHVzZVRyYW5zbGF0aW9uKCd0cmFuc2xhdGlvbicpO1xyXG5cclxuICBpZiAocHJvY2Vzc2luZykge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPFN1Y2Nlc3NBbGVydEJveFxyXG4gICAgICAgIHRpdGxlPXt0KCdnaXZlYXdheS5haXJUb2tlblBvb2wuc3VjY2Vzc1RpdGxlJyl9XHJcbiAgICAgICAgc3VidGl0bGU9e3QoJ2dpdmVhd2F5LmFpclRva2VuUG9vbC5zdWNjZXNzU3VidGl0bGUnKX1cclxuICAgICAgLz5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICBpZiAoIWFtb3VudC5pc1plcm8oKSkge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICA8RG90c2FtYVdhbGxldFxyXG4gICAgICAgICAgYmxvY2tjaGFpbj17YmxvY2tjaGFpbn1cclxuICAgICAgICAgIGJ1dHRvbj17e1xyXG4gICAgICAgICAgICBjb25maXJtOiB7XHJcbiAgICAgICAgICAgICAgZW5hYmxlOiB0cnVlLFxyXG4gICAgICAgICAgICAgIGxvYWRpbmc6IGlzQ2xhaW1pbmcgfHwgaXNVc2VyRXZlbnRSZXdhcmRzTG9hZGluZyxcclxuICAgICAgICAgICAgICB0ZXh0OiBgQ2xhaW0gJHtmb3JtYXRBbW91bnQoXHJcbiAgICAgICAgICAgICAgICBhbW91bnQ/LnRvU3RyaW5nKCksXHJcbiAgICAgICAgICAgICAgICBhc3NldC5kZWNpbWFscyxcclxuICAgICAgICAgICAgICApfSAke2Fzc2V0LnRpY2tlcn0gdXNpbmcgYCxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICBvblN1Y2Nlc3M9e2hhbmRsZVN1Ym1pdH1cclxuICAgICAgICAgIGV4Y2x1ZGVkV2FsbGV0cz17W1dlYjNXYWxsZXRUeXBlLkRPVFNBTUFfTUFOVUFMXX1cclxuICAgICAgICAvPlxyXG4gICAgICAgIHtwcm9qZWN0RXZlbnQuaXBQcm90ZWN0ICYmIChcclxuICAgICAgICAgIDxSZWNhcHRjaGFEZWNsYXJhdGlvbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtY3MgdGV4dC1jZW50ZXJcIiAvPlxyXG4gICAgICAgICl9XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIHJldHVybiA8R2l2ZWF3YXlUcmFuc2FjdGlvbkhhc2ggdHhIYXNoPXt0eEhhc2h9IGJsb2NrY2hhaW49e2Jsb2NrY2hhaW59IC8+O1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgQWlyUG9vbERvdHNhbWFHaXZlYXdheUNsYWltO1xyXG4iXSwibmFtZXMiOlsiU3VjY2Vzc0FsZXJ0Qm94IiwidG9hc3RlciIsIkRvdHNhbWFXYWxsZXQiLCJjb252ZXJ0VG9TczU4QWRkcmVzcyIsIlJld2FyZFN0YXR1cyIsIldlYjNXYWxsZXRUeXBlIiwiZm9ybWF0QW1vdW50IiwidXNlU3RhdGUiLCJHaXZlYXdheVRyYW5zYWN0aW9uSGFzaCIsInVzZUdldFVzZXJFdmVudFJld2FyZHMiLCJ1c2VEb3RzYW1hQWlyUG9vbEdpdmVhd2F5Q2xhaW0iLCJ1c2VUcmFuc2xhdGlvbiIsInVzZUdvb2dsZVJlQ2FwdGNoYSIsIlJlY2FwdGNoYURlY2xhcmF0aW9uIiwidXNlR3RtVHJhY2siLCJBaXJQb29sRG90c2FtYUdpdmVhd2F5Q2xhaW0iLCJnaXZlYXdheSIsInByb2plY3RFdmVudCIsImJsb2NrY2hhaW4iLCJhc3NldCIsImFtb3VudCIsImFpclBvb2wiLCJjbGFpbVJld2FyZFRyYWNrIiwiY2xhaW0iLCJwcm9qZWN0IiwiaWQiLCJpc0NsYWltaW5nIiwic2V0SXNDbGFpbWluZyIsImV4ZWN1dGVSZWNhcHRjaGEiLCJkYXRhIiwidXNlckV2ZW50UmV3YXJkc0RhdGEiLCJsb2FkaW5nIiwiaXNVc2VyRXZlbnRSZXdhcmRzTG9hZGluZyIsInByb2Nlc3NpbmciLCJ1c2VyRXZlbnRSZXdhcmRzIiwiZmluZCIsIml0ZW0iLCJzdGF0dXMiLCJQUk9DRVNTSU5HIiwidHhIYXNoIiwic29ydCIsImEiLCJiIiwiZGF0ZUEiLCJEYXRlIiwidXBkYXRlZEF0IiwiZGF0ZUIiLCJyZXdhcmQiLCJoYW5kbGVTdWJtaXQiLCJjb25uZWN0b3JEYXRhIiwiY2FwdGNoYSIsImlwUHJvdGVjdCIsInRpdGxlIiwidGV4dCIsInR5cGUiLCJhY2NvdW50IiwiZm9ybWF0dGVkQWRkcmVzcyIsImNoYWluSWQiLCJmb3JtYXR0ZWRDb25uZWN0b3JEYXRhIiwib25FcnJvciIsImVyciIsIm1lc3NhZ2UiLCJvblN1Y2Nlc3MiLCJwcm9qZWN0SWQiLCJldmVudElkIiwicHJvamVjdFRpdGxlIiwibmFtZSIsImV2ZW50VGl0bGUiLCJnaXZlYXdheUlkIiwiZ2l2ZWF3YXlUaXRsZSIsInQiLCJzdWJ0aXRsZSIsImlzWmVybyIsImRpdiIsImNsYXNzTmFtZSIsImJ1dHRvbiIsImNvbmZpcm0iLCJlbmFibGUiLCJ0b1N0cmluZyIsImRlY2ltYWxzIiwidGlja2VyIiwiZXhjbHVkZWRXYWxsZXRzIiwiRE9UU0FNQV9NQU5VQUwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Giveaways/AirPoolGiveaway/AirPoolDotsamaGiveawayClaim.tsx\n"));

/***/ }),

/***/ "./components/Giveaways/AirPoolGiveaway/useAirPoolGiveawayClaim.tsx":
/*!**************************************************************************!*\
  !*** ./components/Giveaways/AirPoolGiveaway/useAirPoolGiveawayClaim.tsx ***!
  \**************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDotsamaAirPoolGiveawayClaim: function() { return /* binding */ useDotsamaAirPoolGiveawayClaim; },\n/* harmony export */   useEvmAirPoolGiveawayClaim: function() { return /* binding */ useEvmAirPoolGiveawayClaim; }\n/* harmony export */ });\n/* harmony import */ var _Hooks_useGiveawayTxHash__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @Hooks/useGiveawayTxHash */ \"./hooks/useGiveawayTxHash.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _hooks_useGetContract__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/useGetContract */ \"./components/Giveaways/hooks/useGetContract.ts\");\n/* harmony import */ var _airpool_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./airpool-giveaway.gql */ \"./components/Giveaways/AirPoolGiveaway/airpool-giveaway.gql.ts\");\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nconst claimERC721AirToken = (contractAddress, connectorData, rewardCertificate, airPool)=>{\n    const { provider, account } = connectorData;\n    const contract = _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.ERC721AirPoolController__factory.connect(contractAddress, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.getSigner)(provider, account));\n    const { certificate, window, windowLimit, amount, claimIds } = rewardCertificate;\n    //For EVM case - poolId should always be present\n    if (!airPool.poolId) {\n        throw new Error(\"Invalid Reward Pool ID\");\n    }\n    if (claimIds.length === 1) {\n        return contract.claim(account, airPool.poolId, claimIds[0], amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.formatCertificate)(certificate));\n    }\n    return contract.claimBatch(account, airPool.poolId, claimIds, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.formatCertificate)(certificate));\n};\nconst claimERC1155AirToken = (contractAddress, connectorData, rewardCertificate, airPool)=>{\n    const { provider, account } = connectorData;\n    const contract = _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.ERC1155AirPoolController__factory.connect(contractAddress, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.getSigner)(provider, account));\n    const { certificate, window, windowLimit, amount, claimIds } = rewardCertificate;\n    if (!airPool.poolId) {\n        throw new Error(\"Invalid Reward Pool ID\");\n    }\n    if (claimIds.length === 1) {\n        return contract.claim(account, airPool.poolId, claimIds[0], amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.formatCertificate)(certificate));\n    }\n    return contract.claimBatch(account, airPool.poolId, claimIds, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.formatCertificate)(certificate));\n};\nconst claimERC20AirToken = (contractAddress, connectorData, rewardCertificate, airPool)=>{\n    const { provider, account } = connectorData;\n    const contract = _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.ERC20AirPoolController__factory.connect(contractAddress, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.getSigner)(provider, account));\n    const { certificate, window, windowLimit, amount, claimIds } = rewardCertificate;\n    if (!airPool.poolId) {\n        throw new Error(\"Invalid Reward Pool ID\");\n    }\n    if (claimIds.length === 1) {\n        return contract.claim(account, airPool.poolId, claimIds[0], amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.formatCertificate)(certificate));\n    }\n    return contract.claimBatch(account, airPool.poolId, claimIds, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.formatCertificate)(certificate));\n};\nfunction useEvmAirPoolGiveawayClaim(projectId, projectEventId, giveawayId, blockchain, asset, airPool) {\n    var _contractData_contract;\n    _s();\n    const { update } = (0,_Hooks_useGiveawayTxHash__WEBPACK_IMPORTED_MODULE_0__.useGiveawayTxHash)(giveawayId);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [getAirTokenRewardCertificate] = (0,_airpool_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__.useGetAirPoolRewardCertificate)();\n    const [sync] = (0,_airpool_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__.useSyncClaimAirPoolGiveaway)();\n    const assetType = asset === null || asset === void 0 ? void 0 : asset.assetType;\n    const contractType = assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AssetType.ERC1155 ? _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.ContractType.ERC1155_AIRPOOL : assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AssetType.ERC20 || assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AssetType.NATIVE ? _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.ContractType.ERC20_AIRPOOL : assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AssetType.ERC721 ? _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.ContractType.ERC721_AIRPOOL : undefined;\n    const { data: contractData, loading: contractLoading } = (0,_hooks_useGetContract__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(blockchain === null || blockchain === void 0 ? void 0 : blockchain.id, contractType);\n    const contractAddress = contractData === null || contractData === void 0 ? void 0 : (_contractData_contract = contractData.contract) === null || _contractData_contract === void 0 ? void 0 : _contractData_contract.address;\n    const mountedRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        mountedRef.current = true;\n        return ()=>{\n            mountedRef.current = false;\n        };\n    }, []);\n    const claim = async (param)=>{\n        let { connectorData, onError, onSuccess, gasLess = false, captcha } = param;\n        const { provider, account } = connectorData;\n        if (!gasLess && !provider || !account || !contractAddress || !airPool || !asset) return;\n        mountedRef.current && setLoading(true);\n        try {\n            const { data } = await getAirTokenRewardCertificate({\n                variables: {\n                    projectId,\n                    eventId: projectEventId,\n                    giveawayId,\n                    userAddress: account,\n                    captcha\n                },\n                context: {\n                    gasLess\n                }\n            });\n            if (gasLess) {\n                var _data_claimAirPoolGiveaway;\n                const txHash = data === null || data === void 0 ? void 0 : (_data_claimAirPoolGiveaway = data.claimAirPoolGiveaway) === null || _data_claimAirPoolGiveaway === void 0 ? void 0 : _data_claimAirPoolGiveaway.txHash;\n                if (!txHash) throw new Error(\"Claim Failed\");\n                update(txHash);\n                onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                    contractAddress,\n                    txHash\n                });\n            } else {\n                const result = data === null || data === void 0 ? void 0 : data.claimAirPoolGiveaway;\n                if (!result || !result.certificate) throw new Error(\"Invalid certificate\");\n                let tx;\n                if (asset.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AssetType.ERC20 || asset.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AssetType.NATIVE) {\n                    tx = await claimERC20AirToken(contractAddress, connectorData, result, airPool);\n                } else if (asset.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AssetType.ERC1155) {\n                    tx = await claimERC1155AirToken(contractAddress, connectorData, result, airPool);\n                } else if (asset.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.AssetType.ERC721) {\n                    tx = await claimERC721AirToken(contractAddress, connectorData, result, airPool);\n                } else {\n                    throw new Error(\"Invalid asset\");\n                }\n                update(tx.hash);\n                await tx.wait();\n                await sync({\n                    variables: {\n                        ids: result.raw.map((item)=>item.id),\n                        giveawayId: giveawayId\n                    },\n                    context: {\n                        eventId: projectEventId\n                    }\n                });\n                onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                    contractAddress,\n                    tx\n                });\n            }\n        } catch (err) {\n            let error;\n            if ((err === null || err === void 0 ? void 0 : err.code) === \"ACTION_REJECTED\") {\n                error = new Error(\"Tx Signature: User denied transaction signature.\");\n            }\n            onError === null || onError === void 0 ? void 0 : onError(error || err);\n        } finally{\n            mountedRef.current && setLoading(false);\n        }\n    };\n    return {\n        claim,\n        loading: loading || contractLoading\n    };\n}\n_s(useEvmAirPoolGiveawayClaim, \"TS98jnMNSFVp2vDS2MuYrju2Y0Y=\", false, function() {\n    return [\n        _Hooks_useGiveawayTxHash__WEBPACK_IMPORTED_MODULE_0__.useGiveawayTxHash,\n        _airpool_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__.useGetAirPoolRewardCertificate,\n        _airpool_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__.useSyncClaimAirPoolGiveaway,\n        _hooks_useGetContract__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\nfunction useDotsamaAirPoolGiveawayClaim(projectId, projectEventId, giveawayId, asset, airPool) {\n    _s1();\n    const [claimDotsama] = (0,_airpool_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__.useClaimDotsamaAirPoolGiveaway)();\n    const claim = async (param)=>{\n        let { connectorData, onError, onSuccess, captcha } = param;\n        const { account } = connectorData;\n        if (!account || !airPool || !asset) return;\n        try {\n            await claimDotsama({\n                variables: {\n                    projectId,\n                    eventId: projectEventId,\n                    giveawayId,\n                    userAddress: account,\n                    captcha\n                }\n            });\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n        } catch (err) {\n            onError === null || onError === void 0 ? void 0 : onError(err);\n        }\n    };\n    return {\n        claim\n    };\n}\n_s1(useDotsamaAirPoolGiveawayClaim, \"2AvzVqPfJy8PlRi7AVq8XZfeVrY=\", false, function() {\n    return [\n        _airpool_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__.useClaimDotsamaAirPoolGiveaway\n    ];\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/AirPoolGiveaway/useAirPoolGiveawayClaim.tsx\n"));

/***/ }),

/***/ "./components/Giveaways/GiveawayTransactionHash.tsx":
/*!**********************************************************!*\
  !*** ./components/Giveaways/GiveawayTransactionHash.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_TransactionResult__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/TransactionResult */ \"./components/TransactionResult.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst GiveawayTransactionHash = (param)=>{\n    let { txHash, blockchain } = param;\n    if (!txHash) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayTransactionHash.tsx\",\n        lineNumber: 12,\n        columnNumber: 23\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"!my-0 flex justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TransactionResult__WEBPACK_IMPORTED_MODULE_1__.TransactionResult, {\n            txHash: txHash,\n            blockchain: blockchain\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayTransactionHash.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayTransactionHash.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined);\n};\n_c = GiveawayTransactionHash;\n/* harmony default export */ __webpack_exports__[\"default\"] = (GiveawayTransactionHash);\nvar _c;\n$RefreshReg$(_c, \"GiveawayTransactionHash\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0dpdmVhd2F5cy9HaXZlYXdheVRyYW5zYWN0aW9uSGFzaC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUNrRTtBQUNqQztBQUVqQyxNQUFNRSwwQkFBMEI7UUFBQyxFQUMvQkMsTUFBTSxFQUNOQyxVQUFVLEVBSVg7SUFDQyxJQUFJLENBQUNELFFBQVEscUJBQU8sOERBQUNGLDJDQUFRQTs7Ozs7SUFDN0IscUJBQ0UsOERBQUNJO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNOLDRFQUFpQkE7WUFBQ0csUUFBUUE7WUFBUUMsWUFBWUE7Ozs7Ozs7Ozs7O0FBR3JEO0tBYk1GO0FBZU4sK0RBQWVBLHVCQUF1QkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL0dpdmVhd2F5cy9HaXZlYXdheVRyYW5zYWN0aW9uSGFzaC50c3g/ODZjZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBCbG9ja2NoYWluIH0gZnJvbSAnQGFpcmx5ZnQvdHlwZXMnO1xyXG5pbXBvcnQgeyBUcmFuc2FjdGlvblJlc3VsdCB9IGZyb20gJ0BDb21wb25lbnRzL1RyYW5zYWN0aW9uUmVzdWx0JztcclxuaW1wb3J0IHsgRnJhZ21lbnQgfSBmcm9tICdyZWFjdCc7XHJcblxyXG5jb25zdCBHaXZlYXdheVRyYW5zYWN0aW9uSGFzaCA9ICh7XHJcbiAgdHhIYXNoLFxyXG4gIGJsb2NrY2hhaW4sXHJcbn06IHtcclxuICB0eEhhc2g/OiBzdHJpbmcgfCBudWxsO1xyXG4gIGJsb2NrY2hhaW46IEJsb2NrY2hhaW47XHJcbn0pID0+IHtcclxuICBpZiAoIXR4SGFzaCkgcmV0dXJuIDxGcmFnbWVudCAvPjtcclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCIhbXktMCBmbGV4IGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgIDxUcmFuc2FjdGlvblJlc3VsdCB0eEhhc2g9e3R4SGFzaH0gYmxvY2tjaGFpbj17YmxvY2tjaGFpbn0gLz5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBHaXZlYXdheVRyYW5zYWN0aW9uSGFzaDtcclxuIl0sIm5hbWVzIjpbIlRyYW5zYWN0aW9uUmVzdWx0IiwiRnJhZ21lbnQiLCJHaXZlYXdheVRyYW5zYWN0aW9uSGFzaCIsInR4SGFzaCIsImJsb2NrY2hhaW4iLCJkaXYiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Giveaways/GiveawayTransactionHash.tsx\n"));

/***/ }),

/***/ "./components/Giveaways/hooks/useGetContract.ts":
/*!******************************************************!*\
  !*** ./components/Giveaways/hooks/useGetContract.ts ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ useGetContract; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query contract($blockchainId: ID!, $contractType: ContractType!) {\\n    contract(blockchainId: $blockchainId, contractType: $contractType) {\\n      address\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\n\nconst GET_CONTRACT = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject());\nfunction useGetContract(blockchainId, contractType) {\n    return (0,_apollo_client__WEBPACK_IMPORTED_MODULE_1__.useQuery)(GET_CONTRACT, {\n        variables: {\n            blockchainId: blockchainId,\n            contractType: contractType\n        },\n        skip: !blockchainId || !contractType\n    });\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0dpdmVhd2F5cy9ob29rcy91c2VHZXRDb250cmFjdC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQytDO0FBRS9DLE1BQU1FLGVBQWVGLG1EQUFHQTtBQVFULFNBQVNHLGVBQ3RCQyxZQUFnQyxFQUNoQ0MsWUFBc0M7SUFFdEMsT0FBT0osd0RBQVFBLENBQTZDQyxjQUFjO1FBQ3hFSSxXQUFXO1lBQ1RGLGNBQWNBO1lBQ2RDLGNBQWNBO1FBQ2hCO1FBQ0FFLE1BQU0sQ0FBQ0gsZ0JBQWdCLENBQUNDO0lBQzFCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9HaXZlYXdheXMvaG9va3MvdXNlR2V0Q29udHJhY3QudHM/NDJmZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDb250cmFjdCwgQ29udHJhY3RUeXBlLCBRdWVyeV9jb250cmFjdEFyZ3MgfSBmcm9tICdAYWlybHlmdC90eXBlcyc7XHJcbmltcG9ydCB7IGdxbCwgdXNlUXVlcnkgfSBmcm9tICdAYXBvbGxvL2NsaWVudCc7XHJcblxyXG5jb25zdCBHRVRfQ09OVFJBQ1QgPSBncWxgXHJcbiAgcXVlcnkgY29udHJhY3QoJGJsb2NrY2hhaW5JZDogSUQhLCAkY29udHJhY3RUeXBlOiBDb250cmFjdFR5cGUhKSB7XHJcbiAgICBjb250cmFjdChibG9ja2NoYWluSWQ6ICRibG9ja2NoYWluSWQsIGNvbnRyYWN0VHlwZTogJGNvbnRyYWN0VHlwZSkge1xyXG4gICAgICBhZGRyZXNzXHJcbiAgICB9XHJcbiAgfVxyXG5gO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlR2V0Q29udHJhY3QoXHJcbiAgYmxvY2tjaGFpbklkOiBzdHJpbmcgfCB1bmRlZmluZWQsXHJcbiAgY29udHJhY3RUeXBlOiBDb250cmFjdFR5cGUgfCB1bmRlZmluZWQsXHJcbikge1xyXG4gIHJldHVybiB1c2VRdWVyeTx7IGNvbnRyYWN0OiBDb250cmFjdCB9LCBRdWVyeV9jb250cmFjdEFyZ3M+KEdFVF9DT05UUkFDVCwge1xyXG4gICAgdmFyaWFibGVzOiB7XHJcbiAgICAgIGJsb2NrY2hhaW5JZDogYmxvY2tjaGFpbklkIGFzIHN0cmluZyxcclxuICAgICAgY29udHJhY3RUeXBlOiBjb250cmFjdFR5cGUgYXMgQ29udHJhY3RUeXBlLFxyXG4gICAgfSxcclxuICAgIHNraXA6ICFibG9ja2NoYWluSWQgfHwgIWNvbnRyYWN0VHlwZSxcclxuICB9KTtcclxufVxyXG4iXSwibmFtZXMiOlsiZ3FsIiwidXNlUXVlcnkiLCJHRVRfQ09OVFJBQ1QiLCJ1c2VHZXRDb250cmFjdCIsImJsb2NrY2hhaW5JZCIsImNvbnRyYWN0VHlwZSIsInZhcmlhYmxlcyIsInNraXAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Giveaways/hooks/useGetContract.ts\n"));

/***/ }),

/***/ "./components/RecaptchaDeclaration.tsx":
/*!*********************************************!*\
  !*** ./components/RecaptchaDeclaration.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecaptchaDeclaration: function() { return /* binding */ RecaptchaDeclaration; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst RecaptchaDeclaration = (param)=>{\n    let { className = \"text-sm\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        className: className,\n        children: [\n            \"This site is protected by reCAPTCHA and the Google\",\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                className: \"underline text-link cursor-pointer\",\n                href: \"https://policies.google.com/privacy\",\n                children: \"Privacy Policy\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RecaptchaDeclaration.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined),\n            \" \",\n            \"and\",\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                className: \"underline text-link cursor-pointer\",\n                href: \"https://policies.google.com/terms\",\n                children: \"Terms of Service\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RecaptchaDeclaration.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined),\n            \" \",\n            \"apply.\"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RecaptchaDeclaration.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n_c = RecaptchaDeclaration;\nvar _c;\n$RefreshReg$(_c, \"RecaptchaDeclaration\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1JlY2FwdGNoYURlY2xhcmF0aW9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFFbkIsTUFBTUMsdUJBQXVCO1FBQUMsRUFDbkNDLFlBQVksU0FBUyxFQUd0QjtJQUNDLHFCQUNFLDhEQUFDQztRQUFFRCxXQUFXQTs7WUFBVztZQUM0QjswQkFDbkQsOERBQUNFO2dCQUNDRixXQUFVO2dCQUNWRyxNQUFLOzBCQUNOOzs7Ozs7WUFFSTtZQUFJO1lBQ0w7MEJBQ0osOERBQUNEO2dCQUNDRixXQUFVO2dCQUNWRyxNQUFLOzBCQUNOOzs7Ozs7WUFFSTtZQUFJOzs7Ozs7O0FBSWYsRUFBRTtLQXhCV0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9SZWNhcHRjaGFEZWNsYXJhdGlvbi50c3g/MGYxZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5cclxuZXhwb3J0IGNvbnN0IFJlY2FwdGNoYURlY2xhcmF0aW9uID0gKHtcclxuICBjbGFzc05hbWUgPSAndGV4dC1zbScsXHJcbn06IHtcclxuICBjbGFzc05hbWU/OiBzdHJpbmc7XHJcbn0pID0+IHtcclxuICByZXR1cm4gKFxyXG4gICAgPHAgY2xhc3NOYW1lPXtjbGFzc05hbWV9PlxyXG4gICAgICBUaGlzIHNpdGUgaXMgcHJvdGVjdGVkIGJ5IHJlQ0FQVENIQSBhbmQgdGhlIEdvb2dsZXsnICd9XHJcbiAgICAgIDxhXHJcbiAgICAgICAgY2xhc3NOYW1lPVwidW5kZXJsaW5lIHRleHQtbGluayBjdXJzb3ItcG9pbnRlclwiXHJcbiAgICAgICAgaHJlZj1cImh0dHBzOi8vcG9saWNpZXMuZ29vZ2xlLmNvbS9wcml2YWN5XCJcclxuICAgICAgPlxyXG4gICAgICAgIFByaXZhY3kgUG9saWN5XHJcbiAgICAgIDwvYT57JyAnfVxyXG4gICAgICBhbmR7JyAnfVxyXG4gICAgICA8YVxyXG4gICAgICAgIGNsYXNzTmFtZT1cInVuZGVybGluZSB0ZXh0LWxpbmsgY3Vyc29yLXBvaW50ZXJcIlxyXG4gICAgICAgIGhyZWY9XCJodHRwczovL3BvbGljaWVzLmdvb2dsZS5jb20vdGVybXNcIlxyXG4gICAgICA+XHJcbiAgICAgICAgVGVybXMgb2YgU2VydmljZVxyXG4gICAgICA8L2E+eycgJ31cclxuICAgICAgYXBwbHkuXHJcbiAgICA8L3A+XHJcbiAgKTtcclxufTtcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiUmVjYXB0Y2hhRGVjbGFyYXRpb24iLCJjbGFzc05hbWUiLCJwIiwiYSIsImhyZWYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/RecaptchaDeclaration.tsx\n"));

/***/ }),

/***/ "./components/TransactionResult.tsx":
/*!******************************************!*\
  !*** ./components/TransactionResult.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransactionResult: function() { return /* binding */ TransactionResult; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _BlockExplorerLink__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BlockExplorerLink */ \"./components/BlockExplorerLink.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Loaders_SpinLoader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Loaders/SpinLoader */ \"./components/Loaders/SpinLoader.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction TransactionResult(param) {\n    let { blockchain, tx, txHash } = param;\n    _s();\n    const [broadcasting, setBroadcasting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        let mounted = true;\n        if (!tx || !setBroadcasting || !setError) return;\n        const wait = async ()=>{\n            mounted && setBroadcasting(true);\n            try {\n                await tx.wait();\n            } catch (err) {\n                mounted && setError(true);\n            }\n            mounted && setBroadcasting(false);\n        };\n        wait();\n        return ()=>{\n            mounted = false;\n        };\n    }, [\n        tx,\n        setBroadcasting,\n        setError\n    ]);\n    var _tx_hash;\n    const hash = (_tx_hash = tx === null || tx === void 0 ? void 0 : tx.hash) !== null && _tx_hash !== void 0 ? _tx_hash : txHash;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2\",\n        children: [\n            broadcasting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Loaders_SpinLoader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-bold\",\n                        children: \"Waiting for transaction to be included in the block\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: [\n                    \"Transaction failed due to \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n                lineNumber: 52,\n                columnNumber: 17\n            }, this),\n            blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BlockExplorerLink__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                hash: hash,\n                blockExplorerUrls: blockchain.blockExplorerUrls\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_s(TransactionResult, \"LHBeVubWEHDDvHeefizlWBhn1+s=\");\n_c = TransactionResult;\nvar _c;\n$RefreshReg$(_c, \"TransactionResult\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/TransactionResult.tsx\n"));

/***/ }),

/***/ "./components/Web3Wallet/Dotsama/DotsamaWallet.tsx":
/*!*********************************************************!*\
  !*** ./components/Web3Wallet/Dotsama/DotsamaWallet.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DotsamaWallet; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm-hooks */ \"../web3-evm-hooks/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/DropdownList/DropdownList */ \"./components/DropdownList/DropdownList.tsx\");\n/* harmony import */ var _Components_DropdownList_DropdownListItem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/DropdownList/DropdownListItem */ \"./components/DropdownList/DropdownListItem.tsx\");\n/* harmony import */ var _Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Tasks/components/AppStoreIconRenderer */ \"./components/Tasks/components/AppStoreIconRenderer.tsx\");\n/* harmony import */ var _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @Components/Toaster/Toaster */ \"./components/Toaster/Toaster.tsx\");\n/* harmony import */ var _polkadot_extension_dapp__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @polkadot/extension-dapp */ \"./node_modules/@polkadot/extension-dapp/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _Web3WalletRenderer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../Web3WalletRenderer */ \"./components/Web3Wallet/Web3WalletRenderer.tsx\");\n/* harmony import */ var _Root_context_AppContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @Root/context/AppContext */ \"./context/AppContext.tsx\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction DotsamaWallet(param) {\n    let { onError, ...props } = param;\n    var _useWalletStore, _selected_config;\n    _s();\n    const wallets = ((_useWalletStore = (0,_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__.useWalletStore)((state)=>state.walletCategory.find((category)=>category.categoryType === _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__.Web3WalletCategoryType.DOTSAMA))) === null || _useWalletStore === void 0 ? void 0 : _useWalletStore.wallets) || [];\n    var _props_excludedWallets;\n    const excludedWallets = (_props_excludedWallets = props.excludedWallets) !== null && _props_excludedWallets !== void 0 ? _props_excludedWallets : [];\n    const { state: { isWidget } } = (0,_Root_context_AppContext__WEBPACK_IMPORTED_MODULE_8__.useAppContext)();\n    if (isWidget) {\n        excludedWallets.push(_airlyft_types__WEBPACK_IMPORTED_MODULE_9__.Web3WalletType.DOTSAMA_POLKADOT_JS, _airlyft_types__WEBPACK_IMPORTED_MODULE_9__.Web3WalletType.DOTSAMA_NOVA);\n    }\n    let filteredWallets = wallets;\n    if (excludedWallets === null || excludedWallets === void 0 ? void 0 : excludedWallets.length) {\n        filteredWallets = wallets.filter((wallet)=>!(excludedWallets === null || excludedWallets === void 0 ? void 0 : excludedWallets.includes(wallet.walletType)));\n    }\n    const [selected, setSelected] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(filteredWallets === null || filteredWallets === void 0 ? void 0 : filteredWallets[0]);\n    const [injectedExtensions, setInjectedExtensions] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        if (!window || !_polkadot_extension_dapp__WEBPACK_IMPORTED_MODULE_10__.isWeb3Injected || !(filteredWallets === null || filteredWallets === void 0 ? void 0 : filteredWallets.length)) {\n            return;\n        }\n        const extensions = window.injectedWeb3;\n        setInjectedExtensions(extensions);\n    }, [\n        window,\n        _polkadot_extension_dapp__WEBPACK_IMPORTED_MODULE_10__.isWeb3Injected,\n        filteredWallets\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-5 mt-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-[90]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    data: filteredWallets,\n                    selected: selected,\n                    onChange: (item)=>setSelected(item),\n                    renderItem: (item, isButton)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_DropdownList_DropdownListItem__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            title: item.title,\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                iconKey: item.walletType,\n                                className: \"h-8 w-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 17\n                            }, void 0),\n                            selected: item.walletType === (selected === null || selected === void 0 ? void 0 : selected.walletType),\n                            button: isButton\n                        }, item.title, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            selected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Web3WalletRenderer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                walletType: selected.walletType,\n                categoryType: _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__.Web3WalletCategoryType.DOTSAMA,\n                placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                props: {\n                    wallet: selected,\n                    onError: (err)=>{\n                        (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_5__[\"default\"])({\n                            title: \"Error\",\n                            text: err,\n                            type: \"error\"\n                        });\n                        onError === null || onError === void 0 ? void 0 : onError(err);\n                    },\n                    ...props,\n                    ...((_selected_config = selected.config) === null || _selected_config === void 0 ? void 0 : _selected_config.name) ? {\n                        injectedExtension: injectedExtensions[selected.config.name]\n                    } : {},\n                    excludedWallets\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s(DotsamaWallet, \"Xw7EUvcZb0YrWSTrKH8jl/lk354=\", false, function() {\n    return [\n        _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__.useWalletStore,\n        _Root_context_AppContext__WEBPACK_IMPORTED_MODULE_8__.useAppContext\n    ];\n});\n_c = DotsamaWallet;\nvar _c;\n$RefreshReg$(_c, \"DotsamaWallet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Web3Wallet/Dotsama/DotsamaWallet.tsx\n"));

/***/ }),

/***/ "./components/Web3Wallet/Web3WalletRenderer.tsx":
/*!******************************************************!*\
  !*** ./components/Web3Wallet/Web3WalletRenderer.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ WalletRenderer; },\n/* harmony export */   rendererPath: function() { return /* binding */ rendererPath; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Root/utils/string-utils */ \"./utils/string-utils.ts\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Components_Loaders_ParagraphLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Loaders/ParagraphLoader */ \"./components/Loaders/ParagraphLoader.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst importProvider = (path, placeholder)=>next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__(\"./components/Web3Wallet lazy recursive ^\\\\.\\\\/.*$\")(\"./\".concat(path)), {\n        ssr: false,\n        loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_ParagraphLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Web3WalletRenderer.tsx\",\n                lineNumber: 20,\n                columnNumber: 20\n            }, undefined)\n    });\nconst rendererPath = (categoryType, walletType)=>{\n    const path = (0,_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_2__.capitalizeFirstLetter)((0,_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_2__.camelCased)(categoryType));\n    const fileName = (0,_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_2__.capitalizeFirstLetter)((0,_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_2__.camelCased)(walletType));\n    return \"\".concat(path, \"/\").concat(fileName);\n};\nfunction WalletRenderer(param) {\n    let { categoryType, walletType, placeholder, props } = param;\n    _s();\n    const View = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>importProvider(rendererPath(categoryType, walletType), placeholder), [\n        walletType,\n        categoryType\n    ]);\n    return View ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(View, {\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Web3WalletRenderer.tsx\",\n        lineNumber: 44,\n        columnNumber: 17\n    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Web3WalletRenderer.tsx\",\n        lineNumber: 44,\n        columnNumber: 48\n    }, this);\n}\n_s(WalletRenderer, \"AuOrCRe4asKUtwi/uq401gGfadw=\");\n_c = WalletRenderer;\nvar _c;\n$RefreshReg$(_c, \"WalletRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Web3Wallet/Web3WalletRenderer.tsx\n"));

/***/ }),

/***/ "./hooks/useGiveawayTxHash.ts":
/*!************************************!*\
  !*** ./hooks/useGiveawayTxHash.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGiveawayTxHash: function() { return /* binding */ useGiveawayTxHash; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst LS_GIVEAWAY_HASH_KEY = \"air_gth\";\nfunction useGiveawayTxHash(id) {\n    const [txHash, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n    const [initialized, setInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const handle = ()=>{\n            if (!initialized) return;\n            setLoading(true);\n            const tx = getAll();\n            setState(tx || \"\");\n            setLoading(false);\n        };\n        addEventListener(\"storage\", handle);\n        return ()=>{\n            removeEventListener(\"storage\", handle);\n        };\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        setInitialized(false);\n        setLoading(true);\n        const tx = getAll();\n        setState(tx || \"\");\n        setInitialized(true);\n        setLoading(false);\n    }, [\n        id\n    ]);\n    const getAll = ()=>{\n        try {\n            const parsedFs = JSON.parse(localStorage.getItem(LS_GIVEAWAY_HASH_KEY) || \"{}\");\n            return parsedFs[id];\n        } catch (err) {}\n    };\n    const update = (txHash)=>{\n        if (!initialized) return;\n        sync(txHash);\n    };\n    const sync = (txHash)=>{\n        try {\n            const encodedFs = JSON.stringify({\n                ...JSON.parse(localStorage.getItem(LS_GIVEAWAY_HASH_KEY) || \"{}\"),\n                [id]: txHash\n            });\n            localStorage.setItem(LS_GIVEAWAY_HASH_KEY, encodedFs);\n            dispatchEvent(new Event(\"storage\"));\n        } catch (err) {}\n    };\n    return {\n        txHash,\n        initialized,\n        loading,\n        update\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./hooks/useGiveawayTxHash.ts\n"));

/***/ }),

/***/ "./node_modules/@polkadot/extension-dapp/bundle.js":
/*!*********************************************************!*\
  !*** ./node_modules/@polkadot/extension-dapp/bundle.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isWeb3Injected: function() { return /* binding */ isWeb3Injected; },\n/* harmony export */   packageInfo: function() { return /* reexport safe */ _packageInfo_js__WEBPACK_IMPORTED_MODULE_0__.packageInfo; },\n/* harmony export */   unwrapBytes: function() { return /* reexport safe */ _wrapBytes_js__WEBPACK_IMPORTED_MODULE_1__.unwrapBytes; },\n/* harmony export */   web3Accounts: function() { return /* binding */ web3Accounts; },\n/* harmony export */   web3AccountsSubscribe: function() { return /* binding */ web3AccountsSubscribe; },\n/* harmony export */   web3Enable: function() { return /* binding */ web3Enable; },\n/* harmony export */   web3EnablePromise: function() { return /* binding */ web3EnablePromise; },\n/* harmony export */   web3FromAddress: function() { return /* binding */ web3FromAddress; },\n/* harmony export */   web3FromSource: function() { return /* binding */ web3FromSource; },\n/* harmony export */   web3ListRpcProviders: function() { return /* binding */ web3ListRpcProviders; },\n/* harmony export */   web3UseRpcProvider: function() { return /* binding */ web3UseRpcProvider; },\n/* harmony export */   wrapBytes: function() { return /* reexport safe */ _wrapBytes_js__WEBPACK_IMPORTED_MODULE_1__.wrapBytes; }\n/* harmony export */ });\n/* harmony import */ var _polkadot_util__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @polkadot/util */ \"./node_modules/@polkadot/util/index.js\");\n/* harmony import */ var _polkadot_util_crypto__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @polkadot/util-crypto */ \"./node_modules/@polkadot/util-crypto/index.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./util.js */ \"./node_modules/@polkadot/extension-dapp/util.js\");\n/* harmony import */ var _packageInfo_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./packageInfo.js */ \"./node_modules/@polkadot/extension-dapp/packageInfo.js\");\n/* harmony import */ var _wrapBytes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./wrapBytes.js */ \"./node_modules/@polkadot/extension-dapp/wrapBytes.js\");\n\n\n\n\n\nconst win = window;\nwin.injectedWeb3 = win.injectedWeb3 || {};\nlet isWeb3Injected = web3IsInjected();\nlet web3EnablePromise = null;\n\n/** @internal true when anything has been injected and is available */\nfunction web3IsInjected() {\n    return Object\n        .values(win.injectedWeb3)\n        .filter(({ connect, enable }) => !!(connect || enable))\n        .length !== 0;\n}\n/** @internal throw a consistent error when not extensions have not been enabled */\nfunction throwError(method) {\n    throw new Error(`${method}: web3Enable(originName) needs to be called before ${method}`);\n}\n/** @internal map from Array<InjectedAccount> to Array<InjectedAccountWithMeta> */\nfunction mapAccounts(source, list, ss58Format) {\n    return list.map(({ address, genesisHash, name, type }) => ({\n        address: address.length === 42\n            ? address\n            : (0,_polkadot_util_crypto__WEBPACK_IMPORTED_MODULE_2__.encodeAddress)((0,_polkadot_util_crypto__WEBPACK_IMPORTED_MODULE_2__.decodeAddress)(address), ss58Format),\n        meta: { genesisHash, name, source },\n        type\n    }));\n}\n/** @internal filter accounts based on genesisHash and type of account */\nfunction filterAccounts(list, genesisHash, type) {\n    return list.filter((a) => (!a.type || !type || type.includes(a.type)) &&\n        (!a.genesisHash || !genesisHash || a.genesisHash === genesisHash));\n}\n/** @internal retrieves all the extensions available on the window */\nfunction getWindowExtensions(originName) {\n    return Promise\n        .all(Object\n        .entries(win.injectedWeb3)\n        .map(([nameOrHash, { connect, enable, version }]) => Promise\n        .resolve()\n        .then(() => connect\n        // new style, returning all info\n        ? connect(originName)\n        : enable\n            // previous interface, leakages on name/version\n            ? enable(originName).then((e) => (0,_polkadot_util__WEBPACK_IMPORTED_MODULE_3__.objectSpread)({ name: nameOrHash, version: version || 'unknown' }, e))\n            : Promise.reject(new Error('No connect(..) or enable(...) hook found')))\n        .catch(({ message }) => {\n        console.error(`Error initializing ${nameOrHash}: ${message}`);\n    })))\n        .then((exts) => exts.filter((e) => !!e));\n}\n/** @internal Ensure the enable promise is resolved and filter by extensions */\nasync function filterEnable(caller, extensions) {\n    if (!web3EnablePromise) {\n        return throwError(caller);\n    }\n    const sources = await web3EnablePromise;\n    return sources.filter(({ name }) => !extensions ||\n        extensions.includes(name));\n}\n/**\n * @summary Enables all the providers found on the injected window interface\n * @description\n * Enables all injected extensions that has been found on the page. This\n * should be called before making use of any other web3* functions.\n */\nfunction web3Enable(originName, compatInits = []) {\n    if (!originName) {\n        throw new Error('You must pass a name for your app to the web3Enable function');\n    }\n    const initCompat = compatInits.length\n        ? Promise.all(compatInits.map((c) => c().catch(() => false)))\n        : Promise.resolve([true]);\n    web3EnablePromise = (0,_util_js__WEBPACK_IMPORTED_MODULE_4__.documentReadyPromise)(() => initCompat.then(() => getWindowExtensions(originName)\n        .then((values) => values.map((e) => {\n        // if we don't have an accounts subscriber, add a single-shot version\n        if (!e.accounts.subscribe) {\n            e.accounts.subscribe = (cb) => {\n                e.accounts\n                    .get()\n                    .then(cb)\n                    .catch(console.error);\n                return () => {\n                    // no ubsubscribe needed, this is a single-shot\n                };\n            };\n        }\n        return e;\n    }))\n        .catch(() => [])\n        .then((values) => {\n        const names = values.map(({ name, version }) => `${name}/${version}`);\n        isWeb3Injected = web3IsInjected();\n        console.info(`web3Enable: Enabled ${values.length} extension${values.length !== 1 ? 's' : ''}: ${names.join(', ')}`);\n        return values;\n    })));\n    return web3EnablePromise;\n}\n/**\n * @summary Retrieves all the accounts across all providers\n * @description\n * This returns the full list of account available (across all extensions) to\n * the page. Filtering options are available of a per-extension, per type and\n * per-genesisHash basis. Optionally the accounts can be encoded with the provided\n * ss58Format\n */\nasync function web3Accounts({ accountType, extensions, genesisHash, ss58Format } = {}) {\n    const accounts = [];\n    const sources = await filterEnable('web3Accounts', extensions);\n    const retrieved = await Promise.all(sources.map(async ({ accounts, name: source }) => {\n        try {\n            const list = await accounts.get();\n            return mapAccounts(source, filterAccounts(list, genesisHash, accountType), ss58Format);\n        }\n        catch {\n            // cannot handle this one\n            return [];\n        }\n    }));\n    retrieved.forEach((result) => {\n        accounts.push(...result);\n    });\n    console.info(`web3Accounts: Found ${accounts.length} address${accounts.length !== 1 ? 'es' : ''}`);\n    return accounts;\n}\n/**\n * @summary Subscribes to all the accounts across all providers\n * @description\n * This is the subscription version of the web3Accounts interface with\n * updates as to when new accounts do become available. The list of filtering\n * options are the same as for the web3Accounts interface.\n */\nasync function web3AccountsSubscribe(cb, { accountType, extensions, genesisHash, ss58Format } = {}) {\n    const sources = await filterEnable('web3AccountsSubscribe', extensions);\n    const accounts = {};\n    const triggerUpdate = () => cb(Object\n        .entries(accounts)\n        .reduce((result, [source, list]) => {\n        result.push(...mapAccounts(source, filterAccounts(list, genesisHash, accountType), ss58Format));\n        return result;\n    }, []));\n    const unsubs = sources.map(({ accounts: { subscribe }, name: source }) => subscribe((result) => {\n        accounts[source] = result;\n        try {\n            const result = triggerUpdate();\n            if (result && (0,_polkadot_util__WEBPACK_IMPORTED_MODULE_3__.isPromise)(result)) {\n                result.catch(console.error);\n            }\n        }\n        catch (error) {\n            console.error(error);\n        }\n    }));\n    return () => {\n        unsubs.forEach((unsub) => {\n            unsub();\n        });\n    };\n}\n/**\n * @summary Finds a specific provider based on the name\n * @description\n * This retrieves a specific source (extension) based on the name. In most\n * cases it should not be needed to call it directly (e.g. it is used internally\n * by calls such as web3FromAddress) but would allow operation on a specific\n * known extension.\n */\nasync function web3FromSource(source) {\n    if (!web3EnablePromise) {\n        return throwError('web3FromSource');\n    }\n    const sources = await web3EnablePromise;\n    const found = source && sources.find(({ name }) => name === source);\n    if (!found) {\n        throw new Error(`web3FromSource: Unable to find an injected ${source}`);\n    }\n    return found;\n}\n/**\n * @summary Find a specific provider that provides a specific address\n * @description\n * Based on an address, return the provider that has makes this address\n * available to the page.\n */\nasync function web3FromAddress(address) {\n    if (!web3EnablePromise) {\n        return throwError('web3FromAddress');\n    }\n    const accounts = await web3Accounts();\n    let found;\n    if (address) {\n        const accountU8a = (0,_polkadot_util_crypto__WEBPACK_IMPORTED_MODULE_2__.decodeAddress)(address);\n        found = accounts.find((account) => (0,_polkadot_util__WEBPACK_IMPORTED_MODULE_3__.u8aEq)((0,_polkadot_util_crypto__WEBPACK_IMPORTED_MODULE_2__.decodeAddress)(account.address), accountU8a));\n    }\n    if (!found) {\n        throw new Error(`web3FromAddress: Unable to find injected ${address}`);\n    }\n    return web3FromSource(found.meta.source);\n}\n/**\n * @summary List all providers exposed by one source\n * @description\n * For extensions that supply RPC providers, this call would return the list\n * of RPC providers that any extension may supply.\n */\nasync function web3ListRpcProviders(source) {\n    const { provider } = await web3FromSource(source);\n    if (!provider) {\n        console.warn(`Extension ${source} does not expose any provider`);\n        return null;\n    }\n    return provider.listProviders();\n}\n/**\n * @summary Start an RPC provider provider by a specific source\n * @description\n * For extensions that supply RPC providers, this call would return an\n * enabled provider (initialized with the specific key) from the\n * specified extension source.\n */\nasync function web3UseRpcProvider(source, key) {\n    const { provider } = await web3FromSource(source);\n    if (!provider) {\n        throw new Error(`Extension ${source} does not expose any provider`);\n    }\n    const meta = await provider.startProvider(key);\n    return { meta, provider };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@polkadot/extension-dapp/bundle.js\n"));

/***/ }),

/***/ "./node_modules/@polkadot/extension-dapp/index.js":
/*!********************************************************!*\
  !*** ./node_modules/@polkadot/extension-dapp/index.js ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _bundle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bundle.js */ \"./node_modules/@polkadot/extension-dapp/bundle.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _bundle_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _bundle_js__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHBvbGthZG90L2V4dGVuc2lvbi1kYXBwL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTRCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AcG9sa2Fkb3QvZXh0ZW5zaW9uLWRhcHAvaW5kZXguanM/MDQxOCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL2J1bmRsZS5qcyc7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/@polkadot/extension-dapp/index.js\n"));

/***/ }),

/***/ "./node_modules/@polkadot/extension-dapp/packageInfo.js":
/*!**************************************************************!*\
  !*** ./node_modules/@polkadot/extension-dapp/packageInfo.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   packageInfo: function() { return /* binding */ packageInfo; }\n/* harmony export */ });\nconst packageInfo = { name: '@polkadot/extension-dapp', path: ( true) ? new URL(\"file:///C:/Users/<USER>/OneDrive/Desktop/web%20development/airlfyt/airlyft-monorepo/packages/public-ui/node_modules/@polkadot/extension-dapp/packageInfo.js\").pathname.substring(0, new URL(\"file:///C:/Users/<USER>/OneDrive/Desktop/web%20development/airlfyt/airlyft-monorepo/packages/public-ui/node_modules/@polkadot/extension-dapp/packageInfo.js\").pathname.lastIndexOf('/') + 1) : 0, type: 'esm', version: '0.47.5' };\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHBvbGthZG90L2V4dGVuc2lvbi1kYXBwL3BhY2thZ2VJbmZvLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxzQkFBc0IseUNBQXlDLEtBQThCLFlBQVksNEpBQWUsZ0NBQWdDLDRKQUFlLG1DQUFtQyxDQUFNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AcG9sa2Fkb3QvZXh0ZW5zaW9uLWRhcHAvcGFja2FnZUluZm8uanM/NTc0MCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgcGFja2FnZUluZm8gPSB7IG5hbWU6ICdAcG9sa2Fkb3QvZXh0ZW5zaW9uLWRhcHAnLCBwYXRoOiAoaW1wb3J0Lm1ldGEgJiYgaW1wb3J0Lm1ldGEudXJsKSA/IG5ldyBVUkwoaW1wb3J0Lm1ldGEudXJsKS5wYXRobmFtZS5zdWJzdHJpbmcoMCwgbmV3IFVSTChpbXBvcnQubWV0YS51cmwpLnBhdGhuYW1lLmxhc3RJbmRleE9mKCcvJykgKyAxKSA6ICdhdXRvJywgdHlwZTogJ2VzbScsIHZlcnNpb246ICcwLjQ3LjUnIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/@polkadot/extension-dapp/packageInfo.js\n"));

/***/ }),

/***/ "./node_modules/@polkadot/extension-dapp/util.js":
/*!*******************************************************!*\
  !*** ./node_modules/@polkadot/extension-dapp/util.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   documentReadyPromise: function() { return /* binding */ documentReadyPromise; }\n/* harmony export */ });\nfunction documentReadyPromise(creator) {\n    return new Promise((resolve) => {\n        if (document.readyState === 'complete') {\n            resolve(creator());\n        }\n        else {\n            window.addEventListener('load', () => resolve(creator()));\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHBvbGthZG90L2V4dGVuc2lvbi1kYXBwL3V0aWwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0Bwb2xrYWRvdC9leHRlbnNpb24tZGFwcC91dGlsLmpzPzdkMWIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGRvY3VtZW50UmVhZHlQcm9taXNlKGNyZWF0b3IpIHtcbiAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHtcbiAgICAgICAgaWYgKGRvY3VtZW50LnJlYWR5U3RhdGUgPT09ICdjb21wbGV0ZScpIHtcbiAgICAgICAgICAgIHJlc29sdmUoY3JlYXRvcigpKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdsb2FkJywgKCkgPT4gcmVzb2x2ZShjcmVhdG9yKCkpKTtcbiAgICAgICAgfVxuICAgIH0pO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/@polkadot/extension-dapp/util.js\n"));

/***/ }),

/***/ "./node_modules/@polkadot/extension-dapp/wrapBytes.js":
/*!************************************************************!*\
  !*** ./node_modules/@polkadot/extension-dapp/wrapBytes.js ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ETHEREUM: function() { return /* binding */ ETHEREUM; },\n/* harmony export */   POSTFIX: function() { return /* binding */ POSTFIX; },\n/* harmony export */   PREFIX: function() { return /* binding */ PREFIX; },\n/* harmony export */   isWrapped: function() { return /* binding */ isWrapped; },\n/* harmony export */   unwrapBytes: function() { return /* binding */ unwrapBytes; },\n/* harmony export */   wrapBytes: function() { return /* binding */ wrapBytes; }\n/* harmony export */ });\n/* harmony import */ var _polkadot_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @polkadot/util */ \"./node_modules/@polkadot/util/index.js\");\n\nconst ETHEREUM = _polkadot_util__WEBPACK_IMPORTED_MODULE_0__.U8A_WRAP_ETHEREUM;\nconst POSTFIX = _polkadot_util__WEBPACK_IMPORTED_MODULE_0__.U8A_WRAP_POSTFIX;\nconst PREFIX = _polkadot_util__WEBPACK_IMPORTED_MODULE_0__.U8A_WRAP_PREFIX;\nconst isWrapped = _polkadot_util__WEBPACK_IMPORTED_MODULE_0__.u8aIsWrapped;\nconst unwrapBytes = _polkadot_util__WEBPACK_IMPORTED_MODULE_0__.u8aUnwrapBytes;\nconst wrapBytes = _polkadot_util__WEBPACK_IMPORTED_MODULE_0__.u8aWrapBytes;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHBvbGthZG90L2V4dGVuc2lvbi1kYXBwL3dyYXBCeXRlcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQWtJO0FBQzNILGlCQUFpQiw2REFBaUI7QUFDbEMsZ0JBQWdCLDREQUFnQjtBQUNoQyxlQUFlLDJEQUFlO0FBQzlCLGtCQUFrQix3REFBWTtBQUM5QixvQkFBb0IsMERBQWM7QUFDbEMsa0JBQWtCLHdEQUFZIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AcG9sa2Fkb3QvZXh0ZW5zaW9uLWRhcHAvd3JhcEJ5dGVzLmpzP2VkMmEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVThBX1dSQVBfRVRIRVJFVU0sIFU4QV9XUkFQX1BPU1RGSVgsIFU4QV9XUkFQX1BSRUZJWCwgdThhSXNXcmFwcGVkLCB1OGFVbndyYXBCeXRlcywgdThhV3JhcEJ5dGVzIH0gZnJvbSAnQHBvbGthZG90L3V0aWwnO1xuZXhwb3J0IGNvbnN0IEVUSEVSRVVNID0gVThBX1dSQVBfRVRIRVJFVU07XG5leHBvcnQgY29uc3QgUE9TVEZJWCA9IFU4QV9XUkFQX1BPU1RGSVg7XG5leHBvcnQgY29uc3QgUFJFRklYID0gVThBX1dSQVBfUFJFRklYO1xuZXhwb3J0IGNvbnN0IGlzV3JhcHBlZCA9IHU4YUlzV3JhcHBlZDtcbmV4cG9ydCBjb25zdCB1bndyYXBCeXRlcyA9IHU4YVVud3JhcEJ5dGVzO1xuZXhwb3J0IGNvbnN0IHdyYXBCeXRlcyA9IHU4YVdyYXBCeXRlcztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/@polkadot/extension-dapp/wrapBytes.js\n"));

/***/ })

}]);