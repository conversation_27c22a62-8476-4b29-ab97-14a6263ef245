"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_Giveaways_MerchandiseGiveaway_MerchandiseShopDetails_tsx";
exports.ids = ["components_Giveaways_MerchandiseGiveaway_MerchandiseShopDetails_tsx"];
exports.modules = {

/***/ "./components/AlertBox.tsx":
/*!*********************************!*\
  !*** ./components/AlertBox.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleWarningAlertBox: () => (/* binding */ SimpleWarningAlertBox),\n/* harmony export */   SuccessAlertBox: () => (/* binding */ SuccessAlertBox),\n/* harmony export */   WarningAlertBox: () => (/* binding */ WarningAlertBox),\n/* harmony export */   \"default\": () => (/* binding */ AlertBox)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nfunction AlertBox({ title, subtitle, icon, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(`flex flex-col p-4 py-8 rounded-2xl relative overflow-hidden text-primary-foreground`, className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -top-[40px] -right-[26px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -bottom-[66px] -right-[60px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"primary-gradient-box-circle-icon\",\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg mb-0\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, this),\n                            subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-opacity-80\",\n                                children: subtitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\nconst SimpleWarningAlertBox = ({ title, description })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-red-100 border border-red-300 text-red-600 px-4 py-3 rounded relative mt-4 space-x-2\",\n        role: \"alert\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                className: \"font-bold\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 51,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"block sm:inline\",\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 52,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined);\nfunction SuccessAlertBox({ title, subtitle }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertBox, {\n        className: \"gradient-primary\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n            className: \"h-10 text-primary-foreground\",\n            size: 32\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n            lineNumber: 66,\n            columnNumber: 13\n        }, void 0),\n        title: title,\n        subtitle: subtitle\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\nfunction WarningAlertBox({ title, subtitle }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertBox, {\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Info, {\n            fontSize: 28\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n            lineNumber: 82,\n            columnNumber: 13\n        }, void 0),\n        title: title,\n        subtitle: subtitle,\n        className: \"gradient-warning text-white\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/AlertBox.tsx\n");

/***/ }),

/***/ "./components/Giveaways/MerchandiseGiveaway/MerchandiseGiveawayClaim.tsx":
/*!*******************************************************************************!*\
  !*** ./components/Giveaways/MerchandiseGiveaway/MerchandiseGiveawayClaim.tsx ***!
  \*******************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/AlertBox */ \"./components/AlertBox.tsx\");\n/* harmony import */ var _Components_Tasks_Form_FormAnswerForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Tasks/Form/FormAnswerForm */ \"./components/Tasks/Form/FormAnswerForm.tsx\");\n/* harmony import */ var _Components_Tasks_components_TaskToaster__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/Tasks/components/TaskToaster */ \"./components/Tasks/components/TaskToaster.tsx\");\n/* harmony import */ var _merchandise_giveaway_gql__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./merchandise-giveaway.gql */ \"./components/Giveaways/MerchandiseGiveaway/merchandise-giveaway.gql.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _Root_services_tracking__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @Root/services/tracking */ \"./services/tracking.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__, _Components_Tasks_Form_FormAnswerForm__WEBPACK_IMPORTED_MODULE_2__, _Components_Tasks_components_TaskToaster__WEBPACK_IMPORTED_MODULE_3__, _merchandise_giveaway_gql__WEBPACK_IMPORTED_MODULE_4__, _Root_services_tracking__WEBPACK_IMPORTED_MODULE_6__]);\n([_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__, _Components_Tasks_Form_FormAnswerForm__WEBPACK_IMPORTED_MODULE_2__, _Components_Tasks_components_TaskToaster__WEBPACK_IMPORTED_MODULE_3__, _merchandise_giveaway_gql__WEBPACK_IMPORTED_MODULE_4__, _Root_services_tracking__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst MerchandiseGiveawayClaim = ({ giveaway, projectEvent, formItems, limitReached })=>{\n    const { claimRewardTrack } = (0,_Root_services_tracking__WEBPACK_IMPORTED_MODULE_6__.useGtmTrack)();\n    const giveawayInfo = giveaway?.info;\n    const shopConfig = giveawayInfo?.shopConfig;\n    const [claim, { loading }] = (0,_merchandise_giveaway_gql__WEBPACK_IMPORTED_MODULE_4__.useMerchandiseClaim)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"translation\");\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    if (limitReached) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__.WarningAlertBox, {\n            title: t(\"giveaway.merchandise.giveawayClaim.title\"),\n            subtitle: t(\"giveaway.merchandise.giveawayClaim.subtitle\")\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\MerchandiseGiveaway\\\\MerchandiseGiveawayClaim.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_Form_FormAnswerForm__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        items: formItems,\n        readonly: false,\n        verifying: loading,\n        onSubmit: (input, { setSubmitting })=>{\n            claim({\n                variables: {\n                    eventId: projectEvent.id,\n                    projectId: projectEvent.project.id,\n                    giveawayId: giveaway.id,\n                    data: {\n                        answers: input\n                    }\n                },\n                context: {\n                    amount: parseInt(giveawayInfo.shopConfig?.amount || \"1\"),\n                    points: giveawayInfo.shopConfig?.points\n                },\n                onCompleted: ()=>{\n                    claimRewardTrack({\n                        projectId: projectEvent.project.id,\n                        eventId: projectEvent.id,\n                        projectTitle: projectEvent.project.name,\n                        eventTitle: projectEvent.title,\n                        giveawayId: giveaway.id,\n                        giveawayTitle: giveaway.title || \"\"\n                    });\n                    setSubmitting(false);\n                },\n                onError: (error)=>{\n                    (0,_Components_Tasks_components_TaskToaster__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n                        title: \"Error\",\n                        defaultText: \"Error verifying task!\",\n                        type: \"error\",\n                        error\n                    });\n                    setSubmitting(false);\n                }\n            });\n        },\n        submitText: `Claim ${shopConfig?.amount} for ${shopConfig?.points} ${globalT(\"projectPoints\")}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\MerchandiseGiveaway\\\\MerchandiseGiveawayClaim.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MerchandiseGiveawayClaim);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/MerchandiseGiveaway/MerchandiseGiveawayClaim.tsx\n");

/***/ }),

/***/ "./components/Giveaways/MerchandiseGiveaway/MerchandiseShopDetails.tsx":
/*!*****************************************************************************!*\
  !*** ./components/Giveaways/MerchandiseGiveaway/MerchandiseShopDetails.tsx ***!
  \*****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MerchandisehopDetails)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_Paragraph__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/Paragraph */ \"./components/Paragraph.tsx\");\n/* harmony import */ var _Components_Project_useProjectUserInfo_gql__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Project/useProjectUserInfo.gql */ \"./components/Project/useProjectUserInfo.gql.ts\");\n/* harmony import */ var _Components_TextEditor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/TextEditor */ \"./components/TextEditor.tsx\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"ethers\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ethers__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_ShopGiveawayDetails__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/ShopGiveawayDetails */ \"./components/Giveaways/components/ShopGiveawayDetails.tsx\");\n/* harmony import */ var _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../hooks/useGetUserEventRewards */ \"./components/Giveaways/hooks/useGetUserEventRewards.ts\");\n/* harmony import */ var _MerchandiseGiveawayClaim__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./MerchandiseGiveawayClaim */ \"./components/Giveaways/MerchandiseGiveaway/MerchandiseGiveawayClaim.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_9__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_Project_useProjectUserInfo_gql__WEBPACK_IMPORTED_MODULE_2__, _Components_TextEditor__WEBPACK_IMPORTED_MODULE_3__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_4__, _components_ShopGiveawayDetails__WEBPACK_IMPORTED_MODULE_6__, _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_7__, _MerchandiseGiveawayClaim__WEBPACK_IMPORTED_MODULE_8__]);\n([_Components_Project_useProjectUserInfo_gql__WEBPACK_IMPORTED_MODULE_2__, _Components_TextEditor__WEBPACK_IMPORTED_MODULE_3__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_4__, _components_ShopGiveawayDetails__WEBPACK_IMPORTED_MODULE_6__, _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_7__, _MerchandiseGiveawayClaim__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nfunction MerchandisehopDetails({ giveaway, projectEvent, expandable, expanded, onClick, eventReward }) {\n    const giveawayInfo = giveaway.info;\n    const shopConfig = giveawayInfo?.shopConfig;\n    const totalAmount = ethers__WEBPACK_IMPORTED_MODULE_5__.BigNumber.from(giveawayInfo?.merchRewardAmount || 1);\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_9__.useTranslation)(\"translation\");\n    const { data, loading: infoLoading } = (0,_Components_Project_useProjectUserInfo_gql__WEBPACK_IMPORTED_MODULE_2__.useProjectUserInfo)(projectEvent?.project?.id);\n    const winnerAmount = ethers__WEBPACK_IMPORTED_MODULE_5__.BigNumber.from(shopConfig?.amount || 0);\n    const { totalClaimable: totalAttemptedClaimable, claimed, totalClaimed: userClaimed, loading: statusLoading } = (0,_hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_7__.useGetUserEventRewardsStats)(projectEvent.id, giveaway.id, winnerAmount);\n    const hasSufficientPoints = data?.projectUserInfo?.fuel && shopConfig.points && data?.projectUserInfo?.fuel >= shopConfig.points;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ShopGiveawayDetails__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        loading: infoLoading || statusLoading,\n        icon: giveaway.icon || \"\",\n        totalAmount: totalAmount,\n        totalAttemptedClaimable: totalAttemptedClaimable,\n        userClaimedAmount: userClaimed,\n        expandable: expandable,\n        expanded: expanded,\n        onClick: onClick,\n        reward: giveawayInfo?.reward,\n        shopConfig: shopConfig,\n        userProjectPoints: data?.projectUserInfo?.fuel || undefined,\n        summary: {\n            giveawayType: giveaway.giveawayType\n        },\n        claimed: claimed,\n        giveawayClaimed: ethers__WEBPACK_IMPORTED_MODULE_5__.BigNumber.from(giveawayInfo?.claimed || 0),\n        remainingLiquidity: totalAmount.sub(ethers__WEBPACK_IMPORTED_MODULE_5__.BigNumber.from(giveawayInfo?.claimed || 0)),\n        children: [\n            giveawayInfo.description ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Paragraph__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_4__.Flask, {\n                        weight: \"fill\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\MerchandiseGiveaway\\\\MerchandiseShopDetails.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 19\n                    }, void 0),\n                    title: t(\"giveaway.merchandise.shopDetails\"),\n                    description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TextEditor__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        defaultValue: giveawayInfo.description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\MerchandiseGiveaway\\\\MerchandiseShopDetails.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 26\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\MerchandiseGiveaway\\\\MerchandiseShopDetails.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n            !claimed && hasSufficientPoints ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MerchandiseGiveawayClaim__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                giveaway: giveaway,\n                projectEvent: projectEvent,\n                formItems: giveawayInfo?.formItems,\n                limitReached: ethers__WEBPACK_IMPORTED_MODULE_5__.BigNumber.from(giveawayInfo?.claimed || 0).eq(totalAmount)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\MerchandiseGiveaway\\\\MerchandiseShopDetails.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\MerchandiseGiveaway\\\\MerchandiseShopDetails.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/MerchandiseGiveaway/MerchandiseShopDetails.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/GiveawaySummaryItem.tsx":
/*!*****************************************************************!*\
  !*** ./components/Giveaways/components/GiveawaySummaryItem.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GiveawaySummaryItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_ui_button__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n([_Components_ui_button__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nfunction GiveawaySummaryItem({ banner, children, bannerTag, onClick, size = \"default\", actionText, className, actionSuccess }) {\n    const isVideo = banner?.endsWith(\".mp4\") || banner?.endsWith(\".webm\") || banner?.endsWith(\".mov\");\n    const isGif = banner?.endsWith(\".gif\");\n    if (size === \"small\" || size == \"default\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `relative gap-4 grid grid-cols-[120px_1fr] items-center ${size === \"default\" ? \"sm:grid-cols-[170px_1fr]\" : \"\"} ${className || \"\"}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        isVideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                            autoPlay: true,\n                            playsInline: true,\n                            loop: true,\n                            muted: true,\n                            className: \"object-cover aspect-square rounded-xl absolute top-0 left-0 w-full h-full blur-[100px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: banner,\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            height: 200,\n                            width: 200,\n                            src: banner,\n                            className: `object-cover aspect-square rounded-xl absolute top-0 left-0 w-full h-full blur-[100px]`,\n                            alt: \"giveaway-image\",\n                            unoptimized: isGif\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10 max-w-sm m-auto\",\n                            children: [\n                                isVideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                    autoPlay: true,\n                                    playsInline: true,\n                                    loop: true,\n                                    muted: true,\n                                    className: \"rounded-xl w-full object-cover\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                        src: banner,\n                                        type: \"video/mp4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    height: 200,\n                                    width: 200,\n                                    src: banner,\n                                    className: \"object-cover w-full aspect-square rounded-xl\",\n                                    alt: \"giveaway-image\",\n                                    loading: \"lazy\",\n                                    unoptimized: isGif\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-1 left-0 w-full flex justify-center\",\n                                    children: bannerTag\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-20 gap-2 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: children\n                        }, void 0, false),\n                        actionText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm flex items-center gap-1 text-blue-500 font-medium cursor-pointer\",\n                            onClick: onClick,\n                            children: [\n                                actionSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n                                    weight: \"bold\",\n                                    size: 15\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"line-clamp-1 break-all\",\n                                    children: actionText\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    \"aria-hidden\": \"true\",\n                                    children: \"→\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative gap-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `absolute transform-gpu -z-10 rounded-3xl w-full blur-3xl`,\n                        children: isVideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                            autoPlay: true,\n                            playsInline: true,\n                            loop: true,\n                            muted: true,\n                            className: \"rounded-xl w-full object-cover\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: banner,\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            height: 400,\n                            width: 400,\n                            src: banner || \"https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80\",\n                            className: `object-cover w-full aspect-square rounded-xl`,\n                            alt: \"giveaway-image\",\n                            loading: \"lazy\",\n                            unoptimized: isGif\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pb-2 relative z-10\",\n                        children: [\n                            isVideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                autoPlay: true,\n                                playsInline: true,\n                                loop: true,\n                                muted: true,\n                                className: \"rounded-xl w-full object-cover\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                    src: banner,\n                                    type: \"video/mp4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                height: 400,\n                                width: 400,\n                                src: banner || \"https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80\",\n                                className: `object-cover aspect-square w-full rounded-xl`,\n                                alt: \"giveaway-image\",\n                                loading: \"lazy\",\n                                unoptimized: isGif\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 right-2\",\n                                children: bannerTag\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-20 space-y-2\",\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"sm\",\n                            rounded: \"full\",\n                            className: \"!font-semibold mt-2\",\n                            block: true,\n                            onClick: onClick,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-1 items-center\",\n                                children: [\n                                    actionSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n                                        weight: \"bold\",\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"line-clamp-1 break-all\",\n                                        children: actionText\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/GiveawaySummaryItem.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/ShopGiveawayDetails.tsx":
/*!*****************************************************************!*\
  !*** ./components/Giveaways/components/ShopGiveawayDetails.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ShopGiveawayDetails)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/AlertBox */ \"./components/AlertBox.tsx\");\n/* harmony import */ var _Components_Panel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Panel */ \"./components/Panel.tsx\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Components_Tasks_components_TaskTileLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Tasks/components/TaskTileLoader */ \"./components/Tasks/components/TaskTileLoader.tsx\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var _Root_helpers_giveaway__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @Root/helpers/giveaway */ \"./helpers/giveaway.ts\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"ethers\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(ethers__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./GiveawaySummaryItem */ \"./components/Giveaways/components/GiveawaySummaryItem.tsx\");\n/* harmony import */ var _GiveawaySummaryTags__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./GiveawaySummaryTags */ \"./components/Giveaways/components/GiveawaySummaryTags.tsx\");\n/* harmony import */ var _TokenAddress__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./TokenAddress */ \"./components/Giveaways/components/TokenAddress.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_11__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__, _Components_Panel__WEBPACK_IMPORTED_MODULE_2__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__, _GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_8__, _GiveawaySummaryTags__WEBPACK_IMPORTED_MODULE_9__, _TokenAddress__WEBPACK_IMPORTED_MODULE_10__]);\n([_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__, _Components_Panel__WEBPACK_IMPORTED_MODULE_2__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__, _GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_8__, _GiveawaySummaryTags__WEBPACK_IMPORTED_MODULE_9__, _TokenAddress__WEBPACK_IMPORTED_MODULE_10__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ShopGiveawayDetails({ icon, token, blockchain, totalAmount, userClaimedAmount, totalAttemptedClaimable, expandable, expanded, children, onClick, summary, shopConfig, reward, userProjectPoints, claimed, giveawayClaimed, remainingLiquidity, loading = false }) {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_11__.useTranslation)(\"translation\");\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_11__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    const hasSufficientPoints = userProjectPoints && shopConfig.points && userProjectPoints >= shopConfig.points;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_components_TaskTileLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                style: {\n                    background: \"transparent\",\n                    border: 0,\n                    padding: 0\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Panel__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        expandable: expandable,\n        expanded: expanded,\n        onClick: onClick,\n        header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            banner: icon || \"https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80\",\n            className: expanded ? \"!grid-cols-1 sm:!grid-cols-[170px_1fr]\" : \"\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawaySummaryTags__WEBPACK_IMPORTED_MODULE_9__.GiveawaySummaryTags, {\n                        totalPoints: 0,\n                        totalXP: 0,\n                        summaries: [\n                            summary\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 11\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: `text-lg text-ch font-semibold break-all`,\n                    children: reward\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 11\n                }, void 0),\n                token && token.address && token.address != ethers__WEBPACK_IMPORTED_MODULE_7__.ethers.constants.AddressZero ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenAddress__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    token: token,\n                    blockchain: blockchain,\n                    showBlockchain: true,\n                    className: \"mb-1\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 13\n                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2 text-sm text-ch font-medium \",\n                    children: [\n                        shopConfig?.points && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__.Fire, {\n                                    size: 20,\n                                    className: \"flex-shrink-0\",\n                                    weight: \"duotone\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 17\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"break-all\",\n                                    children: `${shopConfig.points} ${globalT(\"projectPoints\")} for ${(0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3__.formatAmount)(shopConfig.amount, token?.decimals || 0)} ${(0,_Root_helpers_giveaway__WEBPACK_IMPORTED_MODULE_6__.getAssetName)(token, Number.parseFloat((0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3__.formatAmount)(shopConfig.amount, token?.decimals || 0)))}`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 15\n                        }, void 0),\n                        remainingLiquidity && totalAmount?.gt(0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__.Basket, {\n                                    size: 20,\n                                    className: \"flex-shrink-0\",\n                                    weight: \"duotone\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 17\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"break-all\",\n                                    children: `${(0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3__.formatAmount)(remainingLiquidity?.toString(), token?.decimals || 0)}/${(0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3__.formatAmount)(totalAmount?.toString(), token?.decimals || 0)} ${(0,_Root_helpers_giveaway__WEBPACK_IMPORTED_MODULE_6__.getAssetName)(token, Number.parseFloat((0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3__.formatAmount)(remainingLiquidity?.toString(), token?.decimals || 0)))} remaining`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 17\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 15\n                        }, void 0),\n                        totalAmount?.eq(-1) && giveawayClaimed?.gt(0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__.Basket, {\n                                    size: 20,\n                                    className: \"flex-shrink-0\",\n                                    weight: \"duotone\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 17\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"break-all\",\n                                    children: `${(0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3__.formatAmount)(giveawayClaimed?.toString(), token?.decimals || 0)} ${(0,_Root_helpers_giveaway__WEBPACK_IMPORTED_MODULE_6__.getAssetName)(token, Number.parseFloat((0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3__.formatAmount)(giveawayClaimed?.toString(), token?.decimals || 0)))} claimed`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 17\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 15\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 11\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n            lineNumber: 80,\n            columnNumber: 9\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 relative z-50\",\n            children: [\n                children,\n                claimed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__.SuccessAlertBox, {\n                    title: \"Congrats!\",\n                    subtitle: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"You have claimed\",\n                            \" \",\n                            (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3__.formatAmount)(userClaimedAmount?.toString(), token?.decimals || 0),\n                            \" \",\n                            \"item(s)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 11\n                }, this),\n                !claimed && !hasSufficientPoints && totalAttemptedClaimable.lte(0) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm break-words text-cs\",\n                    children: [\n                        t(\"giveaway.shopDetails.pre\"),\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-bold\",\n                            children: userProjectPoints || 0\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, this),\n                        t(\"giveaway.shopDetails.mid\", {\n                            projectPoints: globalT(\"projectPoints\")\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-bold\",\n                            children: shopConfig.points\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, this),\n                        t(\"giveaway.shopDetails.post\", {\n                            projectPoints: globalT(\"projectPoints\")\n                        })\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/ShopGiveawayDetails.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/TokenAddress.tsx":
/*!**********************************************************!*\
  !*** ./components/Giveaways/components/TokenAddress.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenAddress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction TokenAddress({ token, blockchain, className, showBlockchain = false, addressChars = 3 }) {\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    if (token.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.AssetType.NATIVE) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex gap-1.5 text-sm text-cl items-center ${className || \"\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-medium\",\n                children: (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.shortenAddress)(token.address, addressChars)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n                className: \"text-primary-foreground bg-primary rounded-full p-1\",\n                weight: \"bold\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Copy, {\n                className: \"cursor-pointer text-ch\",\n                size: 16,\n                onClick: (event)=>{\n                    event.stopPropagation();\n                    navigator.clipboard.writeText(token.address);\n                    setCopied(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this),\n            blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                className: \"inline-flex text-ch\",\n                target: \"_blank\",\n                href: (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.getExplorerLink)(blockchain.blockExplorerUrls, token.address, _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ExplorerDataType.TOKEN),\n                rel: \"noreferrer\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.ArrowSquareOut, {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, this),\n            showBlockchain && blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_5___default()), {\n                src: blockchain?.icon || \"\",\n                height: 20,\n                width: 20,\n                className: \"h-5 w-5\",\n                alt: \"blockchain-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenAddress.tsx\n");

/***/ }),

/***/ "./components/Panel.tsx":
/*!******************************!*\
  !*** ./components/Panel.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Panel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__]);\n_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nfunction Panel({ expandable, expanded, header, children, onClick }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(`rounded-xl p-4 transition`, expandable ? expanded ? \"cursor-pointer\" : \"hover:bg-foreground/10 cursor-pointer\" : \"\"),\n                onClick: ()=>{\n                    expandable && onClick?.();\n                },\n                children: header\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Panel.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(`p-4`, expandable && !expanded ? \"hidden\" : \"\"),\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Panel.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1BhbmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUF1QztBQUV4QixTQUFTQyxNQUFNLEVBQzVCQyxVQUFVLEVBQ1ZDLFFBQVEsRUFDUkMsTUFBTSxFQUNOQyxRQUFRLEVBQ1JDLE9BQU8sRUFPUjtJQUNDLHFCQUNFOzswQkFDRSw4REFBQ0M7Z0JBQ0NDLFdBQVdSLHFEQUFFQSxDQUNYLENBQUMseUJBQXlCLENBQUMsRUFDM0JFLGFBQ0lDLFdBQ0UsbUJBQ0EsMENBQ0Y7Z0JBRU5HLFNBQVM7b0JBQ1BKLGNBQWNJO2dCQUNoQjswQkFFQ0Y7Ozs7OzswQkFFSCw4REFBQ0c7Z0JBQUlDLFdBQVdSLHFEQUFFQSxDQUFDLENBQUMsR0FBRyxDQUFDLEVBQUVFLGNBQWMsQ0FBQ0MsV0FBVyxXQUFXOzBCQUM1REU7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYWlybHlmdC9wdWJsaWMtdWkvLi9jb21wb25lbnRzL1BhbmVsLnRzeD9lMDM1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNuIH0gZnJvbSAnQFJvb3QvdXRpbHMvdXRpbHMnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGFuZWwoe1xyXG4gIGV4cGFuZGFibGUsXHJcbiAgZXhwYW5kZWQsXHJcbiAgaGVhZGVyLFxyXG4gIGNoaWxkcmVuLFxyXG4gIG9uQ2xpY2ssXHJcbn06IHtcclxuICBleHBhbmRhYmxlPzogYm9vbGVhbjtcclxuICBleHBhbmRlZD86IGJvb2xlYW47XHJcbiAgaGVhZGVyOiBSZWFjdC5SZWFjdE5vZGU7XHJcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcclxuICBvbkNsaWNrPzogKCkgPT4gdm9pZDtcclxufSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICA8ZGl2XHJcbiAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgIGByb3VuZGVkLXhsIHAtNCB0cmFuc2l0aW9uYCxcclxuICAgICAgICAgIGV4cGFuZGFibGVcclxuICAgICAgICAgICAgPyBleHBhbmRlZFxyXG4gICAgICAgICAgICAgID8gJ2N1cnNvci1wb2ludGVyJ1xyXG4gICAgICAgICAgICAgIDogJ2hvdmVyOmJnLWZvcmVncm91bmQvMTAgY3Vyc29yLXBvaW50ZXInXHJcbiAgICAgICAgICAgIDogJycsXHJcbiAgICAgICAgKX1cclxuICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICBleHBhbmRhYmxlICYmIG9uQ2xpY2s/LigpO1xyXG4gICAgICAgIH19XHJcbiAgICAgID5cclxuICAgICAgICB7aGVhZGVyfVxyXG4gICAgICA8L2Rpdj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9e2NuKGBwLTRgLCBleHBhbmRhYmxlICYmICFleHBhbmRlZCA/ICdoaWRkZW4nIDogJycpfT5cclxuICAgICAgICB7Y2hpbGRyZW59XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC8+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiY24iLCJQYW5lbCIsImV4cGFuZGFibGUiLCJleHBhbmRlZCIsImhlYWRlciIsImNoaWxkcmVuIiwib25DbGljayIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/Panel.tsx\n");

/***/ }),

/***/ "./components/Paragraph.tsx":
/*!**********************************!*\
  !*** ./components/Paragraph.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Paragraph = ({ icon, title, description })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-1 mb-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-2 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 w-4 text-ch\",\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-bold text-ch\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm break-words text-cs\",\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Paragraph);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1BhcmFncmFwaC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLE1BQU1BLFlBQVksQ0FBQyxFQUNqQkMsSUFBSSxFQUNKQyxLQUFLLEVBQ0xDLFdBQVcsRUFLWjtJQUNDLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FBbUJKOzs7Ozs7a0NBQ2xDLDhEQUFDSzt3QkFBR0QsV0FBVTtrQ0FBNkJIOzs7Ozs7Ozs7Ozs7MEJBRTdDLDhEQUFDSztnQkFBRUYsV0FBVTswQkFBK0JGOzs7Ozs7Ozs7Ozs7QUFHbEQ7QUFFQSxpRUFBZUgsU0FBU0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2NvbXBvbmVudHMvUGFyYWdyYXBoLnRzeD80YjNkIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IFBhcmFncmFwaCA9ICh7XHJcbiAgaWNvbixcclxuICB0aXRsZSxcclxuICBkZXNjcmlwdGlvbixcclxufToge1xyXG4gIGljb246IFJlYWN0LlJlYWN0Tm9kZTtcclxuICB0aXRsZTogc3RyaW5nIHwgUmVhY3QuUmVhY3ROb2RlO1xyXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmcgfCBSZWFjdC5SZWFjdE5vZGU7XHJcbn0pID0+IHtcclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTEgbWItNFwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yIGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWNoXCI+e2ljb259PC9kaXY+XHJcbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1ib2xkIHRleHQtY2hcIj57dGl0bGV9PC9oMz5cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gYnJlYWstd29yZHMgdGV4dC1jc1wiPntkZXNjcmlwdGlvbn08L3A+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgUGFyYWdyYXBoO1xyXG4iXSwibmFtZXMiOlsiUGFyYWdyYXBoIiwiaWNvbiIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJkaXYiLCJjbGFzc05hbWUiLCJoMyIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Paragraph.tsx\n");

/***/ }),

/***/ "./components/SelectField.tsx":
/*!************************************!*\
  !*** ./components/SelectField.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SelectField)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ui/label */ \"./components/ui/label.tsx\");\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_ui_label__WEBPACK_IMPORTED_MODULE_1__, _Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__]);\n([_ui_label__WEBPACK_IMPORTED_MODULE_1__, _Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nfunction SelectField({ label, id, className, options, requiredMark, errorMsg, ...rest }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid gap-1.5\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_1__.Label, {\n                htmlFor: id,\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"leading-normal\", requiredMark ? \"required-mark\" : \"\"),\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\SelectField.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative border pr-2 rounded-lg component-bg focus:component-bg  hover:border-gray-400 hover:shadow-sm w-full\", errorMsg ? \"border-red-500\" : \"\", className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                    ...rest,\n                    id: id,\n                    name: id,\n                    className: `peer bg-transparent w-full p-4 px-2 disabled:cursor-not-allowed focus:outline-none rounded-lg`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                            value: \"\",\n                            className: \"text-gray-500\",\n                            disabled: true,\n                            selected: true,\n                            children: \"Select your option...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\SelectField.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        (options || []).map((item, key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: item.value,\n                                className: \"text-gray-900\",\n                                children: item.value\n                            }, key, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\SelectField.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\SelectField.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\SelectField.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            errorMsg && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-red-500\",\n                children: errorMsg\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\SelectField.tsx\",\n                lineNumber: 53,\n                columnNumber: 20\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\SelectField.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SelectField.tsx\n");

/***/ }),

/***/ "./components/Tasks/Form/FormAnswerForm.tsx":
/*!**************************************************!*\
  !*** ./components/Tasks/Form/FormAnswerForm.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/utils/string-utils */ \"./utils/string-utils.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! formik */ \"formik\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(formik__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _FormItemRenderer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FormItemRenderer */ \"./components/Tasks/Form/FormItemRenderer.tsx\");\n/* harmony import */ var _Components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @Components/ui/button */ \"./components/ui/button.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_FormItemRenderer__WEBPACK_IMPORTED_MODULE_4__, _Components_ui_button__WEBPACK_IMPORTED_MODULE_5__]);\n([_FormItemRenderer__WEBPACK_IMPORTED_MODULE_4__, _Components_ui_button__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst FormAnswerForm = ({ items, verifying, onSubmit, formParticipationData, readonly, submitText = \"Submit\" })=>{\n    const filteredItems = items.filter((i)=>!i.hidden);\n    const participationMapper = (values)=>{\n        return Object.keys(values).map((item)=>{\n            return {\n                id: item,\n                value: Array.isArray(values[item]) ? values[item] : [\n                    values[item]\n                ]\n            };\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_3__.Formik, {\n        initialValues: (filteredItems || []).reduce((acc, cur)=>{\n            return {\n                ...acc,\n                [cur.id]: formParticipationData?.formAnswers?.filter((i)=>i.id == cur.id)?.[0]?.value\n            };\n        }, {}),\n        validate: (values)=>{\n            return (filteredItems || []).reduce((acc, cur)=>{\n                if (cur.required && !values[cur.id]) {\n                    return {\n                        ...acc,\n                        [cur.id]: `Can't be blank`\n                    };\n                }\n                if (cur.widget === _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.FormWidgetType.WEBSITE && values[cur.id] && !_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_1__.URL_REGEX.test(values[cur.id])) {\n                    return {\n                        ...acc,\n                        [cur.id]: \"Please enter correct url\"\n                    };\n                }\n                if (cur.widget === _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.FormWidgetType.EMAIL && values[cur.id] && !_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_1__.EMAIL_REGEX.test(values[cur.id])) {\n                    return {\n                        ...acc,\n                        [cur.id]: \"Please enter correct email address\"\n                    };\n                }\n                return acc;\n            }, {});\n        },\n        onSubmit: (values, helpers)=>{\n            const input = participationMapper(values);\n            onSubmit(input, helpers);\n        },\n        children: ({ values, errors, touched, handleChange, handleBlur, handleSubmit, isSubmitting, isValid, dirty })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        (filteredItems || []).map((item, key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FormItemRenderer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                data: item,\n                                onBlur: handleBlur,\n                                onChange: handleChange,\n                                initialValue: values[item.id],\n                                disabled: readonly,\n                                tabIndex: key,\n                                error: errors[item.id] && touched[item.id] ? errors[item.id] : undefined\n                            }, key, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Form\\\\FormAnswerForm.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 15\n                            }, undefined)),\n                        !readonly && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            disabled: filteredItems.length > 0 ? !(isValid && dirty) || isSubmitting || verifying : false,\n                            loading: isSubmitting || verifying,\n                            block: true,\n                            rounded: \"full\",\n                            type: \"submit\",\n                            children: submitText\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Form\\\\FormAnswerForm.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Form\\\\FormAnswerForm.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Form\\\\FormAnswerForm.tsx\",\n                lineNumber: 93,\n                columnNumber: 9\n            }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Form\\\\FormAnswerForm.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FormAnswerForm);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Form/FormAnswerForm.tsx\n");

/***/ }),

/***/ "./components/Tasks/Form/FormItemRenderer.tsx":
/*!****************************************************!*\
  !*** ./components/Tasks/Form/FormItemRenderer.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_InputField__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/InputField */ \"./components/InputField.tsx\");\n/* harmony import */ var _Components_RadioGroup__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/RadioGroup */ \"./components/RadioGroup.tsx\");\n/* harmony import */ var _Components_SelectField__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/SelectField */ \"./components/SelectField.tsx\");\n/* harmony import */ var _Components_TextField__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/TextField */ \"./components/TextField.tsx\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_InputField__WEBPACK_IMPORTED_MODULE_1__, _Components_RadioGroup__WEBPACK_IMPORTED_MODULE_2__, _Components_SelectField__WEBPACK_IMPORTED_MODULE_3__, _Components_TextField__WEBPACK_IMPORTED_MODULE_4__]);\n([_Components_InputField__WEBPACK_IMPORTED_MODULE_1__, _Components_RadioGroup__WEBPACK_IMPORTED_MODULE_2__, _Components_SelectField__WEBPACK_IMPORTED_MODULE_3__, _Components_TextField__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst FormItemRenderer = (props)=>{\n    const { initialValue, onChange, onBlur, error, disabled, tabIndex, data } = props;\n    const { widget, title, id, required, values } = data;\n    switch(widget){\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.FormWidgetType.EMAIL:\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.FormWidgetType.NAME:\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.FormWidgetType.INPUT:\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.FormWidgetType.WEBSITE:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_InputField__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                label: title,\n                id: id,\n                name: id,\n                required: required,\n                requiredMark: required,\n                placeholder: widget === _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.FormWidgetType.WEBSITE ? \"https://...\" : \"Type ...\",\n                value: initialValue,\n                errorMsg: error,\n                onChange: onChange,\n                onBlur: onBlur,\n                disabled: disabled,\n                tabIndex: tabIndex,\n                labelPlacement: \"outside\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Form\\\\FormItemRenderer.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, undefined);\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.FormWidgetType.RADIO:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_RadioGroup__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                label: title,\n                id: id,\n                name: id,\n                required: required,\n                requiredMark: required,\n                errorMsg: error,\n                value: initialValue,\n                values: values,\n                onChange: onChange,\n                onBlur: onBlur,\n                disabled: disabled,\n                tabIndex: tabIndex\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Form\\\\FormItemRenderer.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined);\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.FormWidgetType.SELECT:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_SelectField__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                label: title,\n                id: id,\n                name: id,\n                required: required,\n                requiredMark: required,\n                value: initialValue,\n                options: values,\n                errorMsg: error,\n                onChange: onChange,\n                onBlur: onBlur,\n                disabled: disabled,\n                tabIndex: tabIndex\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Form\\\\FormItemRenderer.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, undefined);\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.FormWidgetType.CHECKBOX:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_RadioGroup__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                label: title,\n                inputType: _Components_RadioGroup__WEBPACK_IMPORTED_MODULE_2__.RadioInputType.Multi,\n                id: id,\n                name: id,\n                required: false,\n                errorMsg: error,\n                value: initialValue,\n                values: values,\n                onChange: onChange,\n                onBlur: onBlur,\n                disabled: disabled,\n                tabIndex: tabIndex\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Form\\\\FormItemRenderer.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, undefined);\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.FormWidgetType.TEXT:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TextField__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                label: title,\n                id: id,\n                name: id,\n                required: required,\n                requiredMark: required,\n                placeholder: \"Type ...\",\n                value: initialValue,\n                rows: 3,\n                errorMsg: error,\n                onChange: onChange,\n                onBlur: onBlur,\n                disabled: disabled,\n                tabIndex: tabIndex\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Tasks\\\\Form\\\\FormItemRenderer.tsx\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, undefined);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FormItemRenderer);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Tasks/Form/FormItemRenderer.tsx\n");

/***/ }),

/***/ "./components/TextField.tsx":
/*!**********************************!*\
  !*** ./components/TextField.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TextField)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/label */ \"./components/ui/label.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _ui_label__WEBPACK_IMPORTED_MODULE_2__]);\n([_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__, _ui_label__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nfunction TextField({ label, id, className, requiredMark, errorMsg, ...rest }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-sm grid gap-1.5\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_2__.Label, {\n                htmlFor: id,\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"leading-normal\", requiredMark ? \"required-mark\" : \"\"),\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextField.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                ...rest,\n                id: id,\n                name: id,\n                autoComplete: \"off\",\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(`border flex flex-col items-start relative rounded-lg component-bg p-3 focus-within:border-primary focus-within:shadow-sm w-full disabled:text-gray-500 disabled:cursor-not-allowed`, errorMsg ? \"border-red-500\" : \"\", className)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextField.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            errorMsg && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-red-500\",\n                children: errorMsg\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextField.tsx\",\n                lineNumber: 40,\n                columnNumber: 20\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TextField.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/TextField.tsx\n");

/***/ })

};
;