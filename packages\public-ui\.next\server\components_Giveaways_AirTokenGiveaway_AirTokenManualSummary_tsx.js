"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_Giveaways_AirTokenGiveaway_AirTokenManualSummary_tsx";
exports.ids = ["components_Giveaways_AirTokenGiveaway_AirTokenManualSummary_tsx"];
exports.modules = {

/***/ "./components/Giveaways/AirTokenGiveaway/AirTokenManualSummary.tsx":
/*!*************************************************************************!*\
  !*** ./components/Giveaways/AirTokenGiveaway/AirTokenManualSummary.tsx ***!
  \*************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AirTokenManualSummary)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_Tag__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/Tag */ \"./components/Tag.tsx\");\n/* harmony import */ var _heroicons_react_solid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @heroicons/react/solid */ \"@heroicons/react/solid\");\n/* harmony import */ var _heroicons_react_solid__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_heroicons_react_solid__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Hooks_useGetBlockchain__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Hooks/useGetBlockchain */ \"./hooks/useGetBlockchain.ts\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"ethers\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(ethers__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! moment */ \"moment\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_TokenGiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/TokenGiveawaySummaryItem */ \"./components/Giveaways/components/TokenGiveawaySummaryItem.tsx\");\n/* harmony import */ var _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../hooks/useGetUserEventRewards */ \"./components/Giveaways/hooks/useGetUserEventRewards.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_8__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_Tag__WEBPACK_IMPORTED_MODULE_1__, _components_TokenGiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_6__, _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_7__]);\n([_Components_Tag__WEBPACK_IMPORTED_MODULE_1__, _components_TokenGiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_6__, _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nfunction AirTokenManualSummary({ giveaway, projectEvent, onClick, size }) {\n    const giveawayInfo = giveaway.info;\n    const airToken = giveawayInfo?.airToken;\n    const totalAmount = ethers__WEBPACK_IMPORTED_MODULE_4__.BigNumber.from(giveawayInfo.winnerAmount || 0);\n    const { totalClaimable, loading, totalClaimed } = (0,_hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_7__.useGetUserEventRewardsStats)(projectEvent.id, giveaway.id, totalAmount);\n    const { data: blockchainData, loading: blockchainLoading } = (0,_Hooks_useGetBlockchain__WEBPACK_IMPORTED_MODULE_3__.useGetBlockchain)(airToken.blockchainId);\n    const blockchain = blockchainData?.blockchain;\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation)(\"translation\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TokenGiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                onClick: onClick,\n                size: size,\n                token: airToken,\n                blockchain: blockchain,\n                amount: totalAmount,\n                claimableAmount: totalClaimable,\n                claimedAmount: totalClaimed,\n                title: t(\"giveaway.selectionTypes.manual\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenManualSummary.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 flex gap-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_1__.Tag, {\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_solid__WEBPACK_IMPORTED_MODULE_2__.CalendarIcon, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenManualSummary.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 17\n                        }, void 0),\n                        title: moment__WEBPACK_IMPORTED_MODULE_5___default()(projectEvent.endTime).format(\"MMM Do YY, h:mm a\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenManualSummary.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_1__.Tag, {\n                        title: t(\"giveaway.airTokenPool.manualSummaryTitle\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenManualSummary.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenManualSummary.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenManualSummary.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/AirTokenGiveaway/AirTokenManualSummary.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/GiveawaySummaryItem.tsx":
/*!*****************************************************************!*\
  !*** ./components/Giveaways/components/GiveawaySummaryItem.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GiveawaySummaryItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_ui_button__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n([_Components_ui_button__WEBPACK_IMPORTED_MODULE_1__, _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nfunction GiveawaySummaryItem({ banner, children, bannerTag, onClick, size = \"default\", actionText, className, actionSuccess }) {\n    const isVideo = banner?.endsWith(\".mp4\") || banner?.endsWith(\".webm\") || banner?.endsWith(\".mov\");\n    const isGif = banner?.endsWith(\".gif\");\n    if (size === \"small\" || size == \"default\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `relative gap-4 grid grid-cols-[120px_1fr] items-center ${size === \"default\" ? \"sm:grid-cols-[170px_1fr]\" : \"\"} ${className || \"\"}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        isVideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                            autoPlay: true,\n                            playsInline: true,\n                            loop: true,\n                            muted: true,\n                            className: \"object-cover aspect-square rounded-xl absolute top-0 left-0 w-full h-full blur-[100px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: banner,\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            height: 200,\n                            width: 200,\n                            src: banner,\n                            className: `object-cover aspect-square rounded-xl absolute top-0 left-0 w-full h-full blur-[100px]`,\n                            alt: \"giveaway-image\",\n                            unoptimized: isGif\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10 max-w-sm m-auto\",\n                            children: [\n                                isVideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                    autoPlay: true,\n                                    playsInline: true,\n                                    loop: true,\n                                    muted: true,\n                                    className: \"rounded-xl w-full object-cover\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                        src: banner,\n                                        type: \"video/mp4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    height: 200,\n                                    width: 200,\n                                    src: banner,\n                                    className: \"object-cover w-full aspect-square rounded-xl\",\n                                    alt: \"giveaway-image\",\n                                    loading: \"lazy\",\n                                    unoptimized: isGif\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-1 left-0 w-full flex justify-center\",\n                                    children: bannerTag\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-20 gap-2 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: children\n                        }, void 0, false),\n                        actionText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm flex items-center gap-1 text-blue-500 font-medium cursor-pointer\",\n                            onClick: onClick,\n                            children: [\n                                actionSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n                                    weight: \"bold\",\n                                    size: 15\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"line-clamp-1 break-all\",\n                                    children: actionText\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    \"aria-hidden\": \"true\",\n                                    children: \"→\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative gap-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `absolute transform-gpu -z-10 rounded-3xl w-full blur-3xl`,\n                        children: isVideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                            autoPlay: true,\n                            playsInline: true,\n                            loop: true,\n                            muted: true,\n                            className: \"rounded-xl w-full object-cover\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: banner,\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            height: 400,\n                            width: 400,\n                            src: banner || \"https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80\",\n                            className: `object-cover w-full aspect-square rounded-xl`,\n                            alt: \"giveaway-image\",\n                            loading: \"lazy\",\n                            unoptimized: isGif\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pb-2 relative z-10\",\n                        children: [\n                            isVideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                autoPlay: true,\n                                playsInline: true,\n                                loop: true,\n                                muted: true,\n                                className: \"rounded-xl w-full object-cover\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                    src: banner,\n                                    type: \"video/mp4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                height: 400,\n                                width: 400,\n                                src: banner || \"https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80\",\n                                className: `object-cover aspect-square w-full rounded-xl`,\n                                alt: \"giveaway-image\",\n                                loading: \"lazy\",\n                                unoptimized: isGif\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 right-2\",\n                                children: bannerTag\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-20 space-y-2\",\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"sm\",\n                            rounded: \"full\",\n                            className: \"!font-semibold mt-2\",\n                            block: true,\n                            onClick: onClick,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-1 items-center\",\n                                children: [\n                                    actionSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n                                        weight: \"bold\",\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"line-clamp-1 break-all\",\n                                        children: actionText\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/GiveawaySummaryItem.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/TokenAddress.tsx":
/*!**********************************************************!*\
  !*** ./components/Giveaways/components/TokenAddress.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenAddress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"@phosphor-icons/react\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__]);\n_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction TokenAddress({ token, blockchain, className, showBlockchain = false, addressChars = 3 }) {\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    if (token.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_3__.AssetType.NATIVE) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex gap-1.5 text-sm text-cl items-center ${className || \"\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-medium\",\n                children: (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.shortenAddress)(token.address, addressChars)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n                className: \"text-primary-foreground bg-primary rounded-full p-1\",\n                weight: \"bold\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Copy, {\n                className: \"cursor-pointer text-ch\",\n                size: 16,\n                onClick: (event)=>{\n                    event.stopPropagation();\n                    navigator.clipboard.writeText(token.address);\n                    setCopied(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this),\n            blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                className: \"inline-flex text-ch\",\n                target: \"_blank\",\n                href: (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.getExplorerLink)(blockchain.blockExplorerUrls, token.address, _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ExplorerDataType.TOKEN),\n                rel: \"noreferrer\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.ArrowSquareOut, {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, this),\n            showBlockchain && blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_5___default()), {\n                src: blockchain?.icon || \"\",\n                height: 20,\n                width: 20,\n                className: \"h-5 w-5\",\n                alt: \"blockchain-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0dpdmVhd2F5cy9jb21wb25lbnRzL1Rva2VuQWRkcmVzcy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSTJCO0FBQ3lDO0FBQ0k7QUFDdkM7QUFDRjtBQUVoQixTQUFTUyxhQUFhLEVBQ25DQyxLQUFLLEVBQ0xDLFVBQVUsRUFDVkMsU0FBUyxFQUNUQyxpQkFBaUIsS0FBSyxFQUN0QkMsZUFBZSxDQUFDLEVBT2pCO0lBQ0MsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUdULCtDQUFRQSxDQUFDO0lBRXJDLElBQUlHLE1BQU1PLFNBQVMsS0FBS1gscURBQVNBLENBQUNZLE1BQU0sRUFBRTtRQUN4QyxxQkFBTztJQUNUO0lBRUEscUJBQ0UsOERBQUNDO1FBQ0NQLFdBQVcsQ0FBQywwQ0FBMEMsRUFBRUEsYUFBYSxHQUFHLENBQUM7OzBCQUV6RSw4REFBQ087Z0JBQUlQLFdBQVU7MEJBQ1pWLGlFQUFjQSxDQUFDUSxNQUFNVSxPQUFPLEVBQUVOOzs7Ozs7WUFFaENDLHVCQUNDLDhEQUFDWCx3REFBS0E7Z0JBQ0pRLFdBQVU7Z0JBQ1ZTLFFBQU87Ozs7O3FDQUdULDhEQUFDaEIsdURBQUlBO2dCQUNITyxXQUFVO2dCQUNWVSxNQUFNO2dCQUNOQyxTQUFTLENBQUNDO29CQUNSQSxNQUFNQyxlQUFlO29CQUNyQkMsVUFBVUMsU0FBUyxDQUFDQyxTQUFTLENBQUNsQixNQUFNVSxPQUFPO29CQUMzQ0osVUFBVTtnQkFDWjs7Ozs7O1lBR0hMLDRCQUNDLDhEQUFDa0I7Z0JBQ0NqQixXQUFVO2dCQUNWa0IsUUFBTztnQkFDUEMsTUFBTTlCLGtFQUFlQSxDQUNuQlUsV0FBV3FCLGlCQUFpQixFQUM1QnRCLE1BQU1VLE9BQU8sRUFDYnBCLCtEQUFnQkEsQ0FBQ2lDLEtBQUs7Z0JBRXhCQyxLQUFJOzBCQUVKLDRFQUFDL0IsaUVBQWNBO29CQUFDbUIsTUFBTTs7Ozs7Ozs7Ozs7WUFHekJULGtCQUFrQkYsNEJBQ2pCLDhEQUFDSCxtREFBS0E7Z0JBQ0oyQixLQUFLeEIsWUFBWXlCLFFBQVE7Z0JBQ3pCQyxRQUFRO2dCQUNSQyxPQUFPO2dCQUNQMUIsV0FBVTtnQkFDVjJCLEtBQUk7Ozs7Ozs7Ozs7OztBQUtkIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGFpcmx5ZnQvcHVibGljLXVpLy4vY29tcG9uZW50cy9HaXZlYXdheXMvY29tcG9uZW50cy9Ub2tlbkFkZHJlc3MudHN4PzU3Y2MiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcclxuICBFeHBsb3JlckRhdGFUeXBlLFxyXG4gIGdldEV4cGxvcmVyTGluayxcclxuICBzaG9ydGVuQWRkcmVzcyxcclxufSBmcm9tICdAYWlybHlmdC93ZWIzLWV2bSc7XHJcbmltcG9ydCB7IEFycm93U3F1YXJlT3V0LCBDaGVjaywgQ29weSB9IGZyb20gJ0BwaG9zcGhvci1pY29ucy9yZWFjdCc7XHJcbmltcG9ydCB7IEFzc2V0VHlwZSwgQmxvY2tjaGFpbiwgQmxvY2tjaGFpbkFzc2V0IH0gZnJvbSAnQGFpcmx5ZnQvdHlwZXMnO1xyXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IEltYWdlIGZyb20gJ25leHQvaW1hZ2UnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVG9rZW5BZGRyZXNzKHtcclxuICB0b2tlbixcclxuICBibG9ja2NoYWluLFxyXG4gIGNsYXNzTmFtZSxcclxuICBzaG93QmxvY2tjaGFpbiA9IGZhbHNlLFxyXG4gIGFkZHJlc3NDaGFycyA9IDMsXHJcbn06IHtcclxuICB0b2tlbjogQmxvY2tjaGFpbkFzc2V0O1xyXG4gIGJsb2NrY2hhaW46IEJsb2NrY2hhaW4gfCB1bmRlZmluZWQ7XHJcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xyXG4gIHNob3dCbG9ja2NoYWluPzogYm9vbGVhbjtcclxuICBhZGRyZXNzQ2hhcnM/OiBudW1iZXI7XHJcbn0pIHtcclxuICBjb25zdCBbY29waWVkLCBzZXRDb3BpZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICBpZiAodG9rZW4uYXNzZXRUeXBlID09PSBBc3NldFR5cGUuTkFUSVZFKSB7XHJcbiAgICByZXR1cm4gPD48Lz47XHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdlxyXG4gICAgICBjbGFzc05hbWU9e2BmbGV4IGdhcC0xLjUgdGV4dC1zbSB0ZXh0LWNsIGl0ZW1zLWNlbnRlciAke2NsYXNzTmFtZSB8fCAnJ31gfVxyXG4gICAgPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+XHJcbiAgICAgICAge3Nob3J0ZW5BZGRyZXNzKHRva2VuLmFkZHJlc3MsIGFkZHJlc3NDaGFycyl9XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgICB7Y29waWVkID8gKFxyXG4gICAgICAgIDxDaGVja1xyXG4gICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1wcmltYXJ5LWZvcmVncm91bmQgYmctcHJpbWFyeSByb3VuZGVkLWZ1bGwgcC0xXCJcclxuICAgICAgICAgIHdlaWdodD1cImJvbGRcIlxyXG4gICAgICAgIC8+XHJcbiAgICAgICkgOiAoXHJcbiAgICAgICAgPENvcHlcclxuICAgICAgICAgIGNsYXNzTmFtZT1cImN1cnNvci1wb2ludGVyIHRleHQtY2hcIlxyXG4gICAgICAgICAgc2l6ZT17MTZ9XHJcbiAgICAgICAgICBvbkNsaWNrPXsoZXZlbnQpID0+IHtcclxuICAgICAgICAgICAgZXZlbnQuc3RvcFByb3BhZ2F0aW9uKCk7XHJcbiAgICAgICAgICAgIG5hdmlnYXRvci5jbGlwYm9hcmQud3JpdGVUZXh0KHRva2VuLmFkZHJlc3MpO1xyXG4gICAgICAgICAgICBzZXRDb3BpZWQodHJ1ZSk7XHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgIC8+XHJcbiAgICAgICl9XHJcbiAgICAgIHtibG9ja2NoYWluICYmIChcclxuICAgICAgICA8YVxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggdGV4dC1jaFwiXHJcbiAgICAgICAgICB0YXJnZXQ9XCJfYmxhbmtcIlxyXG4gICAgICAgICAgaHJlZj17Z2V0RXhwbG9yZXJMaW5rKFxyXG4gICAgICAgICAgICBibG9ja2NoYWluLmJsb2NrRXhwbG9yZXJVcmxzLFxyXG4gICAgICAgICAgICB0b2tlbi5hZGRyZXNzLFxyXG4gICAgICAgICAgICBFeHBsb3JlckRhdGFUeXBlLlRPS0VOLFxyXG4gICAgICAgICAgKX1cclxuICAgICAgICAgIHJlbD1cIm5vcmVmZXJyZXJcIlxyXG4gICAgICAgID5cclxuICAgICAgICAgIDxBcnJvd1NxdWFyZU91dCBzaXplPXsxNn0gLz5cclxuICAgICAgICA8L2E+XHJcbiAgICAgICl9XHJcbiAgICAgIHtzaG93QmxvY2tjaGFpbiAmJiBibG9ja2NoYWluICYmIChcclxuICAgICAgICA8SW1hZ2VcclxuICAgICAgICAgIHNyYz17YmxvY2tjaGFpbj8uaWNvbiB8fCAnJ31cclxuICAgICAgICAgIGhlaWdodD17MjB9XHJcbiAgICAgICAgICB3aWR0aD17MjB9XHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJoLTUgdy01XCJcclxuICAgICAgICAgIGFsdD1cImJsb2NrY2hhaW4taWNvblwiXHJcbiAgICAgICAgLz5cclxuICAgICAgKX1cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIkV4cGxvcmVyRGF0YVR5cGUiLCJnZXRFeHBsb3JlckxpbmsiLCJzaG9ydGVuQWRkcmVzcyIsIkFycm93U3F1YXJlT3V0IiwiQ2hlY2siLCJDb3B5IiwiQXNzZXRUeXBlIiwidXNlU3RhdGUiLCJJbWFnZSIsIlRva2VuQWRkcmVzcyIsInRva2VuIiwiYmxvY2tjaGFpbiIsImNsYXNzTmFtZSIsInNob3dCbG9ja2NoYWluIiwiYWRkcmVzc0NoYXJzIiwiY29waWVkIiwic2V0Q29waWVkIiwiYXNzZXRUeXBlIiwiTkFUSVZFIiwiZGl2IiwiYWRkcmVzcyIsIndlaWdodCIsInNpemUiLCJvbkNsaWNrIiwiZXZlbnQiLCJzdG9wUHJvcGFnYXRpb24iLCJuYXZpZ2F0b3IiLCJjbGlwYm9hcmQiLCJ3cml0ZVRleHQiLCJhIiwidGFyZ2V0IiwiaHJlZiIsImJsb2NrRXhwbG9yZXJVcmxzIiwiVE9LRU4iLCJyZWwiLCJzcmMiLCJpY29uIiwiaGVpZ2h0Iiwid2lkdGgiLCJhbHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenAddress.tsx\n");

/***/ }),

/***/ "./components/Giveaways/components/TokenGiveawaySummaryItem.tsx":
/*!**********************************************************************!*\
  !*** ./components/Giveaways/components/TokenGiveawaySummaryItem.tsx ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenGiveawaySummaryItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Components_Tag__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Tag */ \"./components/Tag.tsx\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"ethers\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(ethers__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./GiveawaySummaryItem */ \"./components/Giveaways/components/GiveawaySummaryItem.tsx\");\n/* harmony import */ var _TokenAddress__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TokenAddress */ \"./components/Giveaways/components/TokenAddress.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Components_Tag__WEBPACK_IMPORTED_MODULE_2__, _GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_4__, _TokenAddress__WEBPACK_IMPORTED_MODULE_5__]);\n([_Components_Tag__WEBPACK_IMPORTED_MODULE_2__, _GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_4__, _TokenAddress__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nfunction TokenGiveawaySummaryItem({ token, blockchain, amount, claimableAmount, claimedAmount, onClick, size, title }) {\n    const isClaimed = amount && claimedAmount.eq(amount);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        size: size,\n        onClick: onClick,\n        banner: token.icon || \"\",\n        actionText: isClaimed ? `Claimed ${(0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatAmount)(claimedAmount?.toString() || \"\", token.decimals)} ${token.ticker} ` : claimableAmount.gt(0) ? `Claim ${(0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatAmount)(claimableAmount?.toString() || \"\", token.decimals)} ${token.ticker}` : \"Learn more\",\n        actionSuccess: isClaimed,\n        bannerTag: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_2__.Tag, {\n            title: blockchain?.name,\n            size: \"small\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                src: blockchain?.icon || \"\",\n                className: \"h-3 w-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawaySummaryItem.tsx\",\n                lineNumber: 51,\n                columnNumber: 17\n            }, void 0),\n            className: \"!bg-slate-900/50  backdrop-blur !font-semibold inline-flex !text-white\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawaySummaryItem.tsx\",\n            lineNumber: 48,\n            columnNumber: 9\n        }, void 0),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tag__WEBPACK_IMPORTED_MODULE_2__.Tag, {\n                    title: title,\n                    className: \"!inline-flex !text-xs !font-semibold\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawaySummaryItem.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawaySummaryItem.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-base text-cs font-semibold line-clamp-1 break-all\",\n                children: [\n                    \"Win up to \",\n                    (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatAmount)(amount?.toString() || \"\", token.decimals),\n                    \" \",\n                    token.ticker,\n                    \" \"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawaySummaryItem.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            token.address && token.address != ethers__WEBPACK_IMPORTED_MODULE_3__.ethers.constants.AddressZero ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenAddress__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                token: token,\n                blockchain: blockchain\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawaySummaryItem.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenGiveawaySummaryItem.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenGiveawaySummaryItem.tsx\n");

/***/ }),

/***/ "./hooks/useGetBlockchain.ts":
/*!***********************************!*\
  !*** ./hooks/useGetBlockchain.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGetBlockchain: () => (/* binding */ useGetBlockchain)\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client */ \"@apollo/client\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_apollo_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst GET_BLOCKCHAIN = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql)`\r\n  query blockchain($id: ID!) {\r\n    blockchain(id: $id) {\r\n      id\r\n      name\r\n      chainId\r\n      icon\r\n      blockExplorerUrls\r\n      rpcUrls\r\n      nativeCurrency\r\n      decimals\r\n      type\r\n    }\r\n  }\r\n`;\nfunction useGetBlockchain(id) {\n    return (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.useQuery)(GET_BLOCKCHAIN, {\n        variables: {\n            id\n        },\n        skip: !id\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ob29rcy91c2VHZXRCbG9ja2NoYWluLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUErQztBQUcvQyxNQUFNRSxpQkFBaUJGLG1EQUFHLENBQUM7Ozs7Ozs7Ozs7Ozs7O0FBYzNCLENBQUM7QUFFTSxTQUFTRyxpQkFBaUJDLEVBQVU7SUFDekMsT0FBT0gsd0RBQVFBLENBQ2JDLGdCQUNBO1FBQ0VHLFdBQVc7WUFDVEQ7UUFDRjtRQUNBRSxNQUFNLENBQUNGO0lBQ1Q7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL0BhaXJseWZ0L3B1YmxpYy11aS8uL2hvb2tzL3VzZUdldEJsb2NrY2hhaW4udHM/ZGMxYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBncWwsIHVzZVF1ZXJ5IH0gZnJvbSAnQGFwb2xsby9jbGllbnQnO1xyXG5pbXBvcnQgeyBCbG9ja2NoYWluLCBRdWVyeV9ibG9ja2NoYWluQXJncyB9IGZyb20gJ0BhaXJseWZ0L3R5cGVzJztcclxuXHJcbmNvbnN0IEdFVF9CTE9DS0NIQUlOID0gZ3FsYFxyXG4gIHF1ZXJ5IGJsb2NrY2hhaW4oJGlkOiBJRCEpIHtcclxuICAgIGJsb2NrY2hhaW4oaWQ6ICRpZCkge1xyXG4gICAgICBpZFxyXG4gICAgICBuYW1lXHJcbiAgICAgIGNoYWluSWRcclxuICAgICAgaWNvblxyXG4gICAgICBibG9ja0V4cGxvcmVyVXJsc1xyXG4gICAgICBycGNVcmxzXHJcbiAgICAgIG5hdGl2ZUN1cnJlbmN5XHJcbiAgICAgIGRlY2ltYWxzXHJcbiAgICAgIHR5cGVcclxuICAgIH1cclxuICB9XHJcbmA7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gdXNlR2V0QmxvY2tjaGFpbihpZDogc3RyaW5nKSB7XHJcbiAgcmV0dXJuIHVzZVF1ZXJ5PHsgYmxvY2tjaGFpbjogQmxvY2tjaGFpbiB9LCBRdWVyeV9ibG9ja2NoYWluQXJncz4oXHJcbiAgICBHRVRfQkxPQ0tDSEFJTixcclxuICAgIHtcclxuICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgaWQsXHJcbiAgICAgIH0sXHJcbiAgICAgIHNraXA6ICFpZCxcclxuICAgIH0sXHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiZ3FsIiwidXNlUXVlcnkiLCJHRVRfQkxPQ0tDSEFJTiIsInVzZUdldEJsb2NrY2hhaW4iLCJpZCIsInZhcmlhYmxlcyIsInNraXAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./hooks/useGetBlockchain.ts\n");

/***/ })

};
;