"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("components_Giveaways_AirTokenGiveaway_AirTokenFcfsTaskIdDetails_tsx",{

/***/ "./components/Giveaways/AirTokenGiveaway/AirTokenDotsamaGiveawayClaim.tsx":
/*!********************************************************************************!*\
  !*** ./components/Giveaways/AirTokenGiveaway/AirTokenDotsamaGiveawayClaim.tsx ***!
  \********************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/AlertBox */ \"./components/AlertBox.tsx\");\n/* harmony import */ var _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Toaster/Toaster */ \"./components/Toaster/Toaster.tsx\");\n/* harmony import */ var _Components_Web3Wallet_Dotsama_DotsamaWallet__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/Web3Wallet/Dotsama/DotsamaWallet */ \"./components/Web3Wallet/Dotsama/DotsamaWallet.tsx\");\n/* harmony import */ var _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Root/helpers/dotsama */ \"./helpers/dotsama.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _GiveawayTransactionHash__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../GiveawayTransactionHash */ \"./components/Giveaways/GiveawayTransactionHash.tsx\");\n/* harmony import */ var _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../hooks/useGetUserEventRewards */ \"./components/Giveaways/hooks/useGetUserEventRewards.ts\");\n/* harmony import */ var _airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./airtoken-giveaway.gql */ \"./components/Giveaways/AirTokenGiveaway/airtoken-giveaway.gql.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-google-recaptcha-v3 */ \"./node_modules/react-google-recaptcha-v3/dist/react-google-recaptcha-v3.esm.js\");\n/* harmony import */ var _Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @Components/RecaptchaDeclaration */ \"./components/RecaptchaDeclaration.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AirTokenDotsamaGiveawayClaim = (param)=>{\n    let { giveaway, projectEvent, blockchain, airToken, amount } = param;\n    var _sort_find;\n    _s();\n    const { executeRecaptcha } = (0,react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_12__.useGoogleReCaptcha)();\n    const [claimDotsama] = (0,_airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_10__.useClaimDotsamaAirTokenGiveaway)();\n    const [isClaiming, setIsClaiming] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_11__.useTranslation)(\"translation\");\n    const { data: userEventRewardsData, loading: isUserEventRewardsLoading } = (0,_hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_9__.useGetUserEventRewards)(projectEvent.id);\n    const processing = userEventRewardsData === null || userEventRewardsData === void 0 ? void 0 : userEventRewardsData.userEventRewards.find((item)=>item.status === _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.RewardStatus.PROCESSING);\n    const txHash = (_sort_find = [\n        ...(userEventRewardsData === null || userEventRewardsData === void 0 ? void 0 : userEventRewardsData.userEventRewards) || []\n    ].sort((a, b)=>{\n        const dateA = +new Date(a.updatedAt);\n        const dateB = +new Date(b.updatedAt);\n        return dateB - dateA;\n    }).find((reward)=>reward.txHash && giveaway.id === reward.giveawayId)) === null || _sort_find === void 0 ? void 0 : _sort_find.txHash;\n    const handleSubmit = async (connectorData)=>{\n        const { account } = connectorData;\n        if (!account || !airToken) return;\n        setIsClaiming(true);\n        let captcha;\n        if (projectEvent.ipProtect) {\n            if (!executeRecaptcha) {\n                (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                    title: \"Failed\",\n                    text: \"Recaptcha not initialized\",\n                    type: \"error\"\n                });\n                setIsClaiming(false);\n                return;\n            }\n            captcha = await executeRecaptcha(\"airtoken_dotsama_giveaway_claim\");\n        }\n        try {\n            const formattedAddress = (0,_Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__.convertToSs58Address)(account, blockchain.chainId);\n            await claimDotsama({\n                variables: {\n                    projectId: projectEvent.project.id,\n                    eventId: projectEvent.id,\n                    giveawayId: giveaway.id,\n                    userAddress: formattedAddress,\n                    captcha\n                }\n            });\n            (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                title: \"Submitted\",\n                text: \"Your claim request has been submitted, check your notifications for an update.\",\n                type: \"success\"\n            });\n        } catch (err) {\n            (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                title: \"Failed\",\n                text: err.message,\n                type: \"error\"\n            });\n        } finally{\n            setIsClaiming(false);\n        }\n    };\n    if (processing) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__.SuccessAlertBox, {\n            title: t(\"giveaway.airTokenPool.successTitle\"),\n            subtitle: t(\"giveaway.airTokenPool.successSubtitle\")\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n            lineNumber: 114,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!amount.isZero()) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Web3Wallet_Dotsama_DotsamaWallet__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    blockchain: blockchain,\n                    button: {\n                        confirm: {\n                            enable: true,\n                            loading: isClaiming || isUserEventRewardsLoading,\n                            text: \"Claim \".concat((0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__.formatAmount)(amount === null || amount === void 0 ? void 0 : amount.toString(), airToken.decimals), \" \").concat(airToken.ticker, \" using \")\n                        }\n                    },\n                    onSuccess: handleSubmit,\n                    excludedWallets: [\n                        _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.Web3WalletType.DOTSAMA_MANUAL\n                    ]\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, undefined),\n                projectEvent.ipProtect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_13__.RecaptchaDeclaration, {\n                    className: \"text-xs text-cs text-center\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n            lineNumber: 123,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawayTransactionHash__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        txHash: txHash,\n        blockchain: blockchain\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n        lineNumber: 146,\n        columnNumber: 10\n    }, undefined);\n};\n_s(AirTokenDotsamaGiveawayClaim, \"mdMu2YwNR2p0kya76ZQkFiX65Uc=\", false, function() {\n    return [\n        react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_12__.useGoogleReCaptcha,\n        _airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_10__.useClaimDotsamaAirTokenGiveaway,\n        next_i18next__WEBPACK_IMPORTED_MODULE_11__.useTranslation,\n        _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_9__.useGetUserEventRewards\n    ];\n});\n_c = AirTokenDotsamaGiveawayClaim;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AirTokenDotsamaGiveawayClaim);\nvar _c;\n$RefreshReg$(_c, \"AirTokenDotsamaGiveawayClaim\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0dpdmVhd2F5cy9BaXJUb2tlbkdpdmVhd2F5L0FpclRva2VuRG90c2FtYUdpdmVhd2F5Q2xhaW0udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXVEO0FBQ0w7QUFDdUI7QUFDWjtBQVFyQztBQUN5QjtBQUdoQjtBQUNnQztBQUNRO0FBQ0M7QUFDNUI7QUFDaUI7QUFDUztBQUV4RSxNQUFNYywrQkFBK0I7UUFBQyxFQUNwQ0MsUUFBUSxFQUNSQyxZQUFZLEVBQ1pDLFVBQVUsRUFDVkMsUUFBUSxFQUNSQyxNQUFNLEVBT1A7UUFZZ0I7O0lBWGYsTUFBTSxFQUFFQyxnQkFBZ0IsRUFBRSxHQUFHUiw4RUFBa0JBO0lBQy9DLE1BQU0sQ0FBQ1MsYUFBYSxHQUFHWCx3RkFBK0JBO0lBQ3RELE1BQU0sQ0FBQ1ksWUFBWUMsY0FBYyxHQUFHaEIsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxFQUFFaUIsQ0FBQyxFQUFFLEdBQUdiLDZEQUFjQSxDQUFDO0lBQzdCLE1BQU0sRUFBRWMsTUFBTUMsb0JBQW9CLEVBQUVDLFNBQVNDLHlCQUF5QixFQUFFLEdBQ3RFbkIscUZBQXNCQSxDQUFDTyxhQUFhYSxFQUFFO0lBRXhDLE1BQU1DLGFBQWFKLGlDQUFBQSwyQ0FBQUEscUJBQXNCSyxnQkFBZ0IsQ0FBQ0MsSUFBSSxDQUM1RCxDQUFDQyxPQUFTQSxLQUFLQyxNQUFNLEtBQUs5Qix3REFBWUEsQ0FBQytCLFVBQVU7SUFHbkQsTUFBTUMsVUFBUztXQUFLVixDQUFBQSxpQ0FBQUEsMkNBQUFBLHFCQUFzQkssZ0JBQWdCLEtBQUksRUFBRTtLQUFFLENBQy9ETSxJQUFJLENBQUMsQ0FBQ0MsR0FBR0M7UUFDUixNQUFNQyxRQUFRLENBQUMsSUFBSUMsS0FBS0gsRUFBRUksU0FBUztRQUNuQyxNQUFNQyxRQUFRLENBQUMsSUFBSUYsS0FBS0YsRUFBRUcsU0FBUztRQUNuQyxPQUFPQyxRQUFRSDtJQUNqQixHQUNDUixJQUFJLENBQ0gsQ0FBQ1ksU0FBV0EsT0FBT1IsTUFBTSxJQUFJckIsU0FBU2MsRUFBRSxLQUFLZSxPQUFPQyxVQUFVLGVBUG5ELDRDQVFWVCxNQUFNO0lBRVgsTUFBTVUsZUFBZSxPQUFPQztRQUMxQixNQUFNLEVBQUVDLE9BQU8sRUFBRSxHQUFHRDtRQUVwQixJQUFJLENBQUNDLFdBQVcsQ0FBQzlCLFVBQVU7UUFDM0JLLGNBQWM7UUFFZCxJQUFJMEI7UUFFSixJQUFJakMsYUFBYWtDLFNBQVMsRUFBRTtZQUMxQixJQUFJLENBQUM5QixrQkFBa0I7Z0JBQ3JCbkIsdUVBQU9BLENBQUM7b0JBQ05rRCxPQUFPO29CQUNQQyxNQUFNO29CQUNOQyxNQUFNO2dCQUNSO2dCQUNBOUIsY0FBYztnQkFDZDtZQUNGO1lBRUEwQixVQUFVLE1BQU03QixpQkFBaUI7UUFDbkM7UUFFQSxJQUFJO1lBQ0YsTUFBTWtDLG1CQUFtQm5ELDJFQUFvQkEsQ0FDM0M2QyxTQUNBL0IsV0FBV3NDLE9BQU87WUFHcEIsTUFBTWxDLGFBQWE7Z0JBQ2pCbUMsV0FBVztvQkFDVEMsV0FBV3pDLGFBQWEwQyxPQUFPLENBQUM3QixFQUFFO29CQUNsQzhCLFNBQVMzQyxhQUFhYSxFQUFFO29CQUN4QmdCLFlBQVk5QixTQUFTYyxFQUFFO29CQUN2QitCLGFBQWFOO29CQUNiTDtnQkFDRjtZQUNGO1lBRUFoRCx1RUFBT0EsQ0FBQztnQkFDTmtELE9BQU87Z0JBQ1BDLE1BQU07Z0JBQ05DLE1BQU07WUFDUjtRQUNGLEVBQUUsT0FBT1EsS0FBVTtZQUNqQjVELHVFQUFPQSxDQUFDO2dCQUNOa0QsT0FBTztnQkFDUEMsTUFBTVMsSUFBSUMsT0FBTztnQkFDakJULE1BQU07WUFDUjtRQUNGLFNBQVU7WUFDUjlCLGNBQWM7UUFDaEI7SUFDRjtJQUVBLElBQUlPLFlBQVk7UUFDZCxxQkFDRSw4REFBQzlCLGlFQUFlQTtZQUNkbUQsT0FBTzNCLEVBQUU7WUFDVHVDLFVBQVV2QyxFQUFFOzs7Ozs7SUFHbEI7SUFFQSxJQUFJLENBQUNMLE9BQU82QyxNQUFNLElBQUk7UUFDcEIscUJBQ0UsOERBQUNDO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDaEUsb0ZBQWFBO29CQUNaZSxZQUFZQTtvQkFDWmtELFFBQVE7d0JBQ05DLFNBQVM7NEJBQ1BDLFFBQVE7NEJBQ1IxQyxTQUFTTCxjQUFjTTs0QkFDdkJ3QixNQUFNLFNBR0RsQyxPQUhVWiwrREFBWUEsQ0FDekJhLG1CQUFBQSw2QkFBQUEsT0FBUW1ELFFBQVEsSUFDaEJwRCxTQUFTcUQsUUFBUSxHQUNqQixLQUFtQixPQUFoQnJELFNBQVNzRCxNQUFNLEVBQUM7d0JBQ3ZCO29CQUNGO29CQUNBQyxXQUFXM0I7b0JBQ1g0QixpQkFBaUI7d0JBQUNyRSwwREFBY0EsQ0FBQ3NFLGNBQWM7cUJBQUM7Ozs7OztnQkFFakQzRCxhQUFha0MsU0FBUyxrQkFDckIsOERBQUNyQyxtRkFBb0JBO29CQUFDcUQsV0FBVTs7Ozs7Ozs7Ozs7O0lBSXhDO0lBRUEscUJBQU8sOERBQUMxRCxnRUFBdUJBO1FBQUM0QixRQUFRQTtRQUFRbkIsWUFBWUE7Ozs7OztBQUM5RDtHQTNITUg7O1FBYXlCRiwwRUFBa0JBO1FBQ3hCRixvRkFBK0JBO1FBRXhDQyx5REFBY0E7UUFFMUJGLGlGQUFzQkE7OztLQWxCcEJLO0FBNkhOLCtEQUFlQSw0QkFBNEJBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9HaXZlYXdheXMvQWlyVG9rZW5HaXZlYXdheS9BaXJUb2tlbkRvdHNhbWFHaXZlYXdheUNsYWltLnRzeD9kYmJiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFN1Y2Nlc3NBbGVydEJveCB9IGZyb20gJ0BDb21wb25lbnRzL0FsZXJ0Qm94JztcclxuaW1wb3J0IHRvYXN0ZXIgZnJvbSAnQENvbXBvbmVudHMvVG9hc3Rlci9Ub2FzdGVyJztcclxuaW1wb3J0IERvdHNhbWFXYWxsZXQgZnJvbSAnQENvbXBvbmVudHMvV2ViM1dhbGxldC9Eb3RzYW1hL0RvdHNhbWFXYWxsZXQnO1xyXG5pbXBvcnQgeyBjb252ZXJ0VG9TczU4QWRkcmVzcyB9IGZyb20gJ0BSb290L2hlbHBlcnMvZG90c2FtYSc7XHJcbmltcG9ydCB7XHJcbiAgQmxvY2tjaGFpbixcclxuICBCbG9ja2NoYWluQXNzZXQsXHJcbiAgR2l2ZWF3YXksXHJcbiAgUHJvamVjdEV2ZW50LFxyXG4gIFJld2FyZFN0YXR1cyxcclxuICBXZWIzV2FsbGV0VHlwZSxcclxufSBmcm9tICdAYWlybHlmdC90eXBlcyc7XHJcbmltcG9ydCB7IGZvcm1hdEFtb3VudCB9IGZyb20gJ0BhaXJseWZ0L3dlYjMtZXZtJztcclxuaW1wb3J0IHsgRG90c2FtYUNvbm5lY3RvckRhdGEgfSBmcm9tICdAYWlybHlmdC93ZWIzLWV2bS1ob29rcyc7XHJcbmltcG9ydCB7IEJpZ051bWJlciB9IGZyb20gJ2V0aGVycyc7XHJcbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgR2l2ZWF3YXlUcmFuc2FjdGlvbkhhc2ggZnJvbSAnLi4vR2l2ZWF3YXlUcmFuc2FjdGlvbkhhc2gnO1xyXG5pbXBvcnQgeyB1c2VHZXRVc2VyRXZlbnRSZXdhcmRzIH0gZnJvbSAnLi4vaG9va3MvdXNlR2V0VXNlckV2ZW50UmV3YXJkcyc7XHJcbmltcG9ydCB7IHVzZUNsYWltRG90c2FtYUFpclRva2VuR2l2ZWF3YXkgfSBmcm9tICcuL2FpcnRva2VuLWdpdmVhd2F5LmdxbCc7XHJcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSAnbmV4dC1pMThuZXh0JztcclxuaW1wb3J0IHsgdXNlR29vZ2xlUmVDYXB0Y2hhIH0gZnJvbSAncmVhY3QtZ29vZ2xlLXJlY2FwdGNoYS12Myc7XHJcbmltcG9ydCB7IFJlY2FwdGNoYURlY2xhcmF0aW9uIH0gZnJvbSAnQENvbXBvbmVudHMvUmVjYXB0Y2hhRGVjbGFyYXRpb24nO1xyXG5cclxuY29uc3QgQWlyVG9rZW5Eb3RzYW1hR2l2ZWF3YXlDbGFpbSA9ICh7XHJcbiAgZ2l2ZWF3YXksXHJcbiAgcHJvamVjdEV2ZW50LFxyXG4gIGJsb2NrY2hhaW4sXHJcbiAgYWlyVG9rZW4sXHJcbiAgYW1vdW50LFxyXG59OiB7XHJcbiAgYmxvY2tjaGFpbjogQmxvY2tjaGFpbjtcclxuICBnaXZlYXdheTogR2l2ZWF3YXk7XHJcbiAgcHJvamVjdEV2ZW50OiBQcm9qZWN0RXZlbnQ7XHJcbiAgYWlyVG9rZW46IEJsb2NrY2hhaW5Bc3NldDtcclxuICBhbW91bnQ6IEJpZ051bWJlcjtcclxufSkgPT4ge1xyXG4gIGNvbnN0IHsgZXhlY3V0ZVJlY2FwdGNoYSB9ID0gdXNlR29vZ2xlUmVDYXB0Y2hhKCk7XHJcbiAgY29uc3QgW2NsYWltRG90c2FtYV0gPSB1c2VDbGFpbURvdHNhbWFBaXJUb2tlbkdpdmVhd2F5KCk7XHJcbiAgY29uc3QgW2lzQ2xhaW1pbmcsIHNldElzQ2xhaW1pbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IHsgdCB9ID0gdXNlVHJhbnNsYXRpb24oJ3RyYW5zbGF0aW9uJyk7XHJcbiAgY29uc3QgeyBkYXRhOiB1c2VyRXZlbnRSZXdhcmRzRGF0YSwgbG9hZGluZzogaXNVc2VyRXZlbnRSZXdhcmRzTG9hZGluZyB9ID1cclxuICAgIHVzZUdldFVzZXJFdmVudFJld2FyZHMocHJvamVjdEV2ZW50LmlkKTtcclxuXHJcbiAgY29uc3QgcHJvY2Vzc2luZyA9IHVzZXJFdmVudFJld2FyZHNEYXRhPy51c2VyRXZlbnRSZXdhcmRzLmZpbmQoXHJcbiAgICAoaXRlbSkgPT4gaXRlbS5zdGF0dXMgPT09IFJld2FyZFN0YXR1cy5QUk9DRVNTSU5HLFxyXG4gICk7XHJcblxyXG4gIGNvbnN0IHR4SGFzaCA9IFsuLi4odXNlckV2ZW50UmV3YXJkc0RhdGE/LnVzZXJFdmVudFJld2FyZHMgfHwgW10pXVxyXG4gICAgLnNvcnQoKGEsIGIpID0+IHtcclxuICAgICAgY29uc3QgZGF0ZUEgPSArbmV3IERhdGUoYS51cGRhdGVkQXQpO1xyXG4gICAgICBjb25zdCBkYXRlQiA9ICtuZXcgRGF0ZShiLnVwZGF0ZWRBdCk7XHJcbiAgICAgIHJldHVybiBkYXRlQiAtIGRhdGVBO1xyXG4gICAgfSlcclxuICAgIC5maW5kKFxyXG4gICAgICAocmV3YXJkKSA9PiByZXdhcmQudHhIYXNoICYmIGdpdmVhd2F5LmlkID09PSByZXdhcmQuZ2l2ZWF3YXlJZCxcclxuICAgICk/LnR4SGFzaDtcclxuXHJcbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gYXN5bmMgKGNvbm5lY3RvckRhdGE6IERvdHNhbWFDb25uZWN0b3JEYXRhKSA9PiB7XHJcbiAgICBjb25zdCB7IGFjY291bnQgfSA9IGNvbm5lY3RvckRhdGE7XHJcblxyXG4gICAgaWYgKCFhY2NvdW50IHx8ICFhaXJUb2tlbikgcmV0dXJuO1xyXG4gICAgc2V0SXNDbGFpbWluZyh0cnVlKTtcclxuXHJcbiAgICBsZXQgY2FwdGNoYTogc3RyaW5nIHwgdW5kZWZpbmVkO1xyXG5cclxuICAgIGlmIChwcm9qZWN0RXZlbnQuaXBQcm90ZWN0KSB7XHJcbiAgICAgIGlmICghZXhlY3V0ZVJlY2FwdGNoYSkge1xyXG4gICAgICAgIHRvYXN0ZXIoe1xyXG4gICAgICAgICAgdGl0bGU6ICdGYWlsZWQnLFxyXG4gICAgICAgICAgdGV4dDogJ1JlY2FwdGNoYSBub3QgaW5pdGlhbGl6ZWQnLFxyXG4gICAgICAgICAgdHlwZTogJ2Vycm9yJyxcclxuICAgICAgICB9KTtcclxuICAgICAgICBzZXRJc0NsYWltaW5nKGZhbHNlKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNhcHRjaGEgPSBhd2FpdCBleGVjdXRlUmVjYXB0Y2hhKCdhaXJ0b2tlbl9kb3RzYW1hX2dpdmVhd2F5X2NsYWltJyk7XHJcbiAgICB9XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgZm9ybWF0dGVkQWRkcmVzcyA9IGNvbnZlcnRUb1NzNThBZGRyZXNzKFxyXG4gICAgICAgIGFjY291bnQsXHJcbiAgICAgICAgYmxvY2tjaGFpbi5jaGFpbklkLFxyXG4gICAgICApO1xyXG5cclxuICAgICAgYXdhaXQgY2xhaW1Eb3RzYW1hKHtcclxuICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgIHByb2plY3RJZDogcHJvamVjdEV2ZW50LnByb2plY3QuaWQsXHJcbiAgICAgICAgICBldmVudElkOiBwcm9qZWN0RXZlbnQuaWQsXHJcbiAgICAgICAgICBnaXZlYXdheUlkOiBnaXZlYXdheS5pZCxcclxuICAgICAgICAgIHVzZXJBZGRyZXNzOiBmb3JtYXR0ZWRBZGRyZXNzLFxyXG4gICAgICAgICAgY2FwdGNoYSxcclxuICAgICAgICB9LFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIHRvYXN0ZXIoe1xyXG4gICAgICAgIHRpdGxlOiAnU3VibWl0dGVkJyxcclxuICAgICAgICB0ZXh0OiAnWW91ciBjbGFpbSByZXF1ZXN0IGhhcyBiZWVuIHN1Ym1pdHRlZCwgY2hlY2sgeW91ciBub3RpZmljYXRpb25zIGZvciBhbiB1cGRhdGUuJyxcclxuICAgICAgICB0eXBlOiAnc3VjY2VzcycsXHJcbiAgICAgIH0pO1xyXG4gICAgfSBjYXRjaCAoZXJyOiBhbnkpIHtcclxuICAgICAgdG9hc3Rlcih7XHJcbiAgICAgICAgdGl0bGU6ICdGYWlsZWQnLFxyXG4gICAgICAgIHRleHQ6IGVyci5tZXNzYWdlLFxyXG4gICAgICAgIHR5cGU6ICdlcnJvcicsXHJcbiAgICAgIH0pO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0SXNDbGFpbWluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgaWYgKHByb2Nlc3NpbmcpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxTdWNjZXNzQWxlcnRCb3hcclxuICAgICAgICB0aXRsZT17dCgnZ2l2ZWF3YXkuYWlyVG9rZW5Qb29sLnN1Y2Nlc3NUaXRsZScpfVxyXG4gICAgICAgIHN1YnRpdGxlPXt0KCdnaXZlYXdheS5haXJUb2tlblBvb2wuc3VjY2Vzc1N1YnRpdGxlJyl9XHJcbiAgICAgIC8+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgaWYgKCFhbW91bnQuaXNaZXJvKCkpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgPERvdHNhbWFXYWxsZXRcclxuICAgICAgICAgIGJsb2NrY2hhaW49e2Jsb2NrY2hhaW59XHJcbiAgICAgICAgICBidXR0b249e3tcclxuICAgICAgICAgICAgY29uZmlybToge1xyXG4gICAgICAgICAgICAgIGVuYWJsZTogdHJ1ZSxcclxuICAgICAgICAgICAgICBsb2FkaW5nOiBpc0NsYWltaW5nIHx8IGlzVXNlckV2ZW50UmV3YXJkc0xvYWRpbmcsXHJcbiAgICAgICAgICAgICAgdGV4dDogYENsYWltICR7Zm9ybWF0QW1vdW50KFxyXG4gICAgICAgICAgICAgICAgYW1vdW50Py50b1N0cmluZygpLFxyXG4gICAgICAgICAgICAgICAgYWlyVG9rZW4uZGVjaW1hbHMsXHJcbiAgICAgICAgICAgICAgKX0gJHthaXJUb2tlbi50aWNrZXJ9IHVzaW5nIGAsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgICAgb25TdWNjZXNzPXtoYW5kbGVTdWJtaXR9XHJcbiAgICAgICAgICBleGNsdWRlZFdhbGxldHM9e1tXZWIzV2FsbGV0VHlwZS5ET1RTQU1BX01BTlVBTF19XHJcbiAgICAgICAgLz5cclxuICAgICAgICB7cHJvamVjdEV2ZW50LmlwUHJvdGVjdCAmJiAoXHJcbiAgICAgICAgICA8UmVjYXB0Y2hhRGVjbGFyYXRpb24gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWNzIHRleHQtY2VudGVyXCIgLz5cclxuICAgICAgICApfVxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gPEdpdmVhd2F5VHJhbnNhY3Rpb25IYXNoIHR4SGFzaD17dHhIYXNofSBibG9ja2NoYWluPXtibG9ja2NoYWlufSAvPjtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEFpclRva2VuRG90c2FtYUdpdmVhd2F5Q2xhaW07XHJcbiJdLCJuYW1lcyI6WyJTdWNjZXNzQWxlcnRCb3giLCJ0b2FzdGVyIiwiRG90c2FtYVdhbGxldCIsImNvbnZlcnRUb1NzNThBZGRyZXNzIiwiUmV3YXJkU3RhdHVzIiwiV2ViM1dhbGxldFR5cGUiLCJmb3JtYXRBbW91bnQiLCJ1c2VTdGF0ZSIsIkdpdmVhd2F5VHJhbnNhY3Rpb25IYXNoIiwidXNlR2V0VXNlckV2ZW50UmV3YXJkcyIsInVzZUNsYWltRG90c2FtYUFpclRva2VuR2l2ZWF3YXkiLCJ1c2VUcmFuc2xhdGlvbiIsInVzZUdvb2dsZVJlQ2FwdGNoYSIsIlJlY2FwdGNoYURlY2xhcmF0aW9uIiwiQWlyVG9rZW5Eb3RzYW1hR2l2ZWF3YXlDbGFpbSIsImdpdmVhd2F5IiwicHJvamVjdEV2ZW50IiwiYmxvY2tjaGFpbiIsImFpclRva2VuIiwiYW1vdW50IiwiZXhlY3V0ZVJlY2FwdGNoYSIsImNsYWltRG90c2FtYSIsImlzQ2xhaW1pbmciLCJzZXRJc0NsYWltaW5nIiwidCIsImRhdGEiLCJ1c2VyRXZlbnRSZXdhcmRzRGF0YSIsImxvYWRpbmciLCJpc1VzZXJFdmVudFJld2FyZHNMb2FkaW5nIiwiaWQiLCJwcm9jZXNzaW5nIiwidXNlckV2ZW50UmV3YXJkcyIsImZpbmQiLCJpdGVtIiwic3RhdHVzIiwiUFJPQ0VTU0lORyIsInR4SGFzaCIsInNvcnQiLCJhIiwiYiIsImRhdGVBIiwiRGF0ZSIsInVwZGF0ZWRBdCIsImRhdGVCIiwicmV3YXJkIiwiZ2l2ZWF3YXlJZCIsImhhbmRsZVN1Ym1pdCIsImNvbm5lY3RvckRhdGEiLCJhY2NvdW50IiwiY2FwdGNoYSIsImlwUHJvdGVjdCIsInRpdGxlIiwidGV4dCIsInR5cGUiLCJmb3JtYXR0ZWRBZGRyZXNzIiwiY2hhaW5JZCIsInZhcmlhYmxlcyIsInByb2plY3RJZCIsInByb2plY3QiLCJldmVudElkIiwidXNlckFkZHJlc3MiLCJlcnIiLCJtZXNzYWdlIiwic3VidGl0bGUiLCJpc1plcm8iLCJkaXYiLCJjbGFzc05hbWUiLCJidXR0b24iLCJjb25maXJtIiwiZW5hYmxlIiwidG9TdHJpbmciLCJkZWNpbWFscyIsInRpY2tlciIsIm9uU3VjY2VzcyIsImV4Y2x1ZGVkV2FsbGV0cyIsIkRPVFNBTUFfTUFOVUFMIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/Giveaways/AirTokenGiveaway/AirTokenDotsamaGiveawayClaim.tsx\n"));

/***/ })

});