{"general": {"title": "General", "description": "General settings"}, "global": {"project_one": "Community", "project_many": "Communities", "event_one": "Campaign", "event_many": "Campaigns", "task_one": "Quest", "task_many": "Quests", "platform": "AirLyft", "points": "Fuel", "xp": "XP", "projectPoints": "c<PERSON><PERSON>", "projectXp": "cXP"}, "user-menu": {"profile": "Profile", "participated-events": "Participated {{events}}", "followed-projects": "Followed {{projects}}", "all-rewards": "All My Rewards", "settings": "Settings", "logout": "Logout", "tpl": {"title": "Documentation", "subtitle": "Start integrating products and tools"}}, "auth": {"heading": "Login to Participate", "metamask": "Login via Metamask", "twitter": "Login via Twitter", "nova": "Login via Nova Wallet", "polkadot": "Login via PolkadotJs", "subwallet": {"evm": "Login via SubWallet (EVM)", "dot": "Login via SubWallet (DOT)"}, "talisman": {"evm": "Login via Talisman (EVM)", "dot": "Login via Talisman (DOT)"}, "email": "Passwordless Login", "magiclink": {"head": "Check your email", "body": "We have sent you a temporary verification code to ", "resend": "Didn't receive the email? <PERSON>sen<PERSON>", "send": "Send Verification Code", "code": "I already have the code ", "verify": "Verify", "codePlaceholder": "Enter your 6 digit verification code"}}, "giveaway": {"selectionTypes": {"random": "RANDOM RAFFLE 🎲", "manual": "MANUAL", "fcfs": "FCFS", "fcfsTask": "FCFS (Task Based)"}, "airTokenPool": {"manualSummaryTitle": "Manual Distribution", "shopDetails": {"unsuccessfulAlert": "We have detected an unsuccessful attempt to claim this item, you can try claiming again & your {{projectPoints}} will not be deducted twice."}, "warningTitle": "Sorry!", "warningPoolSubtitle": "", "warningTokenSubtitle": "Insufficient funds. <PERSON><PERSON> Cannot be claimed, please contact the community host.", "successTitle": "Submitted", "successSubtitle": "Your claim request has been submitted, check your notifications for an update."}, "whitelist": {"giveawayDetails": {"heading": "Whitelist / Manual Selection", "distributionDetails": {"title": "Distribution Details", "description": "This is a whitelist {{event}}. You will not be able to claim the rewards from {{platform}}. Instead, contact the {{event}} organizer for the rewards."}, "winner": {"heading": "Winner Message", "title": "Congrats!", "message": "You have been shortlisted in this {{event}}. Please contact {{projectName}} to claim your reward."}, "notWinner": {"title": "Sorry!", "message": "Better Luck next time, you were not shortlisted in this {{event}}."}}}, "merchandise": {"shopDetails": "Details", "giveawayClaim": {"title": "Sorry!", "subtitle": "This merchandise is now out of stock."}}, "distribution": {"title": "Winner Selection Information", "manualTitle": " Distribution Details", "descriptionManual": {"pre": "Please note that the winner selection type of this campaign is ", "mid": "manual.", "post": " Please check with the project host for more details."}, "descriptionRandomInfo": "Winners are chosen based on rank, but if tied they are chosen through a random raffle. ", "descriptionRandom": "Winners are chosen based on rank, but if tied they are chosen through a random raffle. Please check for the results ", "descriptionRandomResults": "Results Announced", "descriptionRandomLater": "later", "descriptionFCFSTask": {"hasWhitelist": {"pre": "You will only be able to claim this reward if you are in the ", "mid1": "whitellist", "mid2": " set by the host on a ", "mid3": "first come first serve", "post": " basis."}, "hasNoWhitelist": {"pre": "Please note that the giveaway type of this {{event}} is ", "mid": " first come first serve.", "post": " Complete the task below to claim the reward."}}, "descriptionFCFSRange": {"hasWhitelist": {"pre": "You will only be able to claim this reward if you are in the ", "mid1": "whitellist", "mid2": " set by the host on a ", "mid3": "first come first serve", "post": " basis. "}, "hasNoWhitelist": {"pre": "Please note that the giveaway type of this {{event}} is ", "mid": " first come first serve.", "post": " Satisfy the condition below to claim the reward. "}}}, "shopDetails": {"pre": "You have ", "mid": " {{projectPoints}} but you need ", "post": " {{projectPoints}} for this item, gain more cFuel by completing different quests within this community"}}, "onboarding": {"onboardingModal": {"title": "Please update your notification address so you never miss a reward!", "skipButton": "Skip now and update from <PERSON><PERSON><PERSON> later"}, "onboardingTerms": {"disclaimer": {"pre": "Disclaimer: {{platform}} is an open quest platform and we only do a preliminary verification of communities. Anyone can create campaigns on {{platform}} so ", "post": "Do Your Own Research!"}, "checkbox": {"pre": "I have read and agree to these ", "post": "terms and conditions."}, "buttonText": "Got It, Let's Go!"}}, "userNotification": {"emptyData": {"title": "Stay Updated", "subtitle": "No New Notifications."}, "rewardSettled": {"pre": "The results of the", "mid": " been announced.", "post": "Please check to see if you have won."}, "newProjectEvent": {"pre": "New Campaign ", "mid": " is live.", "post": "Participate now!"}, "taskInProgress": {"pre": "Your quest ", "mid": "from ", "post": "is in progress/review."}, "taskSuccess": {"pre": "Your claim ", "mid": " from ", "post": "was successful."}, "taskFailed": {"pre": "Your claim ", "mid": " from ", "post": "failed on the blockchain"}, "claimInProgress": {"pre": "Your reward claim for ", "mid": " from ", "post": "is in progress."}, "claimSuccess": {"pre": "Your reward claim for ", "mid": " from ", "post": "was successful."}, "claimFailed": {"pre": "Your reward claim for ", "mid": " from ", "post": "failed on the blockchain"}}, "dragDrop": {"title": "Click or drag file to this area to upload", "subtitle": "PNG, JPG or GIF"}, "tasks": {"airboost": {"copyLink": {"title": "Copy/Share your invite link", "inputLabel": "Your personal invite link for this {{event}}", "shareOn": "Share on"}, "referral": {"title": "Users who joined using your link & have completed at least one task will count as a successful referral & show up below automatically.", "description": "Only the first {{referralLimit}} referrals will be considered.", "descriptionPoints": "Earn {{reward}} for every successful referral. ", "progress": {"pointsEarned": "Earned {{pointsEarned}} {{projectPoints}}", "entriesEarned": "Referred {{entriesEarned}} user(s)", "noEntry": "No Entries Yet"}}, "warning": {"title": "Sorry!", "description": "{{platform}} could not retrieve your referral code."}, "header": "Complete and earn upto "}, "airquest": {"title": {"pre": "Open ", "post": " on {{platform}}."}, "description": "Click the button below to follow the community for you and verify the quest."}, "checkin": {"title": "Streak", "reminder": "Keep your {{streak}} day streak going!", "note": "*UTC timezone is followed"}, "claim": {"title": "Claim <PERSON> / View Results", "subtitle": "Click for details on claiming your reward"}, "discord": {"open": "Open ", "join": "Join ", "discord": " on Discord", "verify": "Verify on {{platform}}"}, "email": {"signin": "Sign in using your email address", "verify": "Verify on {{platform}}", "address": {"title": "Verify your email address to earn"}, "whitelist": {"title": "if your email belongs to the hosts whitelist"}, "subscribe": {"consent": "By clicking on the button below, I agree to receive newsletters and updates from the community host.", "verify": "Subscribe using"}}, "instagram": {"visit": {"title": "Visit a profile", "actionTitle": "Open Instagram to visit profile", "description": "Once you have visited the Instagram profile, you can click the button below to verify your action", "actionDescription": "Verify Profile Visit"}}, "quiz": {"progressCard": {"title": "Your Progress", "pre": "This answer will give you "}}, "rest": {"note": "Note: The action for this task needs to be performed on the {{event}} host's project itself."}, "subgraph": {"note": {"pre": "Note: ", "post": "This is an on-chain task, and the action needs to be performed on the {{event}} host's project itself. Once you perform the action, it might take a few minutes for the action to be logged."}, "query": "Query", "validation": "Validation"}, "telegram": {"join": "Join ", "telegram": " on Telegram", "open": "Open ", "verify": "Verify on {{platform}}"}, "terms": {"title": "Review Terms & Conditions", "accept": "I have read and agree to these terms and conditions.", "acceptSubstrate": "Agree by signing terms using your substrate wallet (no gas required)", "submit": "Submit your approval"}, "twitter": {"follow": {"title": "Follow @{{twitterHandle}} on Twitter", "actionTitle": "Open Twitter to follow", "description": "Follow via {{platform}}"}, "like": {"title": "Like this tweet on Twitter", "actionTitle": "Open Twitter to like", "description": "Like via {{platform}}"}, "retweet": {"title": "Retweet this tweet on Twitter", "actionTitle": "Open Twitter to retweet", "description": "Retweet via {{platform}}"}, "tweet": {"title": "Tweet this on Twitter", "actionTitle": "Open Twitter to tweet", "description": "Verify on {{platform}}", "proof": "Upload screenshot as proof"}, "ugc": {"title": "Post this on twitter", "actionTitle": "Open Twitter to tweet", "description": "Verify on {{platform}}"}, "whitelist": {"title": "Connect your Twitter account", "description": "Verify that your twitter account belongs to the hosts list."}}, "url": {"visit": {"title": "Visit a link", "actionTitle": "Open link in a new tab", "description": "Once you have visited the URL, you can click the button below to verify your action", "actionDescription": "Verify Action"}, "share": {"title": "Share the link on social media", "actionTitle": "Share on", "description": "Once you have shared the link, click the button below to verify", "actionDescription": "Verify Action"}}, "wallet": {"evm": {"title": "Sign in using your EVM wallet address", "whitelistTitlePoints": "If the EVM wallet address exists in the hosts list then you will get the {{projectPoints}}.", "whitelistTitle": "If the EVM wallet address exists in the hosts list then you can verify this quest."}, "dotsama": {"title": "Sign in using your Substrate wallet address", "whitelistTitlePoints": "If the substrate wallet address exists in the hosts list then you will get the {{projectPoints}}.", "whitelistTitle": "If the substrate wallet address exists in the hosts list then you can verify this quest."}}, "youtube": {"visit": {"title": "Visit a youtube link", "actionTitle": "Open Youtube and visit the link", "description": "Once you have visited the youtube link, you can click the button below to verify your action", "actionDescription": "Verify Action"}}, "completedCard": {"title": {"points": "Great! You earned {{points}} {{projectPoints}}", "noPoints": "You have performed this action"}, "subtitle": {"inReview": "The Community Admin will manually review your submission.", "inAIReview": "Your submission will be verified by an AI agent.", "valid": "Task Completed.", "invalid": "The Community Admin has rejected your submission."}}, "producthunt": {"upvote": {"title": "Upvote on Product Hunt", "verify": "Verify your upvote"}}, "blog": {"comment": {"step1": {"title": "Visit our blog and leave a comment", "extra": "Open link in a new tab"}, "step2": {"title": "Enter the username you used to comment on the blog"}}}, "kickstarter": {"support": {"title": "Support Kickstarter Project", "watch": "Watch the video to unlock", "visit": "Visit Kickstarter Project", "verify": "Verify your visit"}}}}