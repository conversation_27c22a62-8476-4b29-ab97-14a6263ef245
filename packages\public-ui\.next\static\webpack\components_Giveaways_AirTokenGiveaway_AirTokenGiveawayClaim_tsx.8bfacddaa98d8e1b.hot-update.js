"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("components_Giveaways_AirTokenGiveaway_AirTokenGiveawayClaim_tsx",{

/***/ "./components/Giveaways/AirTokenGiveaway/AirTokenDotsamaGiveawayClaim.tsx":
/*!********************************************************************************!*\
  !*** ./components/Giveaways/AirTokenGiveaway/AirTokenDotsamaGiveawayClaim.tsx ***!
  \********************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/AlertBox */ \"./components/AlertBox.tsx\");\n/* harmony import */ var _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Toaster/Toaster */ \"./components/Toaster/Toaster.tsx\");\n/* harmony import */ var _Components_Web3Wallet_Dotsama_DotsamaWallet__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/Web3Wallet/Dotsama/DotsamaWallet */ \"./components/Web3Wallet/Dotsama/DotsamaWallet.tsx\");\n/* harmony import */ var _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Root/helpers/dotsama */ \"./helpers/dotsama.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _GiveawayTransactionHash__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../GiveawayTransactionHash */ \"./components/Giveaways/GiveawayTransactionHash.tsx\");\n/* harmony import */ var _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../hooks/useGetUserEventRewards */ \"./components/Giveaways/hooks/useGetUserEventRewards.ts\");\n/* harmony import */ var _airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./airtoken-giveaway.gql */ \"./components/Giveaways/AirTokenGiveaway/airtoken-giveaway.gql.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-google-recaptcha-v3 */ \"./node_modules/react-google-recaptcha-v3/dist/react-google-recaptcha-v3.esm.js\");\n/* harmony import */ var _Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @Components/RecaptchaDeclaration */ \"./components/RecaptchaDeclaration.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AirTokenDotsamaGiveawayClaim = (param)=>{\n    let { giveaway, projectEvent, blockchain, airToken, amount } = param;\n    var _sort_find;\n    _s();\n    const { executeRecaptcha } = (0,react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_12__.useGoogleReCaptcha)();\n    const [claimDotsama] = (0,_airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_10__.useClaimDotsamaAirTokenGiveaway)();\n    const [isClaiming, setIsClaiming] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_11__.useTranslation)(\"translation\");\n    const { data: userEventRewardsData, loading: isUserEventRewardsLoading } = (0,_hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_9__.useGetUserEventRewards)(projectEvent.id);\n    const processing = userEventRewardsData === null || userEventRewardsData === void 0 ? void 0 : userEventRewardsData.userEventRewards.find((item)=>item.status === _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.RewardStatus.PROCESSING);\n    const txHash = (_sort_find = [\n        ...(userEventRewardsData === null || userEventRewardsData === void 0 ? void 0 : userEventRewardsData.userEventRewards) || []\n    ].sort((a, b)=>{\n        const dateA = +new Date(a.updatedAt);\n        const dateB = +new Date(b.updatedAt);\n        return dateB - dateA;\n    }).find((reward)=>reward.txHash && giveaway.id === reward.giveawayId)) === null || _sort_find === void 0 ? void 0 : _sort_find.txHash;\n    const handleSubmit = async (connectorData)=>{\n        const { account } = connectorData;\n        if (!account || !airToken) return;\n        setIsClaiming(true);\n        let captcha;\n        if (projectEvent.ipProtect) {\n            if (!executeRecaptcha) {\n                (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                    title: \"Failed\",\n                    text: \"Recaptcha not initialized\",\n                    type: \"error\"\n                });\n                setIsClaiming(false);\n                return;\n            }\n            captcha = await executeRecaptcha(\"airtoken_dotsama_giveaway_claim\");\n        }\n        try {\n            const formattedAddress = (0,_Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__.convertToSs58Address)(account, blockchain.chainId);\n            await claimDotsama({\n                variables: {\n                    projectId: projectEvent.project.id,\n                    eventId: projectEvent.id,\n                    giveawayId: giveaway.id,\n                    userAddress: formattedAddress,\n                    captcha\n                }\n            });\n            (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                title: \"Submitted\",\n                text: \"Your claim request has been submitted, check your notifications for an update.\",\n                type: \"success\"\n            });\n        } catch (err) {\n            (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                title: \"Failed\",\n                text: err.message,\n                type: \"error\"\n            });\n        } finally{\n            setIsClaiming(false);\n        }\n    };\n    if (processing) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__.SuccessAlertBox, {\n            title: t(\"giveaway.airTokenPool.successTitle\"),\n            subtitle: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: t(\"giveaway.airTokenPool.successSubtitle\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 13\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-sm opacity-90\",\n                        children: [\n                            \"Claiming \",\n                            (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__.formatAmount)(amount === null || amount === void 0 ? void 0 : amount.toString(), airToken.decimals),\n                            \" \",\n                            airToken.ticker,\n                            ' from \"',\n                            giveaway.title || \"Giveaway\",\n                            '\"'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 13\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n                lineNumber: 117,\n                columnNumber: 11\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n            lineNumber: 114,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!amount.isZero()) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Web3Wallet_Dotsama_DotsamaWallet__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    blockchain: blockchain,\n                    button: {\n                        confirm: {\n                            enable: true,\n                            loading: isClaiming || isUserEventRewardsLoading,\n                            text: \"Claim \".concat((0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__.formatAmount)(amount === null || amount === void 0 ? void 0 : amount.toString(), airToken.decimals), \" \").concat(airToken.ticker, \" using \")\n                        }\n                    },\n                    onSuccess: handleSubmit,\n                    excludedWallets: [\n                        _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.Web3WalletType.DOTSAMA_MANUAL\n                    ]\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, undefined),\n                projectEvent.ipProtect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_13__.RecaptchaDeclaration, {\n                    className: \"text-xs text-cs text-center\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawayTransactionHash__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        txHash: txHash,\n        blockchain: blockchain\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n        lineNumber: 154,\n        columnNumber: 10\n    }, undefined);\n};\n_s(AirTokenDotsamaGiveawayClaim, \"mdMu2YwNR2p0kya76ZQkFiX65Uc=\", false, function() {\n    return [\n        react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_12__.useGoogleReCaptcha,\n        _airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_10__.useClaimDotsamaAirTokenGiveaway,\n        next_i18next__WEBPACK_IMPORTED_MODULE_11__.useTranslation,\n        _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_9__.useGetUserEventRewards\n    ];\n});\n_c = AirTokenDotsamaGiveawayClaim;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AirTokenDotsamaGiveawayClaim);\nvar _c;\n$RefreshReg$(_c, \"AirTokenDotsamaGiveawayClaim\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/AirTokenGiveaway/AirTokenDotsamaGiveawayClaim.tsx\n"));

/***/ })

});