/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["components_Giveaways_AirTokenGiveaway_AirTokenShopDetails_tsx"],{

/***/ "./components/Web3Wallet lazy recursive ^\\.\\/.*$":
/*!***************************************************************!*\
  !*** ./components/Web3Wallet/ lazy ^\.\/.*$ namespace object ***!
  \***************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

var map = {
	"./Dotsama/DotsamaAccountList": [
		"./components/Web3Wallet/Dotsama/DotsamaAccountList.tsx",
		"components_Web3Wallet_Dotsama_DotsamaAccountList_tsx"
	],
	"./Dotsama/DotsamaAccountList.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaAccountList.tsx",
		"components_Web3Wallet_Dotsama_DotsamaAccountList_tsx"
	],
	"./Dotsama/DotsamaManual": [
		"./components/Web3Wallet/Dotsama/DotsamaManual.tsx",
		"components_Web3Wallet_Dotsama_DotsamaManual_tsx"
	],
	"./Dotsama/DotsamaManual.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaManual.tsx",
		"components_Web3Wallet_Dotsama_DotsamaManual_tsx"
	],
	"./Dotsama/DotsamaNova": [
		"./components/Web3Wallet/Dotsama/DotsamaNova.tsx",
		"components_Web3Wallet_Dotsama_DotsamaNova_tsx"
	],
	"./Dotsama/DotsamaNova.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaNova.tsx",
		"components_Web3Wallet_Dotsama_DotsamaNova_tsx"
	],
	"./Dotsama/DotsamaPolkadotJs": [
		"./components/Web3Wallet/Dotsama/DotsamaPolkadotJs.tsx",
		"components_Web3Wallet_Dotsama_DotsamaPolkadotJs_tsx"
	],
	"./Dotsama/DotsamaPolkadotJs.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaPolkadotJs.tsx",
		"components_Web3Wallet_Dotsama_DotsamaPolkadotJs_tsx"
	],
	"./Dotsama/DotsamaRaw": [
		"./components/Web3Wallet/Dotsama/DotsamaRaw.tsx",
		"components_Web3Wallet_Dotsama_DotsamaRaw_tsx"
	],
	"./Dotsama/DotsamaRaw.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaRaw.tsx",
		"components_Web3Wallet_Dotsama_DotsamaRaw_tsx"
	],
	"./Dotsama/DotsamaSubwallet": [
		"./components/Web3Wallet/Dotsama/DotsamaSubwallet.tsx",
		"components_Web3Wallet_Dotsama_DotsamaSubwallet_tsx"
	],
	"./Dotsama/DotsamaSubwallet.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaSubwallet.tsx",
		"components_Web3Wallet_Dotsama_DotsamaSubwallet_tsx"
	],
	"./Dotsama/DotsamaTalisman": [
		"./components/Web3Wallet/Dotsama/DotsamaTalisman.tsx",
		"components_Web3Wallet_Dotsama_DotsamaTalisman_tsx"
	],
	"./Dotsama/DotsamaTalisman.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaTalisman.tsx",
		"components_Web3Wallet_Dotsama_DotsamaTalisman_tsx"
	],
	"./Dotsama/DotsamaWallet": [
		"./components/Web3Wallet/Dotsama/DotsamaWallet.tsx",
		"components_Web3Wallet_Dotsama_DotsamaWallet_tsx"
	],
	"./Dotsama/DotsamaWallet.tsx": [
		"./components/Web3Wallet/Dotsama/DotsamaWallet.tsx",
		"components_Web3Wallet_Dotsama_DotsamaWallet_tsx"
	],
	"./Dotsama/WalletNotFound": [
		"./components/Web3Wallet/Dotsama/WalletNotFound.tsx",
		"components_Web3Wallet_Dotsama_WalletNotFound_tsx"
	],
	"./Dotsama/WalletNotFound.tsx": [
		"./components/Web3Wallet/Dotsama/WalletNotFound.tsx",
		"components_Web3Wallet_Dotsama_WalletNotFound_tsx"
	],
	"./Evm/EvmManual": [
		"./components/Web3Wallet/Evm/EvmManual.tsx",
		"components_Web3Wallet_Evm_EvmManual_tsx"
	],
	"./Evm/EvmManual.tsx": [
		"./components/Web3Wallet/Evm/EvmManual.tsx",
		"components_Web3Wallet_Evm_EvmManual_tsx"
	],
	"./Evm/EvmMetamask": [
		"./components/Web3Wallet/Evm/EvmMetamask.tsx",
		"components_Web3Wallet_Evm_EvmMetamask_tsx"
	],
	"./Evm/EvmMetamask.tsx": [
		"./components/Web3Wallet/Evm/EvmMetamask.tsx",
		"components_Web3Wallet_Evm_EvmMetamask_tsx"
	],
	"./Evm/EvmSubwallet": [
		"./components/Web3Wallet/Evm/EvmSubwallet.tsx",
		"components_Web3Wallet_Evm_EvmSubwallet_tsx"
	],
	"./Evm/EvmSubwallet.tsx": [
		"./components/Web3Wallet/Evm/EvmSubwallet.tsx",
		"components_Web3Wallet_Evm_EvmSubwallet_tsx"
	],
	"./Evm/EvmTalisman": [
		"./components/Web3Wallet/Evm/EvmTalisman.tsx",
		"components_Web3Wallet_Evm_EvmTalisman_tsx"
	],
	"./Evm/EvmTalisman.tsx": [
		"./components/Web3Wallet/Evm/EvmTalisman.tsx",
		"components_Web3Wallet_Evm_EvmTalisman_tsx"
	],
	"./Evm/EvmWallet": [
		"./components/Web3Wallet/Evm/EvmWallet.tsx",
		"components_Web3Wallet_Evm_EvmWallet_tsx"
	],
	"./Evm/EvmWallet.tsx": [
		"./components/Web3Wallet/Evm/EvmWallet.tsx",
		"components_Web3Wallet_Evm_EvmWallet_tsx"
	],
	"./Evm/EvmWalletConnect": [
		"./components/Web3Wallet/Evm/EvmWalletConnect.tsx",
		"components_Web3Wallet_Evm_EvmWalletConnect_tsx"
	],
	"./Evm/EvmWalletConnect.tsx": [
		"./components/Web3Wallet/Evm/EvmWalletConnect.tsx",
		"components_Web3Wallet_Evm_EvmWalletConnect_tsx"
	],
	"./Evm/GenericInjectedEvm": [
		"./components/Web3Wallet/Evm/GenericInjectedEvm.tsx",
		"components_Web3Wallet_Evm_GenericInjectedEvm_tsx"
	],
	"./Evm/GenericInjectedEvm.tsx": [
		"./components/Web3Wallet/Evm/GenericInjectedEvm.tsx",
		"components_Web3Wallet_Evm_GenericInjectedEvm_tsx"
	],
	"./Web3WalletRenderer": [
		"./components/Web3Wallet/Web3WalletRenderer.tsx"
	],
	"./Web3WalletRenderer.tsx": [
		"./components/Web3Wallet/Web3WalletRenderer.tsx"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(function() {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return Promise.all(ids.slice(1).map(__webpack_require__.e)).then(function() {
		return __webpack_require__(id);
	});
}
webpackAsyncContext.keys = function() { return Object.keys(map); };
webpackAsyncContext.id = "./components/Web3Wallet lazy recursive ^\\.\\/.*$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "./components/AlertBox.tsx":
/*!*********************************!*\
  !*** ./components/AlertBox.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleWarningAlertBox: function() { return /* binding */ SimpleWarningAlertBox; },\n/* harmony export */   SuccessAlertBox: function() { return /* binding */ SuccessAlertBox; },\n/* harmony export */   WarningAlertBox: function() { return /* binding */ WarningAlertBox; },\n/* harmony export */   \"default\": function() { return /* binding */ AlertBox; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @phosphor-icons/react */ \"./node_modules/@phosphor-icons/react/dist/index.es.js\");\n\n\n\nfunction AlertBox(param) {\n    let { title, subtitle, icon, className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex flex-col p-4 py-8 rounded-2xl relative overflow-hidden text-primary-foreground\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -top-[40px] -right-[26px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-0 w-52 h-52 -bottom-[66px] -right-[60px] bg-[#ffffff1a] rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"primary-gradient-box-circle-icon\",\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg mb-0\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, this),\n                            subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-opacity-80\",\n                                children: subtitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n_c = AlertBox;\nconst SimpleWarningAlertBox = (param)=>{\n    let { title, description } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-red-100 border border-red-300 text-red-600 px-4 py-3 rounded relative mt-4 space-x-2\",\n        role: \"alert\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                className: \"font-bold\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 51,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"block sm:inline\",\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n                lineNumber: 52,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = SimpleWarningAlertBox;\nfunction SuccessAlertBox(param) {\n    let { title, subtitle } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertBox, {\n        className: \"gradient-primary\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Check, {\n            className: \"h-10 text-primary-foreground\",\n            size: 32\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n            lineNumber: 66,\n            columnNumber: 13\n        }, void 0),\n        title: title,\n        subtitle: subtitle\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n_c2 = SuccessAlertBox;\nfunction WarningAlertBox(param) {\n    let { title, subtitle } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertBox, {\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_2__.Info, {\n            fontSize: 28\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n            lineNumber: 82,\n            columnNumber: 13\n        }, void 0),\n        title: title,\n        subtitle: subtitle,\n        className: \"gradient-warning text-white\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\AlertBox.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n_c3 = WarningAlertBox;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"AlertBox\");\n$RefreshReg$(_c1, \"SimpleWarningAlertBox\");\n$RefreshReg$(_c2, \"SuccessAlertBox\");\n$RefreshReg$(_c3, \"WarningAlertBox\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/AlertBox.tsx\n"));

/***/ }),

/***/ "./components/BlockExplorerLink.tsx":
/*!******************************************!*\
  !*** ./components/BlockExplorerLink.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BlockExplorerLink; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction BlockExplorerLink(param) {\n    let { hash, blockExplorerUrls } = param;\n    return hash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        className: \"underline inline-flex text-sm\",\n        target: \"_blank\",\n        href: (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.getExplorerLink)(blockExplorerUrls, hash, _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_2__.ExplorerDataType.TRANSACTION),\n        rel: \"noreferrer\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-1 inline-block\",\n                children: \"View on Explorer \"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-4 w-4 inline-block\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\BlockExplorerLink.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_c = BlockExplorerLink;\nvar _c;\n$RefreshReg$(_c, \"BlockExplorerLink\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0Jsb2NrRXhwbG9yZXJMaW5rLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFpQztBQUNxQztBQUV2RCxTQUFTRyxrQkFBa0IsS0FNekM7UUFOeUMsRUFDeENDLElBQUksRUFDSkMsaUJBQWlCLEVBSWxCLEdBTnlDO0lBT3hDLE9BQU9ELHFCQUNMLDhEQUFDRTtRQUNDQyxXQUFVO1FBQ1ZDLFFBQU87UUFDUEMsTUFBTVAsa0VBQWVBLENBQ25CRyxtQkFDQUQsTUFDQUgsK0RBQWdCQSxDQUFDUyxXQUFXO1FBRTlCQyxLQUFJOzswQkFFSiw4REFBQ0M7Z0JBQUtMLFdBQVU7MEJBQW9COzs7Ozs7WUFBeUI7MEJBQzdELDhEQUFDTTtnQkFDQ0MsT0FBTTtnQkFDTlAsV0FBVTtnQkFDVlEsTUFBSztnQkFDTEMsU0FBUTtnQkFDUkMsUUFBTzswQkFFUCw0RUFBQ0M7b0JBQ0NDLGVBQWM7b0JBQ2RDLGdCQUFlO29CQUNmQyxhQUFhO29CQUNiQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzZCQUtSLDhEQUFDdEIsMkNBQVFBOzs7OztBQUViO0tBckN3QkciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9CbG9ja0V4cGxvcmVyTGluay50c3g/NWM3NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBGcmFnbWVudCB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgRXhwbG9yZXJEYXRhVHlwZSwgZ2V0RXhwbG9yZXJMaW5rIH0gZnJvbSAnQGFpcmx5ZnQvd2ViMy1ldm0nO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQmxvY2tFeHBsb3Jlckxpbmsoe1xyXG4gIGhhc2gsXHJcbiAgYmxvY2tFeHBsb3JlclVybHMsXHJcbn06IHtcclxuICBoYXNoOiBzdHJpbmcgfCB1bmRlZmluZWQ7XHJcbiAgYmxvY2tFeHBsb3JlclVybHM6IEFycmF5PHN0cmluZz47XHJcbn0pIHtcclxuICByZXR1cm4gaGFzaCA/IChcclxuICAgIDxhXHJcbiAgICAgIGNsYXNzTmFtZT1cInVuZGVybGluZSBpbmxpbmUtZmxleCB0ZXh0LXNtXCJcclxuICAgICAgdGFyZ2V0PVwiX2JsYW5rXCJcclxuICAgICAgaHJlZj17Z2V0RXhwbG9yZXJMaW5rKFxyXG4gICAgICAgIGJsb2NrRXhwbG9yZXJVcmxzLFxyXG4gICAgICAgIGhhc2gsXHJcbiAgICAgICAgRXhwbG9yZXJEYXRhVHlwZS5UUkFOU0FDVElPTixcclxuICAgICAgKX1cclxuICAgICAgcmVsPVwibm9yZWZlcnJlclwiXHJcbiAgICA+XHJcbiAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1yLTEgaW5saW5lLWJsb2NrXCI+VmlldyBvbiBFeHBsb3JlciA8L3NwYW4+eycgJ31cclxuICAgICAgPHN2Z1xyXG4gICAgICAgIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIlxyXG4gICAgICAgIGNsYXNzTmFtZT1cImgtNCB3LTQgaW5saW5lLWJsb2NrXCJcclxuICAgICAgICBmaWxsPVwibm9uZVwiXHJcbiAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXHJcbiAgICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcclxuICAgICAgPlxyXG4gICAgICAgIDxwYXRoXHJcbiAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxyXG4gICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXHJcbiAgICAgICAgICBzdHJva2VXaWR0aD17Mn1cclxuICAgICAgICAgIGQ9XCJNMTAgNkg2YTIgMiAwIDAwLTIgMnYxMGEyIDIgMCAwMDIgMmgxMGEyIDIgMCAwMDItMnYtNE0xNCA0aDZtMCAwdjZtMC02TDEwIDE0XCJcclxuICAgICAgICAvPlxyXG4gICAgICA8L3N2Zz5cclxuICAgIDwvYT5cclxuICApIDogKFxyXG4gICAgPEZyYWdtZW50IC8+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiRnJhZ21lbnQiLCJFeHBsb3JlckRhdGFUeXBlIiwiZ2V0RXhwbG9yZXJMaW5rIiwiQmxvY2tFeHBsb3JlckxpbmsiLCJoYXNoIiwiYmxvY2tFeHBsb3JlclVybHMiLCJhIiwiY2xhc3NOYW1lIiwidGFyZ2V0IiwiaHJlZiIsIlRSQU5TQUNUSU9OIiwicmVsIiwic3BhbiIsInN2ZyIsInhtbG5zIiwiZmlsbCIsInZpZXdCb3giLCJzdHJva2UiLCJwYXRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiLCJkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/BlockExplorerLink.tsx\n"));

/***/ }),

/***/ "./components/Giveaways/AirTokenGiveaway/AirTokenDotsamaGiveawayClaim.tsx":
/*!********************************************************************************!*\
  !*** ./components/Giveaways/AirTokenGiveaway/AirTokenDotsamaGiveawayClaim.tsx ***!
  \********************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/AlertBox */ \"./components/AlertBox.tsx\");\n/* harmony import */ var _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Toaster/Toaster */ \"./components/Toaster/Toaster.tsx\");\n/* harmony import */ var _Components_Web3Wallet_Dotsama_DotsamaWallet__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/Web3Wallet/Dotsama/DotsamaWallet */ \"./components/Web3Wallet/Dotsama/DotsamaWallet.tsx\");\n/* harmony import */ var _Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Root/helpers/dotsama */ \"./helpers/dotsama.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _GiveawayTransactionHash__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../GiveawayTransactionHash */ \"./components/Giveaways/GiveawayTransactionHash.tsx\");\n/* harmony import */ var _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../hooks/useGetUserEventRewards */ \"./components/Giveaways/hooks/useGetUserEventRewards.ts\");\n/* harmony import */ var _Hooks_useFindUserRewardByStatus__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @Hooks/useFindUserRewardByStatus */ \"./hooks/useFindUserRewardByStatus.ts\");\n/* harmony import */ var _airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./airtoken-giveaway.gql */ \"./components/Giveaways/AirTokenGiveaway/airtoken-giveaway.gql.ts\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-google-recaptcha-v3 */ \"./node_modules/react-google-recaptcha-v3/dist/react-google-recaptcha-v3.esm.js\");\n/* harmony import */ var _Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @Components/RecaptchaDeclaration */ \"./components/RecaptchaDeclaration.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AirTokenDotsamaGiveawayClaim = (param)=>{\n    let { giveaway, projectEvent, blockchain, airToken, amount } = param;\n    var _giveawayRewards_findUserRewardByStatus, _sort_find;\n    _s();\n    const { executeRecaptcha } = (0,react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_13__.useGoogleReCaptcha)();\n    const [claimDotsama] = (0,_airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_11__.useClaimDotsamaAirTokenGiveaway)();\n    const [isClaiming, setIsClaiming] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_12__.useTranslation)(\"translation\");\n    const { data: userEventRewardsData, loading: isUserEventRewardsLoading } = (0,_hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_9__.useGetUserEventRewards)(projectEvent.id);\n    // Check for processing status specifically for this giveaway\n    const { data: giveawayRewards } = (0,_Hooks_useFindUserRewardByStatus__WEBPACK_IMPORTED_MODULE_10__.useFindUserRewardByStatus)(projectEvent.id, giveaway.id);\n    const processing = giveawayRewards === null || giveawayRewards === void 0 ? void 0 : (_giveawayRewards_findUserRewardByStatus = giveawayRewards.findUserRewardByStatus) === null || _giveawayRewards_findUserRewardByStatus === void 0 ? void 0 : _giveawayRewards_findUserRewardByStatus.find((item)=>item.status === _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.RewardStatus.PROCESSING);\n    const txHash = (_sort_find = [\n        ...(userEventRewardsData === null || userEventRewardsData === void 0 ? void 0 : userEventRewardsData.userEventRewards) || []\n    ].sort((a, b)=>{\n        const dateA = +new Date(a.updatedAt);\n        const dateB = +new Date(b.updatedAt);\n        return dateB - dateA;\n    }).find((reward)=>reward.txHash && giveaway.id === reward.giveawayId)) === null || _sort_find === void 0 ? void 0 : _sort_find.txHash;\n    const handleSubmit = async (connectorData)=>{\n        const { account } = connectorData;\n        if (!account || !airToken) return;\n        setIsClaiming(true);\n        let captcha;\n        if (projectEvent.ipProtect) {\n            if (!executeRecaptcha) {\n                (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                    title: \"Failed\",\n                    text: \"Recaptcha not initialized\",\n                    type: \"error\"\n                });\n                setIsClaiming(false);\n                return;\n            }\n            captcha = await executeRecaptcha(\"airtoken_dotsama_giveaway_claim\");\n        }\n        try {\n            const formattedAddress = (0,_Root_helpers_dotsama__WEBPACK_IMPORTED_MODULE_4__.convertToSs58Address)(account, blockchain.chainId);\n            await claimDotsama({\n                variables: {\n                    projectId: projectEvent.project.id,\n                    eventId: projectEvent.id,\n                    giveawayId: giveaway.id,\n                    userAddress: formattedAddress,\n                    captcha\n                }\n            });\n            (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                title: \"Submitted\",\n                text: \"Your claim request has been submitted, check your notifications for an update.\",\n                type: \"success\"\n            });\n        } catch (err) {\n            (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                title: \"Failed\",\n                text: err.message,\n                type: \"error\"\n            });\n        } finally{\n            setIsClaiming(false);\n        }\n    };\n    if (processing) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__.SuccessAlertBox, {\n            title: t(\"giveaway.airTokenPool.successTitle\"),\n            subtitle: t(\"giveaway.airTokenPool.successSubtitle\")\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!amount.isZero()) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Web3Wallet_Dotsama_DotsamaWallet__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    blockchain: blockchain,\n                    button: {\n                        confirm: {\n                            enable: true,\n                            loading: isClaiming || isUserEventRewardsLoading,\n                            text: \"Claim \".concat((0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_6__.formatAmount)(amount === null || amount === void 0 ? void 0 : amount.toString(), airToken.decimals), \" \").concat(airToken.ticker, \" using \")\n                        }\n                    },\n                    onSuccess: handleSubmit,\n                    excludedWallets: [\n                        _airlyft_types__WEBPACK_IMPORTED_MODULE_5__.Web3WalletType.DOTSAMA_MANUAL\n                    ]\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, undefined),\n                projectEvent.ipProtect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_14__.RecaptchaDeclaration, {\n                    className: \"text-xs text-cs text-center\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n            lineNumber: 130,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawayTransactionHash__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        txHash: txHash,\n        blockchain: blockchain\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenDotsamaGiveawayClaim.tsx\",\n        lineNumber: 153,\n        columnNumber: 10\n    }, undefined);\n};\n_s(AirTokenDotsamaGiveawayClaim, \"enwpcyUUdQ62tt4DF87Q0WOlUaw=\", false, function() {\n    return [\n        react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_13__.useGoogleReCaptcha,\n        _airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_11__.useClaimDotsamaAirTokenGiveaway,\n        next_i18next__WEBPACK_IMPORTED_MODULE_12__.useTranslation,\n        _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_9__.useGetUserEventRewards,\n        _Hooks_useFindUserRewardByStatus__WEBPACK_IMPORTED_MODULE_10__.useFindUserRewardByStatus\n    ];\n});\n_c = AirTokenDotsamaGiveawayClaim;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AirTokenDotsamaGiveawayClaim);\nvar _c;\n$RefreshReg$(_c, \"AirTokenDotsamaGiveawayClaim\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/AirTokenGiveaway/AirTokenDotsamaGiveawayClaim.tsx\n"));

/***/ }),

/***/ "./components/Giveaways/AirTokenGiveaway/AirTokenEVMGiveawayClaim.tsx":
/*!****************************************************************************!*\
  !*** ./components/Giveaways/AirTokenGiveaway/AirTokenEVMGiveawayClaim.tsx ***!
  \****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/AlertBox */ \"./components/AlertBox.tsx\");\n/* harmony import */ var _Components_Loaders_ParagraphLoader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Loaders/ParagraphLoader */ \"./components/Loaders/ParagraphLoader.tsx\");\n/* harmony import */ var _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/Toaster/Toaster */ \"./components/Toaster/Toaster.tsx\");\n/* harmony import */ var _Components_TransactionResult__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/TransactionResult */ \"./components/TransactionResult.tsx\");\n/* harmony import */ var _Components_Web3Wallet_Evm_EvmWallet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @Components/Web3Wallet/Evm/EvmWallet */ \"./components/Web3Wallet/Evm/EvmWallet.tsx\");\n/* harmony import */ var _Hooks_useGiveawayTxHash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @Hooks/useGiveawayTxHash */ \"./hooks/useGiveawayTxHash.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ethers */ \"./node_modules/ethers/lib.esm/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _hooks_useGetTokenWindowInfo__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../hooks/useGetTokenWindowInfo */ \"./components/Giveaways/hooks/useGetTokenWindowInfo.ts\");\n/* harmony import */ var _useAirTokenGiveawayClaim__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./useAirTokenGiveawayClaim */ \"./components/Giveaways/AirTokenGiveaway/useAirTokenGiveawayClaim.tsx\");\n/* harmony import */ var _GiveawayTransactionHash__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../GiveawayTransactionHash */ \"./components/Giveaways/GiveawayTransactionHash.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react-google-recaptcha-v3 */ \"./node_modules/react-google-recaptcha-v3/dist/react-google-recaptcha-v3.esm.js\");\n/* harmony import */ var _Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @Components/RecaptchaDeclaration */ \"./components/RecaptchaDeclaration.tsx\");\n/* harmony import */ var _Root_services_tracking__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @Root/services/tracking */ \"./services/tracking.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AirTokenEVMGiveawayClaim = (param)=>{\n    let { giveaway, projectEvent, blockchain, airToken, amount } = param;\n    _s();\n    const { claimRewardTrack } = (0,_Root_services_tracking__WEBPACK_IMPORTED_MODULE_16__.useGtmTrack)();\n    const { executeRecaptcha } = (0,react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_14__.useGoogleReCaptcha)();\n    const { txHash: giveawayTxHash } = (0,_Hooks_useGiveawayTxHash__WEBPACK_IMPORTED_MODULE_6__.useGiveawayTxHash)(giveaway.id);\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_13__.useTranslation)(\"translation\");\n    const giveawayInfo = giveaway.info;\n    const [tx, setTx] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)();\n    const [txHash, setTxHash] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)();\n    const { claim, loading: claiming } = (0,_useAirTokenGiveawayClaim__WEBPACK_IMPORTED_MODULE_11__.useAirTokenGiveawayClaim)(projectEvent.project.id, projectEvent.id, giveaway.id, blockchain, airToken);\n    const { limitReached, loading: windowLoading } = (0,_hooks_useGetTokenWindowInfo__WEBPACK_IMPORTED_MODULE_10__.useGetTokenWindowInfo)(airToken.contractAddress || \"\", giveaway.id, ethers__WEBPACK_IMPORTED_MODULE_17__.BigNumber.from(giveawayInfo.amount || 0), amount, giveawayInfo.capped, blockchain);\n    const handleSubmit = async (connectorData)=>{\n        const gasLess = !!giveaway.gasless;\n        let captcha;\n        if (projectEvent.ipProtect) {\n            if (!executeRecaptcha) {\n                (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n                    title: \"Failed\",\n                    text: \"Recaptcha not initialized\",\n                    type: \"error\"\n                });\n                return;\n            }\n            captcha = await executeRecaptcha(\"airtoken_evm_giveaway_claim\");\n        }\n        claim({\n            connectorData,\n            onError: (err)=>{\n                (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n                    title: \"Failed\",\n                    text: err.message,\n                    type: \"error\"\n                });\n            },\n            onSuccess: (data)=>{\n                claimRewardTrack({\n                    projectId: projectEvent.project.id,\n                    eventId: projectEvent.id,\n                    projectTitle: projectEvent.project.name,\n                    eventTitle: projectEvent.title,\n                    giveawayId: giveaway.id,\n                    giveawayTitle: giveaway.title || \"\"\n                });\n                if (gasLess) {\n                    setTxHash(data.txHash);\n                } else {\n                    setTx(data.tx);\n                }\n            },\n            gasLess,\n            captcha\n        });\n    };\n    if (windowLoading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_ParagraphLoader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenEVMGiveawayClaim.tsx\",\n        lineNumber: 109,\n        columnNumber: 29\n    }, undefined);\n    if (!amount.isZero() && limitReached) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__.WarningAlertBox, {\n            title: t(\"giveaway.airTokenPool.warningTitle\"),\n            subtitle: t(\"giveaway.airTokenPool.warningTokenSubtitle\")\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenEVMGiveawayClaim.tsx\",\n            lineNumber: 113,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!amount.isZero()) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: tx || txHash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TransactionResult__WEBPACK_IMPORTED_MODULE_4__.TransactionResult, {\n                tx: tx,\n                txHash: txHash,\n                blockchain: blockchain\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenEVMGiveawayClaim.tsx\",\n                lineNumber: 124,\n                columnNumber: 11\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Web3Wallet_Evm_EvmWallet__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        blockchain: blockchain,\n                        size: \"default\",\n                        button: {\n                            confirm: {\n                                enable: true,\n                                loading: claiming,\n                                text: \"Claim \".concat((0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_8__.formatAmount)(amount === null || amount === void 0 ? void 0 : amount.toString(), airToken.decimals), \" \").concat(airToken.ticker, \" using \")\n                            }\n                        },\n                        onSuccess: (item)=>handleSubmit(item),\n                        excludedWallets: giveaway.gasless ? undefined : [\n                            _airlyft_types__WEBPACK_IMPORTED_MODULE_7__.Web3WalletType.EVM_MANUAL\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenEVMGiveawayClaim.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 13\n                    }, undefined),\n                    projectEvent.ipProtect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_RecaptchaDeclaration__WEBPACK_IMPORTED_MODULE_15__.RecaptchaDeclaration, {\n                        className: \"text-xs text-cs text-center\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenEVMGiveawayClaim.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 15\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenEVMGiveawayClaim.tsx\",\n                lineNumber: 126,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenEVMGiveawayClaim.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawayTransactionHash__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n        txHash: giveawayTxHash,\n        blockchain: blockchain\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenEVMGiveawayClaim.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AirTokenEVMGiveawayClaim, \"LwtVOPP3a6fNC2RsTsMCVvKcg9k=\", false, function() {\n    return [\n        _Root_services_tracking__WEBPACK_IMPORTED_MODULE_16__.useGtmTrack,\n        react_google_recaptcha_v3__WEBPACK_IMPORTED_MODULE_14__.useGoogleReCaptcha,\n        _Hooks_useGiveawayTxHash__WEBPACK_IMPORTED_MODULE_6__.useGiveawayTxHash,\n        next_i18next__WEBPACK_IMPORTED_MODULE_13__.useTranslation,\n        _useAirTokenGiveawayClaim__WEBPACK_IMPORTED_MODULE_11__.useAirTokenGiveawayClaim,\n        _hooks_useGetTokenWindowInfo__WEBPACK_IMPORTED_MODULE_10__.useGetTokenWindowInfo\n    ];\n});\n_c = AirTokenEVMGiveawayClaim;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AirTokenEVMGiveawayClaim);\nvar _c;\n$RefreshReg$(_c, \"AirTokenEVMGiveawayClaim\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/AirTokenGiveaway/AirTokenEVMGiveawayClaim.tsx\n"));

/***/ }),

/***/ "./components/Giveaways/AirTokenGiveaway/AirTokenGiveawayClaim.tsx":
/*!*************************************************************************!*\
  !*** ./components/Giveaways/AirTokenGiveaway/AirTokenGiveawayClaim.tsx ***!
  \*************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _AirTokenDotsamaGiveawayClaim__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AirTokenDotsamaGiveawayClaim */ \"./components/Giveaways/AirTokenGiveaway/AirTokenDotsamaGiveawayClaim.tsx\");\n/* harmony import */ var _AirTokenEVMGiveawayClaim__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AirTokenEVMGiveawayClaim */ \"./components/Giveaways/AirTokenGiveaway/AirTokenEVMGiveawayClaim.tsx\");\n\n\n\n\nconst AirTokenGiveawayClaim = (param)=>{\n    let { giveaway, projectEvent, blockchain, airToken, amount } = param;\n    if (!blockchain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    }\n    switch(blockchain.type){\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.BlockchainType.EVM:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AirTokenEVMGiveawayClaim__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                giveaway: giveaway,\n                projectEvent: projectEvent,\n                amount: amount,\n                blockchain: blockchain,\n                airToken: airToken\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenGiveawayClaim.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, undefined);\n        case _airlyft_types__WEBPACK_IMPORTED_MODULE_1__.BlockchainType.DOTSAMA:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AirTokenDotsamaGiveawayClaim__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                giveaway: giveaway,\n                projectEvent: projectEvent,\n                amount: amount,\n                blockchain: blockchain,\n                airToken: airToken\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenGiveawayClaim.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, undefined);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    }\n};\n_c = AirTokenGiveawayClaim;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AirTokenGiveawayClaim);\nvar _c;\n$RefreshReg$(_c, \"AirTokenGiveawayClaim\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/AirTokenGiveaway/AirTokenGiveawayClaim.tsx\n"));

/***/ }),

/***/ "./components/Giveaways/AirTokenGiveaway/AirTokenShopDetails.tsx":
/*!***********************************************************************!*\
  !*** ./components/Giveaways/AirTokenGiveaway/AirTokenShopDetails.tsx ***!
  \***********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AirTokenShopDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_Paragraph__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/Paragraph */ \"./components/Paragraph.tsx\");\n/* harmony import */ var _Components_Project_useProjectUserInfo_gql__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Project/useProjectUserInfo.gql */ \"./components/Project/useProjectUserInfo.gql.ts\");\n/* harmony import */ var _Hooks_useGetBlockchain__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Hooks/useGetBlockchain */ \"./hooks/useGetBlockchain.ts\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @phosphor-icons/react */ \"./node_modules/@phosphor-icons/react/dist/index.es.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ethers */ \"./node_modules/ethers/lib.esm/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_ShopGiveawayDetails__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/ShopGiveawayDetails */ \"./components/Giveaways/components/ShopGiveawayDetails.tsx\");\n/* harmony import */ var _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../hooks/useGetUserEventRewards */ \"./components/Giveaways/hooks/useGetUserEventRewards.ts\");\n/* harmony import */ var _AirTokenGiveawayClaim__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AirTokenGiveawayClaim */ \"./components/Giveaways/AirTokenGiveaway/AirTokenGiveawayClaim.tsx\");\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @airlyft/web3-evm-hooks */ \"../web3-evm-hooks/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction AirTokenShopDetails(param) {\n    let { giveaway, projectEvent, expandable, expanded, onClick } = param;\n    var _projectEvent_project, _data_projectUserInfo, _data_projectUserInfo1, _data_projectUserInfo2;\n    _s();\n    const giveawayInfo = giveaway.info;\n    const airToken = giveawayInfo === null || giveawayInfo === void 0 ? void 0 : giveawayInfo.airToken;\n    const shopConfig = giveawayInfo === null || giveawayInfo === void 0 ? void 0 : giveawayInfo.shopConfig;\n    const totalAmount = ethers__WEBPACK_IMPORTED_MODULE_10__.BigNumber.from((giveawayInfo === null || giveawayInfo === void 0 ? void 0 : giveawayInfo.amount) || -1);\n    const { data, loading: infoLoading } = (0,_Components_Project_useProjectUserInfo_gql__WEBPACK_IMPORTED_MODULE_2__.useProjectUserInfo)(projectEvent === null || projectEvent === void 0 ? void 0 : (_projectEvent_project = projectEvent.project) === null || _projectEvent_project === void 0 ? void 0 : _projectEvent_project.id);\n    const { data: blockchainData, loading: blockchainLoading } = (0,_Hooks_useGetBlockchain__WEBPACK_IMPORTED_MODULE_3__.useGetBlockchain)(airToken.blockchainId);\n    const blockchain = blockchainData === null || blockchainData === void 0 ? void 0 : blockchainData.blockchain;\n    const winnerAmount = ethers__WEBPACK_IMPORTED_MODULE_10__.BigNumber.from((shopConfig === null || shopConfig === void 0 ? void 0 : shopConfig.amount) || 0);\n    const { totalClaimable: totalAttemptedClaimable, claimed, totalClaimed: userClaimed, loading: statsLoading } = (0,_hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_6__.useGetUserEventRewardsStats)(projectEvent.id, giveaway.id, winnerAmount);\n    const hasSufficientPoints = (data === null || data === void 0 ? void 0 : (_data_projectUserInfo = data.projectUserInfo) === null || _data_projectUserInfo === void 0 ? void 0 : _data_projectUserInfo.fuel) && shopConfig.points && (data === null || data === void 0 ? void 0 : (_data_projectUserInfo1 = data.projectUserInfo) === null || _data_projectUserInfo1 === void 0 ? void 0 : _data_projectUserInfo1.fuel) >= shopConfig.points;\n    const { windowsClaimed, loading: windowsLoading } = (0,_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_8__.useGetBatchWindowClaimed)(blockchain, airToken.contractAddress || \"\", [\n        giveaway.id\n    ]);\n    const loading = statsLoading || blockchainLoading || infoLoading || windowsLoading;\n    const giveawayClaimed = ethers__WEBPACK_IMPORTED_MODULE_10__.BigNumber.from((windowsClaimed === null || windowsClaimed === void 0 ? void 0 : windowsClaimed[giveaway.id]) || 0);\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_9__.useTranslation)(\"translation\");\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_9__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ShopGiveawayDetails__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        loading: loading,\n        icon: airToken.icon || \"\",\n        token: airToken,\n        blockchain: blockchain,\n        totalAmount: totalAmount,\n        userClaimedAmount: userClaimed,\n        totalAttemptedClaimable: totalAttemptedClaimable,\n        giveawayClaimed: giveawayClaimed,\n        remainingLiquidity: totalAmount.sub(giveawayClaimed),\n        expandable: expandable,\n        expanded: expanded,\n        onClick: onClick,\n        reward: \"\".concat(airToken.name, \" (\").concat(airToken.ticker, \")\"),\n        claimed: claimed,\n        shopConfig: shopConfig,\n        userProjectPoints: (data === null || data === void 0 ? void 0 : (_data_projectUserInfo2 = data.projectUserInfo) === null || _data_projectUserInfo2 === void 0 ? void 0 : _data_projectUserInfo2.fuel) || undefined,\n        summary: {\n            giveawayType: giveaway.giveawayType\n        },\n        children: [\n            airToken.description ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Paragraph__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_11__.Flask, {\n                        weight: \"fill\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenShopDetails.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 19\n                    }, void 0),\n                    title: \"NFT Details\",\n                    description: airToken.description\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenShopDetails.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n            !loading && totalAttemptedClaimable.gt(0) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-500/10 border border-red-100 text-ch px-4 py-3 rounded relative space-x-2 text-left text-sm\",\n                role: \"alert\",\n                children: t(\"giveaway.airTokenPool.shopDetails.unsuccessfulAlert\", {\n                    projectPoints: globalT(\"projectPoints\")\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenShopDetails.tsx\",\n                lineNumber: 109,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n            blockchain && (!claimed && hasSufficientPoints || totalAttemptedClaimable.gt(0)) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AirTokenGiveawayClaim__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                giveaway: giveaway,\n                projectEvent: projectEvent,\n                amount: ethers__WEBPACK_IMPORTED_MODULE_10__.BigNumber.from(shopConfig === null || shopConfig === void 0 ? void 0 : shopConfig.amount),\n                blockchain: blockchain,\n                airToken: airToken\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenShopDetails.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenShopDetails.tsx\",\n                lineNumber: 131,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\AirTokenGiveaway\\\\AirTokenShopDetails.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\n_s(AirTokenShopDetails, \"VPeKk8kv8AhY9kkgM7Cq4sfdIVo=\", false, function() {\n    return [\n        _Components_Project_useProjectUserInfo_gql__WEBPACK_IMPORTED_MODULE_2__.useProjectUserInfo,\n        _Hooks_useGetBlockchain__WEBPACK_IMPORTED_MODULE_3__.useGetBlockchain,\n        _hooks_useGetUserEventRewards__WEBPACK_IMPORTED_MODULE_6__.useGetUserEventRewardsStats,\n        _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_8__.useGetBatchWindowClaimed,\n        next_i18next__WEBPACK_IMPORTED_MODULE_9__.useTranslation,\n        next_i18next__WEBPACK_IMPORTED_MODULE_9__.useTranslation\n    ];\n});\n_c = AirTokenShopDetails;\nvar _c;\n$RefreshReg$(_c, \"AirTokenShopDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0dpdmVhd2F5cy9BaXJUb2tlbkdpdmVhd2F5L0FpclRva2VuU2hvcERldGFpbHMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUE4QztBQUNrQztBQUNyQjtBQU9iO0FBQ1g7QUFDRjtBQUNtQztBQUNVO0FBQ2xCO0FBQ087QUFDckI7QUFFL0IsU0FBU1csb0JBQW9CLEtBWTNDO1FBWjJDLEVBQzFDQyxRQUFRLEVBQ1JDLFlBQVksRUFDWkMsVUFBVSxFQUNWQyxRQUFRLEVBQ1JDLE9BQU8sRUFPUixHQVoyQztRQW1CeENILHVCQWtCQUksdUJBRUFBLHdCQWlDcUJBOztJQTNEdkIsTUFBTUMsZUFBZU4sU0FBU08sSUFBSTtJQUNsQyxNQUFNQyxXQUFXRix5QkFBQUEsbUNBQUFBLGFBQWNFLFFBQVE7SUFDdkMsTUFBTUMsYUFBYUgseUJBQUFBLG1DQUFBQSxhQUFjRyxVQUFVO0lBQzNDLE1BQU1DLGNBQWNsQiw4Q0FBU0EsQ0FBQ21CLElBQUksQ0FBQ0wsQ0FBQUEseUJBQUFBLG1DQUFBQSxhQUFjTSxNQUFNLEtBQUksQ0FBQztJQUU1RCxNQUFNLEVBQUVQLElBQUksRUFBRVEsU0FBU0MsV0FBVyxFQUFFLEdBQUd6Qiw4RkFBa0JBLENBQ3ZEWSx5QkFBQUEsb0NBQUFBLHdCQUFBQSxhQUFjYyxPQUFPLGNBQXJCZCw0Q0FBQUEsc0JBQXVCZSxFQUFFO0lBRzNCLE1BQU0sRUFBRVgsTUFBTVksY0FBYyxFQUFFSixTQUFTSyxpQkFBaUIsRUFBRSxHQUFHNUIseUVBQWdCQSxDQUMzRWtCLFNBQVNXLFlBQVk7SUFHdkIsTUFBTUMsYUFBYUgsMkJBQUFBLHFDQUFBQSxlQUFnQkcsVUFBVTtJQUU3QyxNQUFNQyxlQUFlN0IsOENBQVNBLENBQUNtQixJQUFJLENBQUNGLENBQUFBLHVCQUFBQSxpQ0FBQUEsV0FBWUcsTUFBTSxLQUFJO0lBQzFELE1BQU0sRUFDSlUsZ0JBQWdCQyx1QkFBdUIsRUFDdkNDLE9BQU8sRUFDUEMsY0FBY0MsV0FBVyxFQUN6QmIsU0FBU2MsWUFBWSxFQUN0QixHQUFHaEMsMEZBQTJCQSxDQUFDTSxhQUFhZSxFQUFFLEVBQUVoQixTQUFTZ0IsRUFBRSxFQUFFSztJQUU5RCxNQUFNTyxzQkFDSnZCLENBQUFBLGlCQUFBQSw0QkFBQUEsd0JBQUFBLEtBQU13QixlQUFlLGNBQXJCeEIsNENBQUFBLHNCQUF1QnlCLElBQUksS0FDM0JyQixXQUFXc0IsTUFBTSxJQUNqQjFCLENBQUFBLGlCQUFBQSw0QkFBQUEseUJBQUFBLEtBQU13QixlQUFlLGNBQXJCeEIsNkNBQUFBLHVCQUF1QnlCLElBQUksS0FBSXJCLFdBQVdzQixNQUFNO0lBRWxELE1BQU0sRUFBRUMsY0FBYyxFQUFFbkIsU0FBU29CLGNBQWMsRUFBRSxHQUFHcEMsaUZBQXdCQSxDQUMxRXVCLFlBQ0FaLFNBQVMwQixlQUFlLElBQUksSUFDNUI7UUFBQ2xDLFNBQVNnQixFQUFFO0tBQUM7SUFHZixNQUFNSCxVQUNKYyxnQkFBZ0JULHFCQUFxQkosZUFBZW1CO0lBRXRELE1BQU1FLGtCQUFrQjNDLDhDQUFTQSxDQUFDbUIsSUFBSSxDQUFDcUIsQ0FBQUEsMkJBQUFBLHFDQUFBQSxjQUFnQixDQUFDaEMsU0FBU2dCLEVBQUUsQ0FBQyxLQUFJO0lBRXhFLE1BQU0sRUFBQ29CLENBQUMsRUFBQyxHQUFHdEMsNERBQWNBLENBQUM7SUFDM0IsTUFBTSxFQUFFc0MsR0FBR0MsT0FBTyxFQUFFLEdBQUd2Qyw0REFBY0EsQ0FBQyxlQUFlO1FBQUV3QyxXQUFXO0lBQVM7SUFFM0UscUJBQ0UsOERBQUM1Qyx1RUFBbUJBO1FBQ2xCbUIsU0FBU0E7UUFDVDBCLE1BQU0vQixTQUFTK0IsSUFBSSxJQUFJO1FBQ3ZCQyxPQUFPaEM7UUFDUFksWUFBWUE7UUFDWlYsYUFBYUE7UUFDYitCLG1CQUFtQmY7UUFDbkJILHlCQUF5QkE7UUFDekJZLGlCQUFpQkE7UUFDakJPLG9CQUFvQmhDLFlBQVlpQyxHQUFHLENBQUNSO1FBQ3BDakMsWUFBWUE7UUFDWkMsVUFBVUE7UUFDVkMsU0FBU0E7UUFDVHdDLFFBQVEsR0FBcUJwQyxPQUFsQkEsU0FBU3FDLElBQUksRUFBQyxNQUFvQixPQUFoQnJDLFNBQVNzQyxNQUFNLEVBQUM7UUFDN0N0QixTQUFTQTtRQUNUZixZQUFZQTtRQUNac0MsbUJBQW1CMUMsQ0FBQUEsaUJBQUFBLDRCQUFBQSx5QkFBQUEsS0FBTXdCLGVBQWUsY0FBckJ4Qiw2Q0FBQUEsdUJBQXVCeUIsSUFBSSxLQUFJa0I7UUFDbERDLFNBQVM7WUFDUEMsY0FBY2xELFNBQVNrRCxZQUFZO1FBQ3JDOztZQUVDMUMsU0FBUzJDLFdBQVcsaUJBQ25COzBCQUNFLDRFQUFDL0QsNkRBQVNBO29CQUNSbUQsb0JBQU0sOERBQUNoRCx5REFBS0E7d0JBQUM2RCxRQUFPOzs7Ozs7b0JBQ3BCQyxPQUFPO29CQUNQRixhQUFhM0MsU0FBUzJDLFdBQVc7Ozs7Ozs4Q0FJckM7WUFHRCxDQUFDdEMsV0FBV1Usd0JBQXdCK0IsRUFBRSxDQUFDLG1CQUN0Qyw4REFBQ0M7Z0JBQ0NDLFdBQVU7Z0JBQ1ZDLE1BQUs7MEJBRUpyQixFQUFFLHVEQUF1RDtvQkFDeERzQixlQUFlckIsUUFBUTtnQkFDekI7Ozs7O3FDQUdGO1lBR0RqQixjQUNBLEVBQUVJLFdBQVdJLHVCQUF3Qkwsd0JBQXdCK0IsRUFBRSxDQUFDLEVBQUMsa0JBQ2hFLDhEQUFDMUQsOERBQXFCQTtnQkFDcEJJLFVBQVVBO2dCQUNWQyxjQUFjQTtnQkFDZFcsUUFBUXBCLDhDQUFTQSxDQUFDbUIsSUFBSSxDQUFDRix1QkFBQUEsaUNBQUFBLFdBQVlHLE1BQU07Z0JBQ3pDUSxZQUFZQTtnQkFDWlosVUFBVUE7Ozs7O3FDQUdaLDhEQUFDZiwyQ0FBUUE7Ozs7Ozs7Ozs7O0FBSWpCO0dBcEh3Qk07O1FBa0JpQlYsMEZBQWtCQTtRQUlJQyxxRUFBZ0JBO1FBWXpFSyxzRkFBMkJBO1FBT3FCRSw2RUFBd0JBO1FBV2hFQyx3REFBY0E7UUFDSEEsd0RBQWNBOzs7S0FyRGZDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2NvbXBvbmVudHMvR2l2ZWF3YXlzL0FpclRva2VuR2l2ZWF3YXkvQWlyVG9rZW5TaG9wRGV0YWlscy50c3g/Zjk4MyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUGFyYWdyYXBoIGZyb20gJ0BDb21wb25lbnRzL1BhcmFncmFwaCc7XHJcbmltcG9ydCB7IHVzZVByb2plY3RVc2VySW5mbyB9IGZyb20gJ0BDb21wb25lbnRzL1Byb2plY3QvdXNlUHJvamVjdFVzZXJJbmZvLmdxbCc7XHJcbmltcG9ydCB7IHVzZUdldEJsb2NrY2hhaW4gfSBmcm9tICdASG9va3MvdXNlR2V0QmxvY2tjaGFpbic7XHJcbmltcG9ydCB7XHJcbiAgQWlyVG9rZW5HaXZlYXdheURhdGEsXHJcbiAgR2l2ZWF3YXksXHJcbiAgUHJvamVjdEV2ZW50LFxyXG4gIFNob3BDb25maWcsXHJcbn0gZnJvbSAnQGFpcmx5ZnQvdHlwZXMnO1xyXG5pbXBvcnQgeyBGbGFzayB9IGZyb20gJ0BwaG9zcGhvci1pY29ucy9yZWFjdCc7XHJcbmltcG9ydCB7IEJpZ051bWJlciB9IGZyb20gJ2V0aGVycyc7XHJcbmltcG9ydCB7IEZyYWdtZW50IH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgU2hvcEdpdmVhd2F5RGV0YWlscyBmcm9tICcuLi9jb21wb25lbnRzL1Nob3BHaXZlYXdheURldGFpbHMnO1xyXG5pbXBvcnQgeyB1c2VHZXRVc2VyRXZlbnRSZXdhcmRzU3RhdHMgfSBmcm9tICcuLi9ob29rcy91c2VHZXRVc2VyRXZlbnRSZXdhcmRzJztcclxuaW1wb3J0IEFpclRva2VuR2l2ZWF3YXlDbGFpbSBmcm9tICcuL0FpclRva2VuR2l2ZWF3YXlDbGFpbSc7XHJcbmltcG9ydCB7IHVzZUdldEJhdGNoV2luZG93Q2xhaW1lZCB9IGZyb20gJ0BhaXJseWZ0L3dlYjMtZXZtLWhvb2tzJztcclxuaW1wb3J0IHsgdXNlVHJhbnNsYXRpb24gfSBmcm9tICduZXh0LWkxOG5leHQnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQWlyVG9rZW5TaG9wRGV0YWlscyh7XHJcbiAgZ2l2ZWF3YXksXHJcbiAgcHJvamVjdEV2ZW50LFxyXG4gIGV4cGFuZGFibGUsXHJcbiAgZXhwYW5kZWQsXHJcbiAgb25DbGljayxcclxufToge1xyXG4gIGdpdmVhd2F5OiBHaXZlYXdheTtcclxuICBwcm9qZWN0RXZlbnQ6IFByb2plY3RFdmVudDtcclxuICBleHBhbmRhYmxlPzogYm9vbGVhbjtcclxuICBleHBhbmRlZD86IGJvb2xlYW47XHJcbiAgb25DbGljaz86ICgpID0+IHZvaWQ7XHJcbn0pIHtcclxuICBjb25zdCBnaXZlYXdheUluZm8gPSBnaXZlYXdheS5pbmZvIGFzIEFpclRva2VuR2l2ZWF3YXlEYXRhO1xyXG4gIGNvbnN0IGFpclRva2VuID0gZ2l2ZWF3YXlJbmZvPy5haXJUb2tlbjtcclxuICBjb25zdCBzaG9wQ29uZmlnID0gZ2l2ZWF3YXlJbmZvPy5zaG9wQ29uZmlnIGFzIFNob3BDb25maWc7XHJcbiAgY29uc3QgdG90YWxBbW91bnQgPSBCaWdOdW1iZXIuZnJvbShnaXZlYXdheUluZm8/LmFtb3VudCB8fCAtMSk7XHJcblxyXG4gIGNvbnN0IHsgZGF0YSwgbG9hZGluZzogaW5mb0xvYWRpbmcgfSA9IHVzZVByb2plY3RVc2VySW5mbyhcclxuICAgIHByb2plY3RFdmVudD8ucHJvamVjdD8uaWQsXHJcbiAgKTtcclxuXHJcbiAgY29uc3QgeyBkYXRhOiBibG9ja2NoYWluRGF0YSwgbG9hZGluZzogYmxvY2tjaGFpbkxvYWRpbmcgfSA9IHVzZUdldEJsb2NrY2hhaW4oXHJcbiAgICBhaXJUb2tlbi5ibG9ja2NoYWluSWQsXHJcbiAgKTtcclxuXHJcbiAgY29uc3QgYmxvY2tjaGFpbiA9IGJsb2NrY2hhaW5EYXRhPy5ibG9ja2NoYWluO1xyXG5cclxuICBjb25zdCB3aW5uZXJBbW91bnQgPSBCaWdOdW1iZXIuZnJvbShzaG9wQ29uZmlnPy5hbW91bnQgfHwgMCk7XHJcbiAgY29uc3Qge1xyXG4gICAgdG90YWxDbGFpbWFibGU6IHRvdGFsQXR0ZW1wdGVkQ2xhaW1hYmxlLFxyXG4gICAgY2xhaW1lZCxcclxuICAgIHRvdGFsQ2xhaW1lZDogdXNlckNsYWltZWQsXHJcbiAgICBsb2FkaW5nOiBzdGF0c0xvYWRpbmcsXHJcbiAgfSA9IHVzZUdldFVzZXJFdmVudFJld2FyZHNTdGF0cyhwcm9qZWN0RXZlbnQuaWQsIGdpdmVhd2F5LmlkLCB3aW5uZXJBbW91bnQpO1xyXG5cclxuICBjb25zdCBoYXNTdWZmaWNpZW50UG9pbnRzID1cclxuICAgIGRhdGE/LnByb2plY3RVc2VySW5mbz8uZnVlbCAmJlxyXG4gICAgc2hvcENvbmZpZy5wb2ludHMgJiZcclxuICAgIGRhdGE/LnByb2plY3RVc2VySW5mbz8uZnVlbCA+PSBzaG9wQ29uZmlnLnBvaW50cztcclxuXHJcbiAgY29uc3QgeyB3aW5kb3dzQ2xhaW1lZCwgbG9hZGluZzogd2luZG93c0xvYWRpbmcgfSA9IHVzZUdldEJhdGNoV2luZG93Q2xhaW1lZChcclxuICAgIGJsb2NrY2hhaW4sXHJcbiAgICBhaXJUb2tlbi5jb250cmFjdEFkZHJlc3MgfHwgJycsXHJcbiAgICBbZ2l2ZWF3YXkuaWRdLFxyXG4gICk7XHJcblxyXG4gIGNvbnN0IGxvYWRpbmcgPVxyXG4gICAgc3RhdHNMb2FkaW5nIHx8IGJsb2NrY2hhaW5Mb2FkaW5nIHx8IGluZm9Mb2FkaW5nIHx8IHdpbmRvd3NMb2FkaW5nO1xyXG5cclxuICBjb25zdCBnaXZlYXdheUNsYWltZWQgPSBCaWdOdW1iZXIuZnJvbSh3aW5kb3dzQ2xhaW1lZD8uW2dpdmVhd2F5LmlkXSB8fCAwKTtcclxuXHJcbiAgY29uc3Qge3R9ID0gdXNlVHJhbnNsYXRpb24oJ3RyYW5zbGF0aW9uJyk7XHJcbiAgY29uc3QgeyB0OiBnbG9iYWxUIH0gPSB1c2VUcmFuc2xhdGlvbigndHJhbnNsYXRpb24nLCB7IGtleVByZWZpeDogJ2dsb2JhbCcgfSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8U2hvcEdpdmVhd2F5RGV0YWlsc1xyXG4gICAgICBsb2FkaW5nPXtsb2FkaW5nfVxyXG4gICAgICBpY29uPXthaXJUb2tlbi5pY29uIHx8ICcnfVxyXG4gICAgICB0b2tlbj17YWlyVG9rZW59XHJcbiAgICAgIGJsb2NrY2hhaW49e2Jsb2NrY2hhaW59XHJcbiAgICAgIHRvdGFsQW1vdW50PXt0b3RhbEFtb3VudH1cclxuICAgICAgdXNlckNsYWltZWRBbW91bnQ9e3VzZXJDbGFpbWVkfVxyXG4gICAgICB0b3RhbEF0dGVtcHRlZENsYWltYWJsZT17dG90YWxBdHRlbXB0ZWRDbGFpbWFibGV9XHJcbiAgICAgIGdpdmVhd2F5Q2xhaW1lZD17Z2l2ZWF3YXlDbGFpbWVkfVxyXG4gICAgICByZW1haW5pbmdMaXF1aWRpdHk9e3RvdGFsQW1vdW50LnN1YihnaXZlYXdheUNsYWltZWQpfVxyXG4gICAgICBleHBhbmRhYmxlPXtleHBhbmRhYmxlfVxyXG4gICAgICBleHBhbmRlZD17ZXhwYW5kZWR9XHJcbiAgICAgIG9uQ2xpY2s9e29uQ2xpY2t9XHJcbiAgICAgIHJld2FyZD17YCR7YWlyVG9rZW4ubmFtZX0gKCR7YWlyVG9rZW4udGlja2VyfSlgfVxyXG4gICAgICBjbGFpbWVkPXtjbGFpbWVkfVxyXG4gICAgICBzaG9wQ29uZmlnPXtzaG9wQ29uZmlnfVxyXG4gICAgICB1c2VyUHJvamVjdFBvaW50cz17ZGF0YT8ucHJvamVjdFVzZXJJbmZvPy5mdWVsIHx8IHVuZGVmaW5lZH1cclxuICAgICAgc3VtbWFyeT17e1xyXG4gICAgICAgIGdpdmVhd2F5VHlwZTogZ2l2ZWF3YXkuZ2l2ZWF3YXlUeXBlLFxyXG4gICAgICB9fVxyXG4gICAgPlxyXG4gICAgICB7YWlyVG9rZW4uZGVzY3JpcHRpb24gPyAoXHJcbiAgICAgICAgPD5cclxuICAgICAgICAgIDxQYXJhZ3JhcGhcclxuICAgICAgICAgICAgaWNvbj17PEZsYXNrIHdlaWdodD1cImZpbGxcIiAvPn1cclxuICAgICAgICAgICAgdGl0bGU9eydORlQgRGV0YWlscyd9XHJcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uPXthaXJUb2tlbi5kZXNjcmlwdGlvbn1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgPC8+XHJcbiAgICAgICkgOiAoXHJcbiAgICAgICAgPD48Lz5cclxuICAgICAgKX1cclxuXHJcbiAgICAgIHshbG9hZGluZyAmJiB0b3RhbEF0dGVtcHRlZENsYWltYWJsZS5ndCgwKSA/IChcclxuICAgICAgICA8ZGl2XHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJiZy1yZWQtNTAwLzEwIGJvcmRlciBib3JkZXItcmVkLTEwMCB0ZXh0LWNoIHB4LTQgcHktMyByb3VuZGVkIHJlbGF0aXZlIHNwYWNlLXgtMiB0ZXh0LWxlZnQgdGV4dC1zbVwiXHJcbiAgICAgICAgICByb2xlPVwiYWxlcnRcIlxyXG4gICAgICAgID5cclxuICAgICAgICAgIHt0KCdnaXZlYXdheS5haXJUb2tlblBvb2wuc2hvcERldGFpbHMudW5zdWNjZXNzZnVsQWxlcnQnLCB7XHJcbiAgICAgICAgICAgIHByb2plY3RQb2ludHM6IGdsb2JhbFQoJ3Byb2plY3RQb2ludHMnKSxcclxuICAgICAgICAgIH0pfVxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApIDogKFxyXG4gICAgICAgIDw+PC8+XHJcbiAgICAgICl9XHJcblxyXG4gICAgICB7YmxvY2tjaGFpbiAmJlxyXG4gICAgICAoKCFjbGFpbWVkICYmIGhhc1N1ZmZpY2llbnRQb2ludHMpIHx8IHRvdGFsQXR0ZW1wdGVkQ2xhaW1hYmxlLmd0KDApKSA/IChcclxuICAgICAgICA8QWlyVG9rZW5HaXZlYXdheUNsYWltXHJcbiAgICAgICAgICBnaXZlYXdheT17Z2l2ZWF3YXl9XHJcbiAgICAgICAgICBwcm9qZWN0RXZlbnQ9e3Byb2plY3RFdmVudH1cclxuICAgICAgICAgIGFtb3VudD17QmlnTnVtYmVyLmZyb20oc2hvcENvbmZpZz8uYW1vdW50KX1cclxuICAgICAgICAgIGJsb2NrY2hhaW49e2Jsb2NrY2hhaW59XHJcbiAgICAgICAgICBhaXJUb2tlbj17YWlyVG9rZW59XHJcbiAgICAgICAgLz5cclxuICAgICAgKSA6IChcclxuICAgICAgICA8RnJhZ21lbnQgLz5cclxuICAgICAgKX1cclxuICAgIDwvU2hvcEdpdmVhd2F5RGV0YWlscz5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJQYXJhZ3JhcGgiLCJ1c2VQcm9qZWN0VXNlckluZm8iLCJ1c2VHZXRCbG9ja2NoYWluIiwiRmxhc2siLCJCaWdOdW1iZXIiLCJGcmFnbWVudCIsIlNob3BHaXZlYXdheURldGFpbHMiLCJ1c2VHZXRVc2VyRXZlbnRSZXdhcmRzU3RhdHMiLCJBaXJUb2tlbkdpdmVhd2F5Q2xhaW0iLCJ1c2VHZXRCYXRjaFdpbmRvd0NsYWltZWQiLCJ1c2VUcmFuc2xhdGlvbiIsIkFpclRva2VuU2hvcERldGFpbHMiLCJnaXZlYXdheSIsInByb2plY3RFdmVudCIsImV4cGFuZGFibGUiLCJleHBhbmRlZCIsIm9uQ2xpY2siLCJkYXRhIiwiZ2l2ZWF3YXlJbmZvIiwiaW5mbyIsImFpclRva2VuIiwic2hvcENvbmZpZyIsInRvdGFsQW1vdW50IiwiZnJvbSIsImFtb3VudCIsImxvYWRpbmciLCJpbmZvTG9hZGluZyIsInByb2plY3QiLCJpZCIsImJsb2NrY2hhaW5EYXRhIiwiYmxvY2tjaGFpbkxvYWRpbmciLCJibG9ja2NoYWluSWQiLCJibG9ja2NoYWluIiwid2lubmVyQW1vdW50IiwidG90YWxDbGFpbWFibGUiLCJ0b3RhbEF0dGVtcHRlZENsYWltYWJsZSIsImNsYWltZWQiLCJ0b3RhbENsYWltZWQiLCJ1c2VyQ2xhaW1lZCIsInN0YXRzTG9hZGluZyIsImhhc1N1ZmZpY2llbnRQb2ludHMiLCJwcm9qZWN0VXNlckluZm8iLCJmdWVsIiwicG9pbnRzIiwid2luZG93c0NsYWltZWQiLCJ3aW5kb3dzTG9hZGluZyIsImNvbnRyYWN0QWRkcmVzcyIsImdpdmVhd2F5Q2xhaW1lZCIsInQiLCJnbG9iYWxUIiwia2V5UHJlZml4IiwiaWNvbiIsInRva2VuIiwidXNlckNsYWltZWRBbW91bnQiLCJyZW1haW5pbmdMaXF1aWRpdHkiLCJzdWIiLCJyZXdhcmQiLCJuYW1lIiwidGlja2VyIiwidXNlclByb2plY3RQb2ludHMiLCJ1bmRlZmluZWQiLCJzdW1tYXJ5IiwiZ2l2ZWF3YXlUeXBlIiwiZGVzY3JpcHRpb24iLCJ3ZWlnaHQiLCJ0aXRsZSIsImd0IiwiZGl2IiwiY2xhc3NOYW1lIiwicm9sZSIsInByb2plY3RQb2ludHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Giveaways/AirTokenGiveaway/AirTokenShopDetails.tsx\n"));

/***/ }),

/***/ "./components/Giveaways/AirTokenGiveaway/useAirTokenGiveawayClaim.tsx":
/*!****************************************************************************!*\
  !*** ./components/Giveaways/AirTokenGiveaway/useAirTokenGiveawayClaim.tsx ***!
  \****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAirTokenGiveawayClaim: function() { return /* binding */ useAirTokenGiveawayClaim; }\n/* harmony export */ });\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Hooks_useGiveawayTxHash__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Hooks/useGiveawayTxHash */ \"./hooks/useGiveawayTxHash.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _hooks_useGetContract__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/useGetContract */ \"./components/Giveaways/hooks/useGetContract.ts\");\n/* harmony import */ var _airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./airtoken-giveaway.gql */ \"./components/Giveaways/AirTokenGiveaway/airtoken-giveaway.gql.ts\");\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst claimERC721AirToken = (contractAddress, connectorData, rewardCertificate, token)=>{\n    const { provider, account } = connectorData;\n    const contract = _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ERC721AirbaseController__factory.connect(contractAddress, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.getSigner)(provider, account));\n    const { certificate, window, windowLimit, amount, claimIds } = rewardCertificate;\n    if (token.external) {\n        if (claimIds.length === 1) {\n            return contract.claimExternal(account, claimIds[0], token.address, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatCertificate)(certificate));\n        }\n        return contract.claimExternalBatch(account, claimIds, token.address, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatCertificate)(certificate));\n    }\n    if (claimIds.length === 1) {\n        return contract.claim(account, claimIds[0], token.address, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatCertificate)(certificate));\n    }\n    return contract.claimBatch(account, claimIds, token.address, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatCertificate)(certificate));\n};\nconst claimERC1155AirToken = (contractAddress, connectorData, rewardCertificate, token)=>{\n    const { provider, account } = connectorData;\n    const contract = _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ERC1155AirbaseController__factory.connect(contractAddress, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.getSigner)(provider, account));\n    const { certificate, window, windowLimit, amount, claimIds } = rewardCertificate;\n    if (claimIds.length === 1) {\n        return contract.claim(account, claimIds[0], token.address, token.tokenId, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatCertificate)(certificate));\n    }\n    return contract.claimBatch(account, claimIds, token.address, token.tokenId, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatCertificate)(certificate));\n};\nconst claimERC20AirToken = (contractAddress, connectorData, rewardCertificate, token)=>{\n    const { provider, account } = connectorData;\n    const contract = _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ERC20AirbaseController__factory.connect(contractAddress, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.getSigner)(provider, account));\n    const { certificate, window, windowLimit, amount, claimIds } = rewardCertificate;\n    if (claimIds.length === 1) {\n        return contract.claim(account, claimIds[0], token.address, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatCertificate)(certificate));\n    }\n    return contract.claimBatch(account, claimIds, token.address, amount, window, windowLimit, (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.formatCertificate)(certificate));\n};\nfunction useAirTokenGiveawayClaim(projectId, projectEventId, giveawayId, blockchain, airToken) {\n    var _contractData_contract;\n    _s();\n    const { update } = (0,_Hooks_useGiveawayTxHash__WEBPACK_IMPORTED_MODULE_2__.useGiveawayTxHash)(giveawayId);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [getAirTokenRewardCertificate] = (0,_airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__.useGetAirTokenRewardCertificate)();\n    const [sync] = (0,_airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__.useSyncClaimAirTokenGiveaway)();\n    const assetType = airToken === null || airToken === void 0 ? void 0 : airToken.assetType;\n    const contractType = assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_0__.AssetType.ERC1155 ? _airlyft_types__WEBPACK_IMPORTED_MODULE_0__.ContractType.ERC1155_AIRBASE : assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_0__.AssetType.ERC20 ? _airlyft_types__WEBPACK_IMPORTED_MODULE_0__.ContractType.ERC20_AIRBASE : assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_0__.AssetType.ERC721 ? _airlyft_types__WEBPACK_IMPORTED_MODULE_0__.ContractType.ERC721_AIRBASE : undefined;\n    const { data: contractData, loading: contractLoading } = (0,_hooks_useGetContract__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(blockchain === null || blockchain === void 0 ? void 0 : blockchain.id, contractType);\n    const contractAddress = contractData === null || contractData === void 0 ? void 0 : (_contractData_contract = contractData.contract) === null || _contractData_contract === void 0 ? void 0 : _contractData_contract.address;\n    const mountedRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        mountedRef.current = true;\n        return ()=>{\n            mountedRef.current = false;\n        };\n    }, []);\n    const claim = async (param)=>{\n        let { connectorData, onError, onSuccess, gasLess = false, captcha } = param;\n        const { provider, account } = connectorData;\n        if (!gasLess && !provider || !account || !contractAddress || !airToken) return;\n        mountedRef.current && setLoading(true);\n        try {\n            const { data } = await getAirTokenRewardCertificate({\n                variables: {\n                    projectId,\n                    eventId: projectEventId,\n                    giveawayId,\n                    userAddress: account,\n                    captcha\n                },\n                context: {\n                    gasLess\n                }\n            });\n            if (gasLess) {\n                var _data_claimAirTokenGiveaway;\n                const txHash = data === null || data === void 0 ? void 0 : (_data_claimAirTokenGiveaway = data.claimAirTokenGiveaway) === null || _data_claimAirTokenGiveaway === void 0 ? void 0 : _data_claimAirTokenGiveaway.txHash;\n                if (!txHash) throw new Error(\"Claim Failed\");\n                update(txHash);\n                onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                    contractAddress,\n                    txHash\n                });\n            } else {\n                const result = data === null || data === void 0 ? void 0 : data.claimAirTokenGiveaway;\n                if (!result || !result.certificate) throw new Error(\"Invalid certificate\");\n                let tx;\n                if (airToken.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_0__.AssetType.ERC20) {\n                    tx = await claimERC20AirToken(contractAddress, connectorData, result, airToken);\n                } else if (airToken.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_0__.AssetType.ERC1155) {\n                    tx = await claimERC1155AirToken(contractAddress, connectorData, result, airToken);\n                } else if (airToken.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_0__.AssetType.ERC721) {\n                    tx = await claimERC721AirToken(contractAddress, connectorData, result, airToken);\n                } else {\n                    throw new Error(\"Invalid asset\");\n                }\n                update(tx.hash);\n                await tx.wait();\n                await sync({\n                    variables: {\n                        ids: result.raw.map((item)=>item.id),\n                        giveawayId: giveawayId\n                    },\n                    context: {\n                        eventId: projectEventId\n                    }\n                });\n                onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                    contractAddress,\n                    tx\n                });\n            }\n        } catch (err) {\n            let error;\n            if ((err === null || err === void 0 ? void 0 : err.code) === \"ACTION_REJECTED\") {\n                error = new Error(\"Tx Signature: User denied transaction signature.\");\n            }\n            onError === null || onError === void 0 ? void 0 : onError(error || err);\n        } finally{\n            mountedRef.current && setLoading(false);\n        }\n    };\n    return {\n        claim,\n        loading: loading || contractLoading\n    };\n}\n_s(useAirTokenGiveawayClaim, \"nSMOIUOYDquGWw1o++l1W626TJc=\", false, function() {\n    return [\n        _Hooks_useGiveawayTxHash__WEBPACK_IMPORTED_MODULE_2__.useGiveawayTxHash,\n        _airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__.useGetAirTokenRewardCertificate,\n        _airtoken_giveaway_gql__WEBPACK_IMPORTED_MODULE_5__.useSyncClaimAirTokenGiveaway,\n        _hooks_useGetContract__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/AirTokenGiveaway/useAirTokenGiveawayClaim.tsx\n"));

/***/ }),

/***/ "./components/Giveaways/GiveawayTransactionHash.tsx":
/*!**********************************************************!*\
  !*** ./components/Giveaways/GiveawayTransactionHash.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_TransactionResult__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/TransactionResult */ \"./components/TransactionResult.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst GiveawayTransactionHash = (param)=>{\n    let { txHash, blockchain } = param;\n    if (!txHash) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Fragment, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayTransactionHash.tsx\",\n        lineNumber: 12,\n        columnNumber: 23\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"!my-0 flex justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TransactionResult__WEBPACK_IMPORTED_MODULE_1__.TransactionResult, {\n            txHash: txHash,\n            blockchain: blockchain\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayTransactionHash.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\GiveawayTransactionHash.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined);\n};\n_c = GiveawayTransactionHash;\n/* harmony default export */ __webpack_exports__[\"default\"] = (GiveawayTransactionHash);\nvar _c;\n$RefreshReg$(_c, \"GiveawayTransactionHash\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0dpdmVhd2F5cy9HaXZlYXdheVRyYW5zYWN0aW9uSGFzaC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUNrRTtBQUNqQztBQUVqQyxNQUFNRSwwQkFBMEI7UUFBQyxFQUMvQkMsTUFBTSxFQUNOQyxVQUFVLEVBSVg7SUFDQyxJQUFJLENBQUNELFFBQVEscUJBQU8sOERBQUNGLDJDQUFRQTs7Ozs7SUFDN0IscUJBQ0UsOERBQUNJO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNOLDRFQUFpQkE7WUFBQ0csUUFBUUE7WUFBUUMsWUFBWUE7Ozs7Ozs7Ozs7O0FBR3JEO0tBYk1GO0FBZU4sK0RBQWVBLHVCQUF1QkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL0dpdmVhd2F5cy9HaXZlYXdheVRyYW5zYWN0aW9uSGFzaC50c3g/ODZjZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBCbG9ja2NoYWluIH0gZnJvbSAnQGFpcmx5ZnQvdHlwZXMnO1xyXG5pbXBvcnQgeyBUcmFuc2FjdGlvblJlc3VsdCB9IGZyb20gJ0BDb21wb25lbnRzL1RyYW5zYWN0aW9uUmVzdWx0JztcclxuaW1wb3J0IHsgRnJhZ21lbnQgfSBmcm9tICdyZWFjdCc7XHJcblxyXG5jb25zdCBHaXZlYXdheVRyYW5zYWN0aW9uSGFzaCA9ICh7XHJcbiAgdHhIYXNoLFxyXG4gIGJsb2NrY2hhaW4sXHJcbn06IHtcclxuICB0eEhhc2g/OiBzdHJpbmcgfCBudWxsO1xyXG4gIGJsb2NrY2hhaW46IEJsb2NrY2hhaW47XHJcbn0pID0+IHtcclxuICBpZiAoIXR4SGFzaCkgcmV0dXJuIDxGcmFnbWVudCAvPjtcclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCIhbXktMCBmbGV4IGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgIDxUcmFuc2FjdGlvblJlc3VsdCB0eEhhc2g9e3R4SGFzaH0gYmxvY2tjaGFpbj17YmxvY2tjaGFpbn0gLz5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBHaXZlYXdheVRyYW5zYWN0aW9uSGFzaDtcclxuIl0sIm5hbWVzIjpbIlRyYW5zYWN0aW9uUmVzdWx0IiwiRnJhZ21lbnQiLCJHaXZlYXdheVRyYW5zYWN0aW9uSGFzaCIsInR4SGFzaCIsImJsb2NrY2hhaW4iLCJkaXYiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Giveaways/GiveawayTransactionHash.tsx\n"));

/***/ }),

/***/ "./components/Giveaways/components/GiveawaySummaryItem.tsx":
/*!*****************************************************************!*\
  !*** ./components/Giveaways/components/GiveawaySummaryItem.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GiveawaySummaryItem; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @phosphor-icons/react */ \"./node_modules/@phosphor-icons/react/dist/index.es.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nfunction GiveawaySummaryItem(param) {\n    let { banner, children, bannerTag, onClick, size = \"default\", actionText, className, actionSuccess } = param;\n    const isVideo = (banner === null || banner === void 0 ? void 0 : banner.endsWith(\".mp4\")) || (banner === null || banner === void 0 ? void 0 : banner.endsWith(\".webm\")) || (banner === null || banner === void 0 ? void 0 : banner.endsWith(\".mov\"));\n    const isGif = banner === null || banner === void 0 ? void 0 : banner.endsWith(\".gif\");\n    if (size === \"small\" || size == \"default\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative gap-4 grid grid-cols-[120px_1fr] items-center \".concat(size === \"default\" ? \"sm:grid-cols-[170px_1fr]\" : \"\", \" \").concat(className || \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        isVideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                            autoPlay: true,\n                            playsInline: true,\n                            loop: true,\n                            muted: true,\n                            className: \"object-cover aspect-square rounded-xl absolute top-0 left-0 w-full h-full blur-[100px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: banner,\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            height: 200,\n                            width: 200,\n                            src: banner,\n                            className: \"object-cover aspect-square rounded-xl absolute top-0 left-0 w-full h-full blur-[100px]\",\n                            alt: \"giveaway-image\",\n                            unoptimized: isGif\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10 max-w-sm m-auto\",\n                            children: [\n                                isVideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                    autoPlay: true,\n                                    playsInline: true,\n                                    loop: true,\n                                    muted: true,\n                                    className: \"rounded-xl w-full object-cover\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                        src: banner,\n                                        type: \"video/mp4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    height: 200,\n                                    width: 200,\n                                    src: banner,\n                                    className: \"object-cover w-full aspect-square rounded-xl\",\n                                    alt: \"giveaway-image\",\n                                    loading: \"lazy\",\n                                    unoptimized: isGif\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-1 left-0 w-full flex justify-center\",\n                                    children: bannerTag\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-20 gap-2 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: children\n                        }, void 0, false),\n                        actionText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm flex items-center gap-1 text-blue-500 font-medium cursor-pointer\",\n                            onClick: onClick,\n                            children: [\n                                actionSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                                    weight: \"bold\",\n                                    size: 15\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"line-clamp-1 break-all\",\n                                    children: actionText\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    \"aria-hidden\": \"true\",\n                                    children: \"→\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative gap-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute transform-gpu -z-10 rounded-3xl w-full blur-3xl\",\n                        children: isVideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                            autoPlay: true,\n                            playsInline: true,\n                            loop: true,\n                            muted: true,\n                            className: \"rounded-xl w-full object-cover\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: banner,\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            height: 400,\n                            width: 400,\n                            src: banner || \"https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80\",\n                            className: \"object-cover w-full aspect-square rounded-xl\",\n                            alt: \"giveaway-image\",\n                            loading: \"lazy\",\n                            unoptimized: isGif\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pb-2 relative z-10\",\n                        children: [\n                            isVideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                autoPlay: true,\n                                playsInline: true,\n                                loop: true,\n                                muted: true,\n                                className: \"rounded-xl w-full object-cover\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                    src: banner,\n                                    type: \"video/mp4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                height: 400,\n                                width: 400,\n                                src: banner || \"https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80\",\n                                className: \"object-cover aspect-square w-full rounded-xl\",\n                                alt: \"giveaway-image\",\n                                loading: \"lazy\",\n                                unoptimized: isGif\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 right-2\",\n                                children: bannerTag\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-20 space-y-2\",\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"sm\",\n                            rounded: \"full\",\n                            className: \"!font-semibold mt-2\",\n                            block: true,\n                            onClick: onClick,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-1 items-center\",\n                                children: [\n                                    actionSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                                        weight: \"bold\",\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"line-clamp-1 break-all\",\n                                        children: actionText\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\GiveawaySummaryItem.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n_c = GiveawaySummaryItem;\nvar _c;\n$RefreshReg$(_c, \"GiveawaySummaryItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/GiveawaySummaryItem.tsx\n"));

/***/ }),

/***/ "./components/Giveaways/components/ShopGiveawayDetails.tsx":
/*!*****************************************************************!*\
  !*** ./components/Giveaways/components/ShopGiveawayDetails.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ShopGiveawayDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/AlertBox */ \"./components/AlertBox.tsx\");\n/* harmony import */ var _Components_Panel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/Panel */ \"./components/Panel.tsx\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Components_Tasks_components_TaskTileLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Tasks/components/TaskTileLoader */ \"./components/Tasks/components/TaskTileLoader.tsx\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @phosphor-icons/react */ \"./node_modules/@phosphor-icons/react/dist/index.es.js\");\n/* harmony import */ var _Root_helpers_giveaway__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @Root/helpers/giveaway */ \"./helpers/giveaway.ts\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ethers */ \"./node_modules/ethers/lib.esm/index.js\");\n/* harmony import */ var _GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./GiveawaySummaryItem */ \"./components/Giveaways/components/GiveawaySummaryItem.tsx\");\n/* harmony import */ var _GiveawaySummaryTags__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./GiveawaySummaryTags */ \"./components/Giveaways/components/GiveawaySummaryTags.tsx\");\n/* harmony import */ var _TokenAddress__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TokenAddress */ \"./components/Giveaways/components/TokenAddress.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction ShopGiveawayDetails(param) {\n    let { icon, token, blockchain, totalAmount, userClaimedAmount, totalAttemptedClaimable, expandable, expanded, children, onClick, summary, shopConfig, reward, userProjectPoints, claimed, giveawayClaimed, remainingLiquidity, loading = false } = param;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_9__.useTranslation)(\"translation\");\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_9__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    const hasSufficientPoints = userProjectPoints && shopConfig.points && userProjectPoints >= shopConfig.points;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_components_TaskTileLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                style: {\n                    background: \"transparent\",\n                    border: 0,\n                    padding: 0\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Panel__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        expandable: expandable,\n        expanded: expanded,\n        onClick: onClick,\n        header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawaySummaryItem__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            banner: icon || \"https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80\",\n            className: expanded ? \"!grid-cols-1 sm:!grid-cols-[170px_1fr]\" : \"\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GiveawaySummaryTags__WEBPACK_IMPORTED_MODULE_7__.GiveawaySummaryTags, {\n                        totalPoints: 0,\n                        totalXP: 0,\n                        summaries: [\n                            summary\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 11\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-lg text-ch font-semibold break-all\",\n                    children: reward\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 11\n                }, void 0),\n                token && token.address && token.address != ethers__WEBPACK_IMPORTED_MODULE_10__.ethers.constants.AddressZero ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenAddress__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    token: token,\n                    blockchain: blockchain,\n                    showBlockchain: true,\n                    className: \"mb-1\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 13\n                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2 text-sm text-ch font-medium \",\n                    children: [\n                        (shopConfig === null || shopConfig === void 0 ? void 0 : shopConfig.points) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_11__.Fire, {\n                                    size: 20,\n                                    className: \"flex-shrink-0\",\n                                    weight: \"duotone\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 17\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"break-all\",\n                                    children: \"\".concat(shopConfig.points, \" \").concat(globalT(\"projectPoints\"), \" for \").concat((0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3__.formatAmount)(shopConfig.amount, (token === null || token === void 0 ? void 0 : token.decimals) || 0), \" \").concat((0,_Root_helpers_giveaway__WEBPACK_IMPORTED_MODULE_5__.getAssetName)(token, Number.parseFloat((0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3__.formatAmount)(shopConfig.amount, (token === null || token === void 0 ? void 0 : token.decimals) || 0))))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 15\n                        }, void 0),\n                        remainingLiquidity && (totalAmount === null || totalAmount === void 0 ? void 0 : totalAmount.gt(0)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_11__.Basket, {\n                                    size: 20,\n                                    className: \"flex-shrink-0\",\n                                    weight: \"duotone\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 17\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"break-all\",\n                                    children: \"\".concat((0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3__.formatAmount)(remainingLiquidity === null || remainingLiquidity === void 0 ? void 0 : remainingLiquidity.toString(), (token === null || token === void 0 ? void 0 : token.decimals) || 0), \"/\").concat((0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3__.formatAmount)(totalAmount === null || totalAmount === void 0 ? void 0 : totalAmount.toString(), (token === null || token === void 0 ? void 0 : token.decimals) || 0), \" \").concat((0,_Root_helpers_giveaway__WEBPACK_IMPORTED_MODULE_5__.getAssetName)(token, Number.parseFloat((0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3__.formatAmount)(remainingLiquidity === null || remainingLiquidity === void 0 ? void 0 : remainingLiquidity.toString(), (token === null || token === void 0 ? void 0 : token.decimals) || 0))), \" remaining\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 17\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 15\n                        }, void 0),\n                        (totalAmount === null || totalAmount === void 0 ? void 0 : totalAmount.eq(-1)) && (giveawayClaimed === null || giveawayClaimed === void 0 ? void 0 : giveawayClaimed.gt(0)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_11__.Basket, {\n                                    size: 20,\n                                    className: \"flex-shrink-0\",\n                                    weight: \"duotone\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 17\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"break-all\",\n                                    children: \"\".concat((0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3__.formatAmount)(giveawayClaimed === null || giveawayClaimed === void 0 ? void 0 : giveawayClaimed.toString(), (token === null || token === void 0 ? void 0 : token.decimals) || 0), \" \").concat((0,_Root_helpers_giveaway__WEBPACK_IMPORTED_MODULE_5__.getAssetName)(token, Number.parseFloat((0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3__.formatAmount)(giveawayClaimed === null || giveawayClaimed === void 0 ? void 0 : giveawayClaimed.toString(), (token === null || token === void 0 ? void 0 : token.decimals) || 0))), \" claimed\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 17\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 15\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 11\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n            lineNumber: 80,\n            columnNumber: 9\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 relative z-50\",\n            children: [\n                children,\n                claimed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AlertBox__WEBPACK_IMPORTED_MODULE_1__.SuccessAlertBox, {\n                    title: \"Congrats!\",\n                    subtitle: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"You have claimed\",\n                            \" \",\n                            (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_3__.formatAmount)(userClaimedAmount === null || userClaimedAmount === void 0 ? void 0 : userClaimedAmount.toString(), (token === null || token === void 0 ? void 0 : token.decimals) || 0),\n                            \" \",\n                            \"item(s)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 11\n                }, this),\n                !claimed && !hasSufficientPoints && totalAttemptedClaimable.lte(0) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm break-words text-cs\",\n                    children: [\n                        t(\"giveaway.shopDetails.pre\"),\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-bold\",\n                            children: userProjectPoints || 0\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, this),\n                        t(\"giveaway.shopDetails.mid\", {\n                            projectPoints: globalT(\"projectPoints\")\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-bold\",\n                            children: shopConfig.points\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, this),\n                        t(\"giveaway.shopDetails.post\", {\n                            projectPoints: globalT(\"projectPoints\")\n                        })\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\ShopGiveawayDetails.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\n_s(ShopGiveawayDetails, \"OkqBUa8ZQsz6JZ9DDKpS2vbBUzs=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_9__.useTranslation,\n        next_i18next__WEBPACK_IMPORTED_MODULE_9__.useTranslation\n    ];\n});\n_c = ShopGiveawayDetails;\nvar _c;\n$RefreshReg$(_c, \"ShopGiveawayDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/ShopGiveawayDetails.tsx\n"));

/***/ }),

/***/ "./components/Giveaways/components/TokenAddress.tsx":
/*!**********************************************************!*\
  !*** ./components/Giveaways/components/TokenAddress.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TokenAddress; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm */ \"../web3-evm/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @phosphor-icons/react */ \"./node_modules/@phosphor-icons/react/dist/index.es.js\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction TokenAddress(param) {\n    let { token, blockchain, className, showBlockchain = false, addressChars = 3 } = param;\n    _s();\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    if (token.assetType === _airlyft_types__WEBPACK_IMPORTED_MODULE_2__.AssetType.NATIVE) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex gap-1.5 text-sm text-cl items-center \".concat(className || \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-medium\",\n                children: (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.shortenAddress)(token.address, addressChars)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__.Check, {\n                className: \"text-primary-foreground bg-primary rounded-full p-1\",\n                weight: \"bold\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__.Copy, {\n                className: \"cursor-pointer text-ch\",\n                size: 16,\n                onClick: (event)=>{\n                    event.stopPropagation();\n                    navigator.clipboard.writeText(token.address);\n                    setCopied(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this),\n            blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                className: \"inline-flex text-ch\",\n                target: \"_blank\",\n                href: (0,_airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.getExplorerLink)(blockchain.blockExplorerUrls, token.address, _airlyft_web3_evm__WEBPACK_IMPORTED_MODULE_1__.ExplorerDataType.TOKEN),\n                rel: \"noreferrer\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_5__.ArrowSquareOut, {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, this),\n            showBlockchain && blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                src: (blockchain === null || blockchain === void 0 ? void 0 : blockchain.icon) || \"\",\n                height: 20,\n                width: 20,\n                className: \"h-5 w-5\",\n                alt: \"blockchain-icon\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Giveaways\\\\components\\\\TokenAddress.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n_s(TokenAddress, \"NE86rL3vg4NVcTTWDavsT0hUBJs=\");\n_c = TokenAddress;\nvar _c;\n$RefreshReg$(_c, \"TokenAddress\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/components/TokenAddress.tsx\n"));

/***/ }),

/***/ "./components/Giveaways/hooks/useGetContract.ts":
/*!******************************************************!*\
  !*** ./components/Giveaways/hooks/useGetContract.ts ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ useGetContract; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query contract($blockchainId: ID!, $contractType: ContractType!) {\\n    contract(blockchainId: $blockchainId, contractType: $contractType) {\\n      address\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\n\nconst GET_CONTRACT = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject());\nfunction useGetContract(blockchainId, contractType) {\n    return (0,_apollo_client__WEBPACK_IMPORTED_MODULE_1__.useQuery)(GET_CONTRACT, {\n        variables: {\n            blockchainId: blockchainId,\n            contractType: contractType\n        },\n        skip: !blockchainId || !contractType\n    });\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0dpdmVhd2F5cy9ob29rcy91c2VHZXRDb250cmFjdC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQytDO0FBRS9DLE1BQU1FLGVBQWVGLG1EQUFHQTtBQVFULFNBQVNHLGVBQ3RCQyxZQUFnQyxFQUNoQ0MsWUFBc0M7SUFFdEMsT0FBT0osd0RBQVFBLENBQTZDQyxjQUFjO1FBQ3hFSSxXQUFXO1lBQ1RGLGNBQWNBO1lBQ2RDLGNBQWNBO1FBQ2hCO1FBQ0FFLE1BQU0sQ0FBQ0gsZ0JBQWdCLENBQUNDO0lBQzFCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9HaXZlYXdheXMvaG9va3MvdXNlR2V0Q29udHJhY3QudHM/NDJmZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDb250cmFjdCwgQ29udHJhY3RUeXBlLCBRdWVyeV9jb250cmFjdEFyZ3MgfSBmcm9tICdAYWlybHlmdC90eXBlcyc7XHJcbmltcG9ydCB7IGdxbCwgdXNlUXVlcnkgfSBmcm9tICdAYXBvbGxvL2NsaWVudCc7XHJcblxyXG5jb25zdCBHRVRfQ09OVFJBQ1QgPSBncWxgXHJcbiAgcXVlcnkgY29udHJhY3QoJGJsb2NrY2hhaW5JZDogSUQhLCAkY29udHJhY3RUeXBlOiBDb250cmFjdFR5cGUhKSB7XHJcbiAgICBjb250cmFjdChibG9ja2NoYWluSWQ6ICRibG9ja2NoYWluSWQsIGNvbnRyYWN0VHlwZTogJGNvbnRyYWN0VHlwZSkge1xyXG4gICAgICBhZGRyZXNzXHJcbiAgICB9XHJcbiAgfVxyXG5gO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlR2V0Q29udHJhY3QoXHJcbiAgYmxvY2tjaGFpbklkOiBzdHJpbmcgfCB1bmRlZmluZWQsXHJcbiAgY29udHJhY3RUeXBlOiBDb250cmFjdFR5cGUgfCB1bmRlZmluZWQsXHJcbikge1xyXG4gIHJldHVybiB1c2VRdWVyeTx7IGNvbnRyYWN0OiBDb250cmFjdCB9LCBRdWVyeV9jb250cmFjdEFyZ3M+KEdFVF9DT05UUkFDVCwge1xyXG4gICAgdmFyaWFibGVzOiB7XHJcbiAgICAgIGJsb2NrY2hhaW5JZDogYmxvY2tjaGFpbklkIGFzIHN0cmluZyxcclxuICAgICAgY29udHJhY3RUeXBlOiBjb250cmFjdFR5cGUgYXMgQ29udHJhY3RUeXBlLFxyXG4gICAgfSxcclxuICAgIHNraXA6ICFibG9ja2NoYWluSWQgfHwgIWNvbnRyYWN0VHlwZSxcclxuICB9KTtcclxufVxyXG4iXSwibmFtZXMiOlsiZ3FsIiwidXNlUXVlcnkiLCJHRVRfQ09OVFJBQ1QiLCJ1c2VHZXRDb250cmFjdCIsImJsb2NrY2hhaW5JZCIsImNvbnRyYWN0VHlwZSIsInZhcmlhYmxlcyIsInNraXAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Giveaways/hooks/useGetContract.ts\n"));

/***/ }),

/***/ "./components/Giveaways/hooks/useGetTokenWindowInfo.ts":
/*!*************************************************************!*\
  !*** ./components/Giveaways/hooks/useGetTokenWindowInfo.ts ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGetTokenWindowInfo: function() { return /* binding */ useGetTokenWindowInfo; }\n/* harmony export */ });\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @airlyft/web3-evm-hooks */ \"../web3-evm-hooks/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"./node_modules/ethers/lib.esm/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\nfunction useGetTokenWindowInfo(contractAddress, giveawayId, totalAmount, amount, capped, blockchain) {\n    const { windowsClaimed, loading: windowsLoading } = (0,_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_0__.useGetBatchWindowClaimed)(blockchain, contractAddress, [\n        giveawayId\n    ]);\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (!capped) return {\n            limitReached: false\n        };\n        const giveawayWindowLeft = totalAmount.sub((windowsClaimed === null || windowsClaimed === void 0 ? void 0 : windowsClaimed[giveawayId]) || ethers__WEBPACK_IMPORTED_MODULE_2__.BigNumber.from(0));\n        if (giveawayWindowLeft.lte(ethers__WEBPACK_IMPORTED_MODULE_2__.BigNumber.from(0))) return {\n            limitReached: true\n        };\n        if (amount.gt(giveawayWindowLeft)) return {\n            limitReached: true\n        };\n        return {\n            limitReached: false\n        };\n    }, [\n        windowsLoading,\n        totalAmount,\n        giveawayId,\n        amount\n    ]);\n    return {\n        ...stats,\n        loading: windowsLoading\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Giveaways/hooks/useGetTokenWindowInfo.ts\n"));

/***/ }),

/***/ "./components/Panel.tsx":
/*!******************************!*\
  !*** ./components/Panel.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Panel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n\n\nfunction Panel(param) {\n    let { expandable, expanded, header, children, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"rounded-xl p-4 transition\", expandable ? expanded ? \"cursor-pointer\" : \"hover:bg-foreground/10 cursor-pointer\" : \"\"),\n                onClick: ()=>{\n                    expandable && (onClick === null || onClick === void 0 ? void 0 : onClick());\n                },\n                children: header\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Panel.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"p-4\", expandable && !expanded ? \"hidden\" : \"\"),\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Panel.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_c = Panel;\nvar _c;\n$RefreshReg$(_c, \"Panel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1BhbmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF1QztBQUV4QixTQUFTQyxNQUFNLEtBWTdCO1FBWjZCLEVBQzVCQyxVQUFVLEVBQ1ZDLFFBQVEsRUFDUkMsTUFBTSxFQUNOQyxRQUFRLEVBQ1JDLE9BQU8sRUFPUixHQVo2QjtJQWE1QixxQkFDRTs7MEJBQ0UsOERBQUNDO2dCQUNDQyxXQUFXUixxREFBRUEsQ0FDViw2QkFDREUsYUFDSUMsV0FDRSxtQkFDQSwwQ0FDRjtnQkFFTkcsU0FBUztvQkFDUEosZUFBY0ksb0JBQUFBLDhCQUFBQTtnQkFDaEI7MEJBRUNGOzs7Ozs7MEJBRUgsOERBQUNHO2dCQUFJQyxXQUFXUixxREFBRUEsQ0FBRSxPQUFNRSxjQUFjLENBQUNDLFdBQVcsV0FBVzswQkFDNURFOzs7Ozs7OztBQUlUO0tBbkN3QkoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9QYW5lbC50c3g/ZTAzNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbiB9IGZyb20gJ0BSb290L3V0aWxzL3V0aWxzJztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBhbmVsKHtcclxuICBleHBhbmRhYmxlLFxyXG4gIGV4cGFuZGVkLFxyXG4gIGhlYWRlcixcclxuICBjaGlsZHJlbixcclxuICBvbkNsaWNrLFxyXG59OiB7XHJcbiAgZXhwYW5kYWJsZT86IGJvb2xlYW47XHJcbiAgZXhwYW5kZWQ/OiBib29sZWFuO1xyXG4gIGhlYWRlcjogUmVhY3QuUmVhY3ROb2RlO1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XHJcbiAgb25DbGljaz86ICgpID0+IHZvaWQ7XHJcbn0pIHtcclxuICByZXR1cm4gKFxyXG4gICAgPD5cclxuICAgICAgPGRpdlxyXG4gICAgICAgIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgICAgICBgcm91bmRlZC14bCBwLTQgdHJhbnNpdGlvbmAsXHJcbiAgICAgICAgICBleHBhbmRhYmxlXHJcbiAgICAgICAgICAgID8gZXhwYW5kZWRcclxuICAgICAgICAgICAgICA/ICdjdXJzb3ItcG9pbnRlcidcclxuICAgICAgICAgICAgICA6ICdob3ZlcjpiZy1mb3JlZ3JvdW5kLzEwIGN1cnNvci1wb2ludGVyJ1xyXG4gICAgICAgICAgICA6ICcnLFxyXG4gICAgICAgICl9XHJcbiAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgZXhwYW5kYWJsZSAmJiBvbkNsaWNrPy4oKTtcclxuICAgICAgICB9fVxyXG4gICAgICA+XHJcbiAgICAgICAge2hlYWRlcn1cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbihgcC00YCwgZXhwYW5kYWJsZSAmJiAhZXhwYW5kZWQgPyAnaGlkZGVuJyA6ICcnKX0+XHJcbiAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvPlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbImNuIiwiUGFuZWwiLCJleHBhbmRhYmxlIiwiZXhwYW5kZWQiLCJoZWFkZXIiLCJjaGlsZHJlbiIsIm9uQ2xpY2siLCJkaXYiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Panel.tsx\n"));

/***/ }),

/***/ "./components/Paragraph.tsx":
/*!**********************************!*\
  !*** ./components/Paragraph.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Paragraph = (param)=>{\n    let { icon, title, description } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-1 mb-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-2 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 w-4 text-ch\",\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-bold text-ch\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm break-words text-cs\",\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Paragraph.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Paragraph;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Paragraph);\nvar _c;\n$RefreshReg$(_c, \"Paragraph\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1BhcmFncmFwaC50c3giLCJtYXBwaW5ncyI6Ijs7OztBQUFBLE1BQU1BLFlBQVk7UUFBQyxFQUNqQkMsSUFBSSxFQUNKQyxLQUFLLEVBQ0xDLFdBQVcsRUFLWjtJQUNDLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FBbUJKOzs7Ozs7a0NBQ2xDLDhEQUFDSzt3QkFBR0QsV0FBVTtrQ0FBNkJIOzs7Ozs7Ozs7Ozs7MEJBRTdDLDhEQUFDSztnQkFBRUYsV0FBVTswQkFBK0JGOzs7Ozs7Ozs7Ozs7QUFHbEQ7S0FsQk1IO0FBb0JOLCtEQUFlQSxTQUFTQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2NvbXBvbmVudHMvUGFyYWdyYXBoLnRzeD80YjNkIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IFBhcmFncmFwaCA9ICh7XHJcbiAgaWNvbixcclxuICB0aXRsZSxcclxuICBkZXNjcmlwdGlvbixcclxufToge1xyXG4gIGljb246IFJlYWN0LlJlYWN0Tm9kZTtcclxuICB0aXRsZTogc3RyaW5nIHwgUmVhY3QuUmVhY3ROb2RlO1xyXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmcgfCBSZWFjdC5SZWFjdE5vZGU7XHJcbn0pID0+IHtcclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTEgbWItNFwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yIGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWNoXCI+e2ljb259PC9kaXY+XHJcbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1ib2xkIHRleHQtY2hcIj57dGl0bGV9PC9oMz5cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gYnJlYWstd29yZHMgdGV4dC1jc1wiPntkZXNjcmlwdGlvbn08L3A+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgUGFyYWdyYXBoO1xyXG4iXSwibmFtZXMiOlsiUGFyYWdyYXBoIiwiaWNvbiIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJkaXYiLCJjbGFzc05hbWUiLCJoMyIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Paragraph.tsx\n"));

/***/ }),

/***/ "./components/RecaptchaDeclaration.tsx":
/*!*********************************************!*\
  !*** ./components/RecaptchaDeclaration.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecaptchaDeclaration: function() { return /* binding */ RecaptchaDeclaration; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst RecaptchaDeclaration = (param)=>{\n    let { className = \"text-sm\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        className: className,\n        children: [\n            \"This site is protected by reCAPTCHA and the Google\",\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                className: \"underline text-link cursor-pointer\",\n                href: \"https://policies.google.com/privacy\",\n                children: \"Privacy Policy\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RecaptchaDeclaration.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined),\n            \" \",\n            \"and\",\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                className: \"underline text-link cursor-pointer\",\n                href: \"https://policies.google.com/terms\",\n                children: \"Terms of Service\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RecaptchaDeclaration.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined),\n            \" \",\n            \"apply.\"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\RecaptchaDeclaration.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n_c = RecaptchaDeclaration;\nvar _c;\n$RefreshReg$(_c, \"RecaptchaDeclaration\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1JlY2FwdGNoYURlY2xhcmF0aW9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFFbkIsTUFBTUMsdUJBQXVCO1FBQUMsRUFDbkNDLFlBQVksU0FBUyxFQUd0QjtJQUNDLHFCQUNFLDhEQUFDQztRQUFFRCxXQUFXQTs7WUFBVztZQUM0QjswQkFDbkQsOERBQUNFO2dCQUNDRixXQUFVO2dCQUNWRyxNQUFLOzBCQUNOOzs7Ozs7WUFFSTtZQUFJO1lBQ0w7MEJBQ0osOERBQUNEO2dCQUNDRixXQUFVO2dCQUNWRyxNQUFLOzBCQUNOOzs7Ozs7WUFFSTtZQUFJOzs7Ozs7O0FBSWYsRUFBRTtLQXhCV0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9SZWNhcHRjaGFEZWNsYXJhdGlvbi50c3g/MGYxZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5cclxuZXhwb3J0IGNvbnN0IFJlY2FwdGNoYURlY2xhcmF0aW9uID0gKHtcclxuICBjbGFzc05hbWUgPSAndGV4dC1zbScsXHJcbn06IHtcclxuICBjbGFzc05hbWU/OiBzdHJpbmc7XHJcbn0pID0+IHtcclxuICByZXR1cm4gKFxyXG4gICAgPHAgY2xhc3NOYW1lPXtjbGFzc05hbWV9PlxyXG4gICAgICBUaGlzIHNpdGUgaXMgcHJvdGVjdGVkIGJ5IHJlQ0FQVENIQSBhbmQgdGhlIEdvb2dsZXsnICd9XHJcbiAgICAgIDxhXHJcbiAgICAgICAgY2xhc3NOYW1lPVwidW5kZXJsaW5lIHRleHQtbGluayBjdXJzb3ItcG9pbnRlclwiXHJcbiAgICAgICAgaHJlZj1cImh0dHBzOi8vcG9saWNpZXMuZ29vZ2xlLmNvbS9wcml2YWN5XCJcclxuICAgICAgPlxyXG4gICAgICAgIFByaXZhY3kgUG9saWN5XHJcbiAgICAgIDwvYT57JyAnfVxyXG4gICAgICBhbmR7JyAnfVxyXG4gICAgICA8YVxyXG4gICAgICAgIGNsYXNzTmFtZT1cInVuZGVybGluZSB0ZXh0LWxpbmsgY3Vyc29yLXBvaW50ZXJcIlxyXG4gICAgICAgIGhyZWY9XCJodHRwczovL3BvbGljaWVzLmdvb2dsZS5jb20vdGVybXNcIlxyXG4gICAgICA+XHJcbiAgICAgICAgVGVybXMgb2YgU2VydmljZVxyXG4gICAgICA8L2E+eycgJ31cclxuICAgICAgYXBwbHkuXHJcbiAgICA8L3A+XHJcbiAgKTtcclxufTtcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiUmVjYXB0Y2hhRGVjbGFyYXRpb24iLCJjbGFzc05hbWUiLCJwIiwiYSIsImhyZWYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/RecaptchaDeclaration.tsx\n"));

/***/ }),

/***/ "./components/TransactionResult.tsx":
/*!******************************************!*\
  !*** ./components/TransactionResult.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransactionResult: function() { return /* binding */ TransactionResult; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _BlockExplorerLink__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BlockExplorerLink */ \"./components/BlockExplorerLink.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Loaders_SpinLoader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Loaders/SpinLoader */ \"./components/Loaders/SpinLoader.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction TransactionResult(param) {\n    let { blockchain, tx, txHash } = param;\n    _s();\n    const [broadcasting, setBroadcasting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        let mounted = true;\n        if (!tx || !setBroadcasting || !setError) return;\n        const wait = async ()=>{\n            mounted && setBroadcasting(true);\n            try {\n                await tx.wait();\n            } catch (err) {\n                mounted && setError(true);\n            }\n            mounted && setBroadcasting(false);\n        };\n        wait();\n        return ()=>{\n            mounted = false;\n        };\n    }, [\n        tx,\n        setBroadcasting,\n        setError\n    ]);\n    var _tx_hash;\n    const hash = (_tx_hash = tx === null || tx === void 0 ? void 0 : tx.hash) !== null && _tx_hash !== void 0 ? _tx_hash : txHash;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2\",\n        children: [\n            broadcasting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Loaders_SpinLoader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-bold\",\n                        children: \"Waiting for transaction to be included in the block\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: [\n                    \"Transaction failed due to \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n                lineNumber: 52,\n                columnNumber: 17\n            }, this),\n            blockchain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BlockExplorerLink__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                hash: hash,\n                blockExplorerUrls: blockchain.blockExplorerUrls\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\TransactionResult.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_s(TransactionResult, \"LHBeVubWEHDDvHeefizlWBhn1+s=\");\n_c = TransactionResult;\nvar _c;\n$RefreshReg$(_c, \"TransactionResult\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/TransactionResult.tsx\n"));

/***/ }),

/***/ "./components/Web3Wallet/Dotsama/DotsamaWallet.tsx":
/*!*********************************************************!*\
  !*** ./components/Web3Wallet/Dotsama/DotsamaWallet.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DotsamaWallet; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @airlyft/web3-evm-hooks */ \"../web3-evm-hooks/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/DropdownList/DropdownList */ \"./components/DropdownList/DropdownList.tsx\");\n/* harmony import */ var _Components_DropdownList_DropdownListItem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/DropdownList/DropdownListItem */ \"./components/DropdownList/DropdownListItem.tsx\");\n/* harmony import */ var _Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Tasks/components/AppStoreIconRenderer */ \"./components/Tasks/components/AppStoreIconRenderer.tsx\");\n/* harmony import */ var _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @Components/Toaster/Toaster */ \"./components/Toaster/Toaster.tsx\");\n/* harmony import */ var _polkadot_extension_dapp__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @polkadot/extension-dapp */ \"./node_modules/@polkadot/extension-dapp/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _Web3WalletRenderer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../Web3WalletRenderer */ \"./components/Web3Wallet/Web3WalletRenderer.tsx\");\n/* harmony import */ var _Root_context_AppContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @Root/context/AppContext */ \"./context/AppContext.tsx\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction DotsamaWallet(param) {\n    let { onError, ...props } = param;\n    var _useWalletStore, _selected_config;\n    _s();\n    const wallets = ((_useWalletStore = (0,_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__.useWalletStore)((state)=>state.walletCategory.find((category)=>category.categoryType === _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__.Web3WalletCategoryType.DOTSAMA))) === null || _useWalletStore === void 0 ? void 0 : _useWalletStore.wallets) || [];\n    var _props_excludedWallets;\n    const excludedWallets = (_props_excludedWallets = props.excludedWallets) !== null && _props_excludedWallets !== void 0 ? _props_excludedWallets : [];\n    const { state: { isWidget } } = (0,_Root_context_AppContext__WEBPACK_IMPORTED_MODULE_8__.useAppContext)();\n    if (isWidget) {\n        excludedWallets.push(_airlyft_types__WEBPACK_IMPORTED_MODULE_9__.Web3WalletType.DOTSAMA_POLKADOT_JS, _airlyft_types__WEBPACK_IMPORTED_MODULE_9__.Web3WalletType.DOTSAMA_NOVA);\n    }\n    let filteredWallets = wallets;\n    if (excludedWallets === null || excludedWallets === void 0 ? void 0 : excludedWallets.length) {\n        filteredWallets = wallets.filter((wallet)=>!(excludedWallets === null || excludedWallets === void 0 ? void 0 : excludedWallets.includes(wallet.walletType)));\n    }\n    const [selected, setSelected] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(filteredWallets === null || filteredWallets === void 0 ? void 0 : filteredWallets[0]);\n    const [injectedExtensions, setInjectedExtensions] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        if (!window || !_polkadot_extension_dapp__WEBPACK_IMPORTED_MODULE_10__.isWeb3Injected || !(filteredWallets === null || filteredWallets === void 0 ? void 0 : filteredWallets.length)) {\n            return;\n        }\n        const extensions = window.injectedWeb3;\n        setInjectedExtensions(extensions);\n    }, [\n        window,\n        _polkadot_extension_dapp__WEBPACK_IMPORTED_MODULE_10__.isWeb3Injected,\n        filteredWallets\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-5 mt-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-[90]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    data: filteredWallets,\n                    selected: selected,\n                    onChange: (item)=>setSelected(item),\n                    renderItem: (item, isButton)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_DropdownList_DropdownListItem__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            title: item.title,\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                iconKey: item.walletType,\n                                className: \"h-8 w-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 17\n                            }, void 0),\n                            selected: item.walletType === (selected === null || selected === void 0 ? void 0 : selected.walletType),\n                            button: isButton\n                        }, item.title, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            selected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Web3WalletRenderer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                walletType: selected.walletType,\n                categoryType: _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__.Web3WalletCategoryType.DOTSAMA,\n                placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                props: {\n                    wallet: selected,\n                    onError: (err)=>{\n                        (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_5__[\"default\"])({\n                            title: \"Error\",\n                            text: err,\n                            type: \"error\"\n                        });\n                        onError === null || onError === void 0 ? void 0 : onError(err);\n                    },\n                    ...props,\n                    ...((_selected_config = selected.config) === null || _selected_config === void 0 ? void 0 : _selected_config.name) ? {\n                        injectedExtension: injectedExtensions[selected.config.name]\n                    } : {},\n                    excludedWallets\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Dotsama\\\\DotsamaWallet.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s(DotsamaWallet, \"Xw7EUvcZb0YrWSTrKH8jl/lk354=\", false, function() {\n    return [\n        _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_1__.useWalletStore,\n        _Root_context_AppContext__WEBPACK_IMPORTED_MODULE_8__.useAppContext\n    ];\n});\n_c = DotsamaWallet;\nvar _c;\n$RefreshReg$(_c, \"DotsamaWallet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Web3Wallet/Dotsama/DotsamaWallet.tsx\n"));

/***/ }),

/***/ "./components/Web3Wallet/Evm/EvmWallet.tsx":
/*!*************************************************!*\
  !*** ./components/Web3Wallet/Evm/EvmWallet.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EvmWallet; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Components/DropdownList/DropdownList */ \"./components/DropdownList/DropdownList.tsx\");\n/* harmony import */ var _Components_DropdownList_DropdownListItem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Components/DropdownList/DropdownListItem */ \"./components/DropdownList/DropdownListItem.tsx\");\n/* harmony import */ var _Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Components/Tasks/components/AppStoreIconRenderer */ \"./components/Tasks/components/AppStoreIconRenderer.tsx\");\n/* harmony import */ var _Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Toaster/Toaster */ \"./components/Toaster/Toaster.tsx\");\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @airlyft/web3-evm-hooks */ \"../web3-evm-hooks/dist/index.js\");\n/* harmony import */ var _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _Web3WalletRenderer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../Web3WalletRenderer */ \"./components/Web3Wallet/Web3WalletRenderer.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction EvmWallet(param) {\n    let { onError, ...props } = param;\n    var _useWalletStore, _props_excludedWallets;\n    _s();\n    let wallets = ((_useWalletStore = (0,_airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_5__.useWalletStore)((state)=>state.walletCategory.find((category)=>category.categoryType === _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_5__.Web3WalletCategoryType.EVM))) === null || _useWalletStore === void 0 ? void 0 : _useWalletStore.wallets) || [];\n    if ((_props_excludedWallets = props.excludedWallets) === null || _props_excludedWallets === void 0 ? void 0 : _props_excludedWallets.length) {\n        wallets = wallets.filter((wallet)=>{\n            var _props_excludedWallets;\n            return !((_props_excludedWallets = props.excludedWallets) === null || _props_excludedWallets === void 0 ? void 0 : _props_excludedWallets.includes(wallet.walletType));\n        });\n    }\n    const [selected, setSelected] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(wallets === null || wallets === void 0 ? void 0 : wallets[0]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-5 mt-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_DropdownList_DropdownList__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                data: wallets,\n                selected: selected,\n                onChange: (item)=>setSelected(item),\n                renderItem: (item, isButton)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_DropdownList_DropdownListItem__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        title: item.title,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Tasks_components_AppStoreIconRenderer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            iconKey: item.walletType,\n                            className: \"h-8 w-8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Evm\\\\EvmWallet.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 15\n                        }, void 0),\n                        selected: item.walletType === (selected === null || selected === void 0 ? void 0 : selected.walletType),\n                        button: isButton\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Evm\\\\EvmWallet.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Evm\\\\EvmWallet.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            selected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Web3WalletRenderer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                walletType: selected.walletType,\n                categoryType: _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_5__.Web3WalletCategoryType.EVM,\n                placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                props: {\n                    wallet: selected,\n                    onError: (err)=>{\n                        (0,_Components_Toaster_Toaster__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n                            title: \"Error\",\n                            text: err,\n                            type: \"error\"\n                        });\n                        onError === null || onError === void 0 ? void 0 : onError(err);\n                    },\n                    ...props\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Evm\\\\EvmWallet.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Evm\\\\EvmWallet.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(EvmWallet, \"go8XrvbUzPY5Zc5iv3F7AQSG5co=\", false, function() {\n    return [\n        _airlyft_web3_evm_hooks__WEBPACK_IMPORTED_MODULE_5__.useWalletStore\n    ];\n});\n_c = EvmWallet;\nvar _c;\n$RefreshReg$(_c, \"EvmWallet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Web3Wallet/Evm/EvmWallet.tsx\n"));

/***/ }),

/***/ "./components/Web3Wallet/Web3WalletRenderer.tsx":
/*!******************************************************!*\
  !*** ./components/Web3Wallet/Web3WalletRenderer.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ WalletRenderer; },\n/* harmony export */   rendererPath: function() { return /* binding */ rendererPath; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Root/utils/string-utils */ \"./utils/string-utils.ts\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Components_Loaders_ParagraphLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Components/Loaders/ParagraphLoader */ \"./components/Loaders/ParagraphLoader.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst importProvider = (path, placeholder)=>next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__(\"./components/Web3Wallet lazy recursive ^\\\\.\\\\/.*$\")(\"./\".concat(path)), {\n        ssr: false,\n        loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Loaders_ParagraphLoader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Web3WalletRenderer.tsx\",\n                lineNumber: 20,\n                columnNumber: 20\n            }, undefined)\n    });\nconst rendererPath = (categoryType, walletType)=>{\n    const path = (0,_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_2__.capitalizeFirstLetter)((0,_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_2__.camelCased)(categoryType));\n    const fileName = (0,_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_2__.capitalizeFirstLetter)((0,_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_2__.camelCased)(walletType));\n    return \"\".concat(path, \"/\").concat(fileName);\n};\nfunction WalletRenderer(param) {\n    let { categoryType, walletType, placeholder, props } = param;\n    _s();\n    const View = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>importProvider(rendererPath(categoryType, walletType), placeholder), [\n        walletType,\n        categoryType\n    ]);\n    return View ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(View, {\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Web3WalletRenderer.tsx\",\n        lineNumber: 44,\n        columnNumber: 17\n    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Web3Wallet\\\\Web3WalletRenderer.tsx\",\n        lineNumber: 44,\n        columnNumber: 48\n    }, this);\n}\n_s(WalletRenderer, \"AuOrCRe4asKUtwi/uq401gGfadw=\");\n_c = WalletRenderer;\nvar _c;\n$RefreshReg$(_c, \"WalletRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Web3Wallet/Web3WalletRenderer.tsx\n"));

/***/ }),

/***/ "./hooks/useFindUserRewardByStatus.ts":
/*!********************************************!*\
  !*** ./hooks/useFindUserRewardByStatus.ts ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFindUserRewardByStatus: function() { return /* binding */ useFindUserRewardByStatus; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query findUserRewardByStatus($eventId: ID!, $giveawayId: ID!) {\\n    findUserRewardByStatus(eventId: $eventId, giveawayId: $giveawayId) {\\n      id\\n      giveawayId\\n      status\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\n\nconst FIND_USER_REWARD_BY_STATUS = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject());\nfunction useFindUserRewardByStatus(eventId, giveawayId) {\n    return (0,_apollo_client__WEBPACK_IMPORTED_MODULE_1__.useQuery)(FIND_USER_REWARD_BY_STATUS, {\n        variables: {\n            eventId,\n            giveawayId\n        },\n        skip: !giveawayId\n    });\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ob29rcy91c2VGaW5kVXNlclJld2FyZEJ5U3RhdHVzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDK0M7QUFFL0MsTUFBTUUsNkJBQTZCRixtREFBR0E7QUFVL0IsU0FBU0csMEJBQTBCQyxPQUFlLEVBQUVDLFVBQWtCO0lBQzNFLE9BQU9KLHdEQUFRQSxDQUdiQyw0QkFBNEI7UUFDNUJJLFdBQVc7WUFDVEY7WUFDQUM7UUFDRjtRQUNBRSxNQUFNLENBQUNGO0lBQ1Q7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ob29rcy91c2VGaW5kVXNlclJld2FyZEJ5U3RhdHVzLnRzP2NiMjkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRXZlbnRSZXdhcmQsIFF1ZXJ5X2ZpbmRVc2VyUmV3YXJkQnlTdGF0dXNBcmdzIH0gZnJvbSAnQGFpcmx5ZnQvdHlwZXMnO1xyXG5pbXBvcnQgeyBncWwsIHVzZVF1ZXJ5IH0gZnJvbSAnQGFwb2xsby9jbGllbnQnO1xyXG5cclxuY29uc3QgRklORF9VU0VSX1JFV0FSRF9CWV9TVEFUVVMgPSBncWxgXHJcbiAgcXVlcnkgZmluZFVzZXJSZXdhcmRCeVN0YXR1cygkZXZlbnRJZDogSUQhLCAkZ2l2ZWF3YXlJZDogSUQhKSB7XHJcbiAgICBmaW5kVXNlclJld2FyZEJ5U3RhdHVzKGV2ZW50SWQ6ICRldmVudElkLCBnaXZlYXdheUlkOiAkZ2l2ZWF3YXlJZCkge1xyXG4gICAgICBpZFxyXG4gICAgICBnaXZlYXdheUlkXHJcbiAgICAgIHN0YXR1c1xyXG4gICAgfVxyXG4gIH1cclxuYDtcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiB1c2VGaW5kVXNlclJld2FyZEJ5U3RhdHVzKGV2ZW50SWQ6IHN0cmluZywgZ2l2ZWF3YXlJZDogc3RyaW5nKSB7XHJcbiAgcmV0dXJuIHVzZVF1ZXJ5PFxyXG4gICAgeyBmaW5kVXNlclJld2FyZEJ5U3RhdHVzOiBFdmVudFJld2FyZFtdIH0sXHJcbiAgICBRdWVyeV9maW5kVXNlclJld2FyZEJ5U3RhdHVzQXJnc1xyXG4gID4oRklORF9VU0VSX1JFV0FSRF9CWV9TVEFUVVMsIHtcclxuICAgIHZhcmlhYmxlczoge1xyXG4gICAgICBldmVudElkLFxyXG4gICAgICBnaXZlYXdheUlkLFxyXG4gICAgfSxcclxuICAgIHNraXA6ICFnaXZlYXdheUlkLFxyXG4gIH0pO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJncWwiLCJ1c2VRdWVyeSIsIkZJTkRfVVNFUl9SRVdBUkRfQllfU1RBVFVTIiwidXNlRmluZFVzZXJSZXdhcmRCeVN0YXR1cyIsImV2ZW50SWQiLCJnaXZlYXdheUlkIiwidmFyaWFibGVzIiwic2tpcCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./hooks/useFindUserRewardByStatus.ts\n"));

/***/ }),

/***/ "./hooks/useGetBlockchain.ts":
/*!***********************************!*\
  !*** ./hooks/useGetBlockchain.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGetBlockchain: function() { return /* binding */ useGetBlockchain; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query blockchain($id: ID!) {\\n    blockchain(id: $id) {\\n      id\\n      name\\n      chainId\\n      icon\\n      blockExplorerUrls\\n      rpcUrls\\n      nativeCurrency\\n      decimals\\n      type\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\n\nconst GET_BLOCKCHAIN = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject());\nfunction useGetBlockchain(id) {\n    return (0,_apollo_client__WEBPACK_IMPORTED_MODULE_1__.useQuery)(GET_BLOCKCHAIN, {\n        variables: {\n            id\n        },\n        skip: !id\n    });\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ob29rcy91c2VHZXRCbG9ja2NoYWluLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBK0M7QUFHL0MsTUFBTUUsaUJBQWlCRixtREFBR0E7QUFnQm5CLFNBQVNHLGlCQUFpQkMsRUFBVTtJQUN6QyxPQUFPSCx3REFBUUEsQ0FDYkMsZ0JBQ0E7UUFDRUcsV0FBVztZQUNURDtRQUNGO1FBQ0FFLE1BQU0sQ0FBQ0Y7SUFDVDtBQUVKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2hvb2tzL3VzZUdldEJsb2NrY2hhaW4udHM/ZGMxYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBncWwsIHVzZVF1ZXJ5IH0gZnJvbSAnQGFwb2xsby9jbGllbnQnO1xyXG5pbXBvcnQgeyBCbG9ja2NoYWluLCBRdWVyeV9ibG9ja2NoYWluQXJncyB9IGZyb20gJ0BhaXJseWZ0L3R5cGVzJztcclxuXHJcbmNvbnN0IEdFVF9CTE9DS0NIQUlOID0gZ3FsYFxyXG4gIHF1ZXJ5IGJsb2NrY2hhaW4oJGlkOiBJRCEpIHtcclxuICAgIGJsb2NrY2hhaW4oaWQ6ICRpZCkge1xyXG4gICAgICBpZFxyXG4gICAgICBuYW1lXHJcbiAgICAgIGNoYWluSWRcclxuICAgICAgaWNvblxyXG4gICAgICBibG9ja0V4cGxvcmVyVXJsc1xyXG4gICAgICBycGNVcmxzXHJcbiAgICAgIG5hdGl2ZUN1cnJlbmN5XHJcbiAgICAgIGRlY2ltYWxzXHJcbiAgICAgIHR5cGVcclxuICAgIH1cclxuICB9XHJcbmA7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gdXNlR2V0QmxvY2tjaGFpbihpZDogc3RyaW5nKSB7XHJcbiAgcmV0dXJuIHVzZVF1ZXJ5PHsgYmxvY2tjaGFpbjogQmxvY2tjaGFpbiB9LCBRdWVyeV9ibG9ja2NoYWluQXJncz4oXHJcbiAgICBHRVRfQkxPQ0tDSEFJTixcclxuICAgIHtcclxuICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgaWQsXHJcbiAgICAgIH0sXHJcbiAgICAgIHNraXA6ICFpZCxcclxuICAgIH0sXHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiZ3FsIiwidXNlUXVlcnkiLCJHRVRfQkxPQ0tDSEFJTiIsInVzZUdldEJsb2NrY2hhaW4iLCJpZCIsInZhcmlhYmxlcyIsInNraXAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./hooks/useGetBlockchain.ts\n"));

/***/ }),

/***/ "./hooks/useGiveawayTxHash.ts":
/*!************************************!*\
  !*** ./hooks/useGiveawayTxHash.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGiveawayTxHash: function() { return /* binding */ useGiveawayTxHash; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst LS_GIVEAWAY_HASH_KEY = \"air_gth\";\nfunction useGiveawayTxHash(id) {\n    const [txHash, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n    const [initialized, setInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const handle = ()=>{\n            if (!initialized) return;\n            setLoading(true);\n            const tx = getAll();\n            setState(tx || \"\");\n            setLoading(false);\n        };\n        addEventListener(\"storage\", handle);\n        return ()=>{\n            removeEventListener(\"storage\", handle);\n        };\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        setInitialized(false);\n        setLoading(true);\n        const tx = getAll();\n        setState(tx || \"\");\n        setInitialized(true);\n        setLoading(false);\n    }, [\n        id\n    ]);\n    const getAll = ()=>{\n        try {\n            const parsedFs = JSON.parse(localStorage.getItem(LS_GIVEAWAY_HASH_KEY) || \"{}\");\n            return parsedFs[id];\n        } catch (err) {}\n    };\n    const update = (txHash)=>{\n        if (!initialized) return;\n        sync(txHash);\n    };\n    const sync = (txHash)=>{\n        try {\n            const encodedFs = JSON.stringify({\n                ...JSON.parse(localStorage.getItem(LS_GIVEAWAY_HASH_KEY) || \"{}\"),\n                [id]: txHash\n            });\n            localStorage.setItem(LS_GIVEAWAY_HASH_KEY, encodedFs);\n            dispatchEvent(new Event(\"storage\"));\n        } catch (err) {}\n    };\n    return {\n        txHash,\n        initialized,\n        loading,\n        update\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ob29rcy91c2VHaXZlYXdheVR4SGFzaC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNEM7QUFJNUMsTUFBTUUsdUJBQXVCO0FBRXRCLFNBQVNDLGtCQUFrQkMsRUFBVTtJQUMxQyxNQUFNLENBQUNDLFFBQVFDLFNBQVMsR0FBR0wsK0NBQVFBO0lBRW5DLE1BQU0sQ0FBQ00sYUFBYUMsZUFBZSxHQUFHUCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNRLFNBQVNDLFdBQVcsR0FBR1QsK0NBQVFBLENBQUM7SUFFdkNELGdEQUFTQSxDQUFDO1FBQ1IsTUFBTVcsU0FBUztZQUNiLElBQUksQ0FBQ0osYUFBYTtZQUNsQkcsV0FBVztZQUNYLE1BQU1FLEtBQUtDO1lBQ1hQLFNBQVNNLE1BQU07WUFFZkYsV0FBVztRQUNiO1FBQ0FJLGlCQUFpQixXQUFXSDtRQUU1QixPQUFPO1lBQ0xJLG9CQUFvQixXQUFXSjtRQUNqQztJQUNGO0lBRUFYLGdEQUFTQSxDQUFDO1FBQ1JRLGVBQWU7UUFDZkUsV0FBVztRQUNYLE1BQU1FLEtBQUtDO1FBQ1hQLFNBQVNNLE1BQU07UUFFZkosZUFBZTtRQUNmRSxXQUFXO0lBQ2IsR0FBRztRQUFDTjtLQUFHO0lBRVAsTUFBTVMsU0FBUztRQUNiLElBQUk7WUFDRixNQUFNRyxXQUFXQyxLQUFLQyxLQUFLLENBQ3pCQyxhQUFhQyxPQUFPLENBQUNsQix5QkFBeUI7WUFHaEQsT0FBT2MsUUFBUSxDQUFDWixHQUFHO1FBQ3JCLEVBQUUsT0FBT2lCLEtBQUssQ0FBQztJQUNqQjtJQUVBLE1BQU1DLFNBQVMsQ0FBQ2pCO1FBQ2QsSUFBSSxDQUFDRSxhQUFhO1FBRWxCZ0IsS0FBS2xCO0lBQ1A7SUFFQSxNQUFNa0IsT0FBTyxDQUFDbEI7UUFDWixJQUFJO1lBQ0YsTUFBTW1CLFlBQVlQLEtBQUtRLFNBQVMsQ0FBQztnQkFDL0IsR0FBR1IsS0FBS0MsS0FBSyxDQUFDQyxhQUFhQyxPQUFPLENBQUNsQix5QkFBeUIsS0FBSztnQkFDakUsQ0FBQ0UsR0FBRyxFQUFFQztZQUNSO1lBRUFjLGFBQWFPLE9BQU8sQ0FBQ3hCLHNCQUFzQnNCO1lBQzNDRyxjQUFjLElBQUlDLE1BQU07UUFDMUIsRUFBRSxPQUFPUCxLQUFLLENBQUM7SUFDakI7SUFFQSxPQUFPO1FBQUVoQjtRQUFRRTtRQUFhRTtRQUFTYTtJQUFPO0FBQ2hEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2hvb2tzL3VzZUdpdmVhd2F5VHhIYXNoLnRzPzJiYjAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuXHJcbmV4cG9ydCB0eXBlIEdpdmVhd2F5VHhIYXNoU3RhdGUgPSBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+O1xyXG5cclxuY29uc3QgTFNfR0lWRUFXQVlfSEFTSF9LRVkgPSAnYWlyX2d0aCc7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gdXNlR2l2ZWF3YXlUeEhhc2goaWQ6IHN0cmluZykge1xyXG4gIGNvbnN0IFt0eEhhc2gsIHNldFN0YXRlXSA9IHVzZVN0YXRlPHN0cmluZz4oKTtcclxuXHJcbiAgY29uc3QgW2luaXRpYWxpemVkLCBzZXRJbml0aWFsaXplZF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgaGFuZGxlID0gKCkgPT4ge1xyXG4gICAgICBpZiAoIWluaXRpYWxpemVkKSByZXR1cm47XHJcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XHJcbiAgICAgIGNvbnN0IHR4ID0gZ2V0QWxsKCk7XHJcbiAgICAgIHNldFN0YXRlKHR4IHx8ICcnKTtcclxuXHJcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgfTtcclxuICAgIGFkZEV2ZW50TGlzdGVuZXIoJ3N0b3JhZ2UnLCBoYW5kbGUpO1xyXG5cclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIHJlbW92ZUV2ZW50TGlzdGVuZXIoJ3N0b3JhZ2UnLCBoYW5kbGUpO1xyXG4gICAgfTtcclxuICB9KTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIHNldEluaXRpYWxpemVkKGZhbHNlKTtcclxuICAgIHNldExvYWRpbmcodHJ1ZSk7XHJcbiAgICBjb25zdCB0eCA9IGdldEFsbCgpO1xyXG4gICAgc2V0U3RhdGUodHggfHwgJycpO1xyXG5cclxuICAgIHNldEluaXRpYWxpemVkKHRydWUpO1xyXG4gICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgfSwgW2lkXSk7XHJcblxyXG4gIGNvbnN0IGdldEFsbCA9ICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHBhcnNlZEZzID0gSlNPTi5wYXJzZShcclxuICAgICAgICBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShMU19HSVZFQVdBWV9IQVNIX0tFWSkgfHwgJ3t9JyxcclxuICAgICAgKSBhcyBHaXZlYXdheVR4SGFzaFN0YXRlO1xyXG5cclxuICAgICAgcmV0dXJuIHBhcnNlZEZzW2lkXTtcclxuICAgIH0gY2F0Y2ggKGVycikge31cclxuICB9O1xyXG5cclxuICBjb25zdCB1cGRhdGUgPSAodHhIYXNoOiBzdHJpbmcpID0+IHtcclxuICAgIGlmICghaW5pdGlhbGl6ZWQpIHJldHVybjtcclxuXHJcbiAgICBzeW5jKHR4SGFzaCk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3Qgc3luYyA9ICh0eEhhc2g6IHN0cmluZykgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgZW5jb2RlZEZzID0gSlNPTi5zdHJpbmdpZnkoe1xyXG4gICAgICAgIC4uLkpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oTFNfR0lWRUFXQVlfSEFTSF9LRVkpIHx8ICd7fScpLFxyXG4gICAgICAgIFtpZF06IHR4SGFzaCxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShMU19HSVZFQVdBWV9IQVNIX0tFWSwgZW5jb2RlZEZzKTtcclxuICAgICAgZGlzcGF0Y2hFdmVudChuZXcgRXZlbnQoJ3N0b3JhZ2UnKSk7XHJcbiAgICB9IGNhdGNoIChlcnIpIHt9XHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIHsgdHhIYXNoLCBpbml0aWFsaXplZCwgbG9hZGluZywgdXBkYXRlIH07XHJcbn1cclxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiTFNfR0lWRUFXQVlfSEFTSF9LRVkiLCJ1c2VHaXZlYXdheVR4SGFzaCIsImlkIiwidHhIYXNoIiwic2V0U3RhdGUiLCJpbml0aWFsaXplZCIsInNldEluaXRpYWxpemVkIiwibG9hZGluZyIsInNldExvYWRpbmciLCJoYW5kbGUiLCJ0eCIsImdldEFsbCIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwicGFyc2VkRnMiLCJKU09OIiwicGFyc2UiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwiZXJyIiwidXBkYXRlIiwic3luYyIsImVuY29kZWRGcyIsInN0cmluZ2lmeSIsInNldEl0ZW0iLCJkaXNwYXRjaEV2ZW50IiwiRXZlbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./hooks/useGiveawayTxHash.ts\n"));

/***/ }),

/***/ "./node_modules/@polkadot/extension-dapp/bundle.js":
/*!*********************************************************!*\
  !*** ./node_modules/@polkadot/extension-dapp/bundle.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isWeb3Injected: function() { return /* binding */ isWeb3Injected; },\n/* harmony export */   packageInfo: function() { return /* reexport safe */ _packageInfo_js__WEBPACK_IMPORTED_MODULE_0__.packageInfo; },\n/* harmony export */   unwrapBytes: function() { return /* reexport safe */ _wrapBytes_js__WEBPACK_IMPORTED_MODULE_1__.unwrapBytes; },\n/* harmony export */   web3Accounts: function() { return /* binding */ web3Accounts; },\n/* harmony export */   web3AccountsSubscribe: function() { return /* binding */ web3AccountsSubscribe; },\n/* harmony export */   web3Enable: function() { return /* binding */ web3Enable; },\n/* harmony export */   web3EnablePromise: function() { return /* binding */ web3EnablePromise; },\n/* harmony export */   web3FromAddress: function() { return /* binding */ web3FromAddress; },\n/* harmony export */   web3FromSource: function() { return /* binding */ web3FromSource; },\n/* harmony export */   web3ListRpcProviders: function() { return /* binding */ web3ListRpcProviders; },\n/* harmony export */   web3UseRpcProvider: function() { return /* binding */ web3UseRpcProvider; },\n/* harmony export */   wrapBytes: function() { return /* reexport safe */ _wrapBytes_js__WEBPACK_IMPORTED_MODULE_1__.wrapBytes; }\n/* harmony export */ });\n/* harmony import */ var _polkadot_util__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @polkadot/util */ \"./node_modules/@polkadot/util/index.js\");\n/* harmony import */ var _polkadot_util_crypto__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @polkadot/util-crypto */ \"./node_modules/@polkadot/util-crypto/index.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./util.js */ \"./node_modules/@polkadot/extension-dapp/util.js\");\n/* harmony import */ var _packageInfo_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./packageInfo.js */ \"./node_modules/@polkadot/extension-dapp/packageInfo.js\");\n/* harmony import */ var _wrapBytes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./wrapBytes.js */ \"./node_modules/@polkadot/extension-dapp/wrapBytes.js\");\n\n\n\n\n\nconst win = window;\nwin.injectedWeb3 = win.injectedWeb3 || {};\nlet isWeb3Injected = web3IsInjected();\nlet web3EnablePromise = null;\n\n/** @internal true when anything has been injected and is available */\nfunction web3IsInjected() {\n    return Object\n        .values(win.injectedWeb3)\n        .filter(({ connect, enable }) => !!(connect || enable))\n        .length !== 0;\n}\n/** @internal throw a consistent error when not extensions have not been enabled */\nfunction throwError(method) {\n    throw new Error(`${method}: web3Enable(originName) needs to be called before ${method}`);\n}\n/** @internal map from Array<InjectedAccount> to Array<InjectedAccountWithMeta> */\nfunction mapAccounts(source, list, ss58Format) {\n    return list.map(({ address, genesisHash, name, type }) => ({\n        address: address.length === 42\n            ? address\n            : (0,_polkadot_util_crypto__WEBPACK_IMPORTED_MODULE_2__.encodeAddress)((0,_polkadot_util_crypto__WEBPACK_IMPORTED_MODULE_2__.decodeAddress)(address), ss58Format),\n        meta: { genesisHash, name, source },\n        type\n    }));\n}\n/** @internal filter accounts based on genesisHash and type of account */\nfunction filterAccounts(list, genesisHash, type) {\n    return list.filter((a) => (!a.type || !type || type.includes(a.type)) &&\n        (!a.genesisHash || !genesisHash || a.genesisHash === genesisHash));\n}\n/** @internal retrieves all the extensions available on the window */\nfunction getWindowExtensions(originName) {\n    return Promise\n        .all(Object\n        .entries(win.injectedWeb3)\n        .map(([nameOrHash, { connect, enable, version }]) => Promise\n        .resolve()\n        .then(() => connect\n        // new style, returning all info\n        ? connect(originName)\n        : enable\n            // previous interface, leakages on name/version\n            ? enable(originName).then((e) => (0,_polkadot_util__WEBPACK_IMPORTED_MODULE_3__.objectSpread)({ name: nameOrHash, version: version || 'unknown' }, e))\n            : Promise.reject(new Error('No connect(..) or enable(...) hook found')))\n        .catch(({ message }) => {\n        console.error(`Error initializing ${nameOrHash}: ${message}`);\n    })))\n        .then((exts) => exts.filter((e) => !!e));\n}\n/** @internal Ensure the enable promise is resolved and filter by extensions */\nasync function filterEnable(caller, extensions) {\n    if (!web3EnablePromise) {\n        return throwError(caller);\n    }\n    const sources = await web3EnablePromise;\n    return sources.filter(({ name }) => !extensions ||\n        extensions.includes(name));\n}\n/**\n * @summary Enables all the providers found on the injected window interface\n * @description\n * Enables all injected extensions that has been found on the page. This\n * should be called before making use of any other web3* functions.\n */\nfunction web3Enable(originName, compatInits = []) {\n    if (!originName) {\n        throw new Error('You must pass a name for your app to the web3Enable function');\n    }\n    const initCompat = compatInits.length\n        ? Promise.all(compatInits.map((c) => c().catch(() => false)))\n        : Promise.resolve([true]);\n    web3EnablePromise = (0,_util_js__WEBPACK_IMPORTED_MODULE_4__.documentReadyPromise)(() => initCompat.then(() => getWindowExtensions(originName)\n        .then((values) => values.map((e) => {\n        // if we don't have an accounts subscriber, add a single-shot version\n        if (!e.accounts.subscribe) {\n            e.accounts.subscribe = (cb) => {\n                e.accounts\n                    .get()\n                    .then(cb)\n                    .catch(console.error);\n                return () => {\n                    // no ubsubscribe needed, this is a single-shot\n                };\n            };\n        }\n        return e;\n    }))\n        .catch(() => [])\n        .then((values) => {\n        const names = values.map(({ name, version }) => `${name}/${version}`);\n        isWeb3Injected = web3IsInjected();\n        console.info(`web3Enable: Enabled ${values.length} extension${values.length !== 1 ? 's' : ''}: ${names.join(', ')}`);\n        return values;\n    })));\n    return web3EnablePromise;\n}\n/**\n * @summary Retrieves all the accounts across all providers\n * @description\n * This returns the full list of account available (across all extensions) to\n * the page. Filtering options are available of a per-extension, per type and\n * per-genesisHash basis. Optionally the accounts can be encoded with the provided\n * ss58Format\n */\nasync function web3Accounts({ accountType, extensions, genesisHash, ss58Format } = {}) {\n    const accounts = [];\n    const sources = await filterEnable('web3Accounts', extensions);\n    const retrieved = await Promise.all(sources.map(async ({ accounts, name: source }) => {\n        try {\n            const list = await accounts.get();\n            return mapAccounts(source, filterAccounts(list, genesisHash, accountType), ss58Format);\n        }\n        catch {\n            // cannot handle this one\n            return [];\n        }\n    }));\n    retrieved.forEach((result) => {\n        accounts.push(...result);\n    });\n    console.info(`web3Accounts: Found ${accounts.length} address${accounts.length !== 1 ? 'es' : ''}`);\n    return accounts;\n}\n/**\n * @summary Subscribes to all the accounts across all providers\n * @description\n * This is the subscription version of the web3Accounts interface with\n * updates as to when new accounts do become available. The list of filtering\n * options are the same as for the web3Accounts interface.\n */\nasync function web3AccountsSubscribe(cb, { accountType, extensions, genesisHash, ss58Format } = {}) {\n    const sources = await filterEnable('web3AccountsSubscribe', extensions);\n    const accounts = {};\n    const triggerUpdate = () => cb(Object\n        .entries(accounts)\n        .reduce((result, [source, list]) => {\n        result.push(...mapAccounts(source, filterAccounts(list, genesisHash, accountType), ss58Format));\n        return result;\n    }, []));\n    const unsubs = sources.map(({ accounts: { subscribe }, name: source }) => subscribe((result) => {\n        accounts[source] = result;\n        try {\n            const result = triggerUpdate();\n            if (result && (0,_polkadot_util__WEBPACK_IMPORTED_MODULE_3__.isPromise)(result)) {\n                result.catch(console.error);\n            }\n        }\n        catch (error) {\n            console.error(error);\n        }\n    }));\n    return () => {\n        unsubs.forEach((unsub) => {\n            unsub();\n        });\n    };\n}\n/**\n * @summary Finds a specific provider based on the name\n * @description\n * This retrieves a specific source (extension) based on the name. In most\n * cases it should not be needed to call it directly (e.g. it is used internally\n * by calls such as web3FromAddress) but would allow operation on a specific\n * known extension.\n */\nasync function web3FromSource(source) {\n    if (!web3EnablePromise) {\n        return throwError('web3FromSource');\n    }\n    const sources = await web3EnablePromise;\n    const found = source && sources.find(({ name }) => name === source);\n    if (!found) {\n        throw new Error(`web3FromSource: Unable to find an injected ${source}`);\n    }\n    return found;\n}\n/**\n * @summary Find a specific provider that provides a specific address\n * @description\n * Based on an address, return the provider that has makes this address\n * available to the page.\n */\nasync function web3FromAddress(address) {\n    if (!web3EnablePromise) {\n        return throwError('web3FromAddress');\n    }\n    const accounts = await web3Accounts();\n    let found;\n    if (address) {\n        const accountU8a = (0,_polkadot_util_crypto__WEBPACK_IMPORTED_MODULE_2__.decodeAddress)(address);\n        found = accounts.find((account) => (0,_polkadot_util__WEBPACK_IMPORTED_MODULE_3__.u8aEq)((0,_polkadot_util_crypto__WEBPACK_IMPORTED_MODULE_2__.decodeAddress)(account.address), accountU8a));\n    }\n    if (!found) {\n        throw new Error(`web3FromAddress: Unable to find injected ${address}`);\n    }\n    return web3FromSource(found.meta.source);\n}\n/**\n * @summary List all providers exposed by one source\n * @description\n * For extensions that supply RPC providers, this call would return the list\n * of RPC providers that any extension may supply.\n */\nasync function web3ListRpcProviders(source) {\n    const { provider } = await web3FromSource(source);\n    if (!provider) {\n        console.warn(`Extension ${source} does not expose any provider`);\n        return null;\n    }\n    return provider.listProviders();\n}\n/**\n * @summary Start an RPC provider provider by a specific source\n * @description\n * For extensions that supply RPC providers, this call would return an\n * enabled provider (initialized with the specific key) from the\n * specified extension source.\n */\nasync function web3UseRpcProvider(source, key) {\n    const { provider } = await web3FromSource(source);\n    if (!provider) {\n        throw new Error(`Extension ${source} does not expose any provider`);\n    }\n    const meta = await provider.startProvider(key);\n    return { meta, provider };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@polkadot/extension-dapp/bundle.js\n"));

/***/ }),

/***/ "./node_modules/@polkadot/extension-dapp/index.js":
/*!********************************************************!*\
  !*** ./node_modules/@polkadot/extension-dapp/index.js ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _bundle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bundle.js */ \"./node_modules/@polkadot/extension-dapp/bundle.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _bundle_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _bundle_js__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHBvbGthZG90L2V4dGVuc2lvbi1kYXBwL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTRCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AcG9sa2Fkb3QvZXh0ZW5zaW9uLWRhcHAvaW5kZXguanM/MDQxOCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL2J1bmRsZS5qcyc7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/@polkadot/extension-dapp/index.js\n"));

/***/ }),

/***/ "./node_modules/@polkadot/extension-dapp/packageInfo.js":
/*!**************************************************************!*\
  !*** ./node_modules/@polkadot/extension-dapp/packageInfo.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   packageInfo: function() { return /* binding */ packageInfo; }\n/* harmony export */ });\nconst packageInfo = { name: '@polkadot/extension-dapp', path: ( true) ? new URL(\"file:///C:/Users/<USER>/OneDrive/Desktop/web%20development/airlfyt/airlyft-monorepo/packages/public-ui/node_modules/@polkadot/extension-dapp/packageInfo.js\").pathname.substring(0, new URL(\"file:///C:/Users/<USER>/OneDrive/Desktop/web%20development/airlfyt/airlyft-monorepo/packages/public-ui/node_modules/@polkadot/extension-dapp/packageInfo.js\").pathname.lastIndexOf('/') + 1) : 0, type: 'esm', version: '0.47.5' };\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHBvbGthZG90L2V4dGVuc2lvbi1kYXBwL3BhY2thZ2VJbmZvLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxzQkFBc0IseUNBQXlDLEtBQThCLFlBQVksNEpBQWUsZ0NBQWdDLDRKQUFlLG1DQUFtQyxDQUFNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AcG9sa2Fkb3QvZXh0ZW5zaW9uLWRhcHAvcGFja2FnZUluZm8uanM/NTc0MCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgcGFja2FnZUluZm8gPSB7IG5hbWU6ICdAcG9sa2Fkb3QvZXh0ZW5zaW9uLWRhcHAnLCBwYXRoOiAoaW1wb3J0Lm1ldGEgJiYgaW1wb3J0Lm1ldGEudXJsKSA/IG5ldyBVUkwoaW1wb3J0Lm1ldGEudXJsKS5wYXRobmFtZS5zdWJzdHJpbmcoMCwgbmV3IFVSTChpbXBvcnQubWV0YS51cmwpLnBhdGhuYW1lLmxhc3RJbmRleE9mKCcvJykgKyAxKSA6ICdhdXRvJywgdHlwZTogJ2VzbScsIHZlcnNpb246ICcwLjQ3LjUnIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/@polkadot/extension-dapp/packageInfo.js\n"));

/***/ }),

/***/ "./node_modules/@polkadot/extension-dapp/util.js":
/*!*******************************************************!*\
  !*** ./node_modules/@polkadot/extension-dapp/util.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   documentReadyPromise: function() { return /* binding */ documentReadyPromise; }\n/* harmony export */ });\nfunction documentReadyPromise(creator) {\n    return new Promise((resolve) => {\n        if (document.readyState === 'complete') {\n            resolve(creator());\n        }\n        else {\n            window.addEventListener('load', () => resolve(creator()));\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHBvbGthZG90L2V4dGVuc2lvbi1kYXBwL3V0aWwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0Bwb2xrYWRvdC9leHRlbnNpb24tZGFwcC91dGlsLmpzPzdkMWIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGRvY3VtZW50UmVhZHlQcm9taXNlKGNyZWF0b3IpIHtcbiAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHtcbiAgICAgICAgaWYgKGRvY3VtZW50LnJlYWR5U3RhdGUgPT09ICdjb21wbGV0ZScpIHtcbiAgICAgICAgICAgIHJlc29sdmUoY3JlYXRvcigpKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdsb2FkJywgKCkgPT4gcmVzb2x2ZShjcmVhdG9yKCkpKTtcbiAgICAgICAgfVxuICAgIH0pO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/@polkadot/extension-dapp/util.js\n"));

/***/ }),

/***/ "./node_modules/@polkadot/extension-dapp/wrapBytes.js":
/*!************************************************************!*\
  !*** ./node_modules/@polkadot/extension-dapp/wrapBytes.js ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ETHEREUM: function() { return /* binding */ ETHEREUM; },\n/* harmony export */   POSTFIX: function() { return /* binding */ POSTFIX; },\n/* harmony export */   PREFIX: function() { return /* binding */ PREFIX; },\n/* harmony export */   isWrapped: function() { return /* binding */ isWrapped; },\n/* harmony export */   unwrapBytes: function() { return /* binding */ unwrapBytes; },\n/* harmony export */   wrapBytes: function() { return /* binding */ wrapBytes; }\n/* harmony export */ });\n/* harmony import */ var _polkadot_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @polkadot/util */ \"./node_modules/@polkadot/util/index.js\");\n\nconst ETHEREUM = _polkadot_util__WEBPACK_IMPORTED_MODULE_0__.U8A_WRAP_ETHEREUM;\nconst POSTFIX = _polkadot_util__WEBPACK_IMPORTED_MODULE_0__.U8A_WRAP_POSTFIX;\nconst PREFIX = _polkadot_util__WEBPACK_IMPORTED_MODULE_0__.U8A_WRAP_PREFIX;\nconst isWrapped = _polkadot_util__WEBPACK_IMPORTED_MODULE_0__.u8aIsWrapped;\nconst unwrapBytes = _polkadot_util__WEBPACK_IMPORTED_MODULE_0__.u8aUnwrapBytes;\nconst wrapBytes = _polkadot_util__WEBPACK_IMPORTED_MODULE_0__.u8aWrapBytes;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHBvbGthZG90L2V4dGVuc2lvbi1kYXBwL3dyYXBCeXRlcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQWtJO0FBQzNILGlCQUFpQiw2REFBaUI7QUFDbEMsZ0JBQWdCLDREQUFnQjtBQUNoQyxlQUFlLDJEQUFlO0FBQzlCLGtCQUFrQix3REFBWTtBQUM5QixvQkFBb0IsMERBQWM7QUFDbEMsa0JBQWtCLHdEQUFZIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AcG9sa2Fkb3QvZXh0ZW5zaW9uLWRhcHAvd3JhcEJ5dGVzLmpzP2VkMmEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVThBX1dSQVBfRVRIRVJFVU0sIFU4QV9XUkFQX1BPU1RGSVgsIFU4QV9XUkFQX1BSRUZJWCwgdThhSXNXcmFwcGVkLCB1OGFVbndyYXBCeXRlcywgdThhV3JhcEJ5dGVzIH0gZnJvbSAnQHBvbGthZG90L3V0aWwnO1xuZXhwb3J0IGNvbnN0IEVUSEVSRVVNID0gVThBX1dSQVBfRVRIRVJFVU07XG5leHBvcnQgY29uc3QgUE9TVEZJWCA9IFU4QV9XUkFQX1BPU1RGSVg7XG5leHBvcnQgY29uc3QgUFJFRklYID0gVThBX1dSQVBfUFJFRklYO1xuZXhwb3J0IGNvbnN0IGlzV3JhcHBlZCA9IHU4YUlzV3JhcHBlZDtcbmV4cG9ydCBjb25zdCB1bndyYXBCeXRlcyA9IHU4YVVud3JhcEJ5dGVzO1xuZXhwb3J0IGNvbnN0IHdyYXBCeXRlcyA9IHU4YVdyYXBCeXRlcztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/@polkadot/extension-dapp/wrapBytes.js\n"));

/***/ })

}]);